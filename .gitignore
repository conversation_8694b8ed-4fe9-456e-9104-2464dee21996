# LuminariMUD .gitignore
# Based on C/C++ gitignore from https://github.com/github/gitignore/blob/master/
# Maintained by <PERSON>us<PERSON>

####################
# Build Artifacts
####################

# Prerequisites and dependencies
*.d
depend
*/depend

# Object files
*.o
*.ko
*.elf
*.lo
*.slo

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lai

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Linker output
*.ilk
*.map
*.exp

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Fortran module files
*.mod
*.smod


####################
# IDE and Editor Files
####################
.vscode/
.idea/
.cursor/
.editorconfig
*.swp
*.swo
*~
*.code-workspace

####################
# AI Assistant Files
####################
# AI development guide and configuration
CLAUDE.md
.claude/
.cursorrules
.cursorignore

####################
# Backup Files
####################
*.bak
*.old
bak/

####################
# Configure-Generated Files
####################
# These are generated by running ./configure
# Makefile - commented out as this project tracks Makefile in git
# util/Makefile
src/conf.h
config.status
config.cache
config.log

# Autoconf/automake generated files
autom4te.cache/
cnf/autom4te.cache/
cnf/configure
cnf/conf.h.in
configure
conf.h.in
.deps/
*.Po
*.Tpo
Makefile.in
aclocal.m4
missing
install-sh
depcomp
compile
.dirstamp
stamp-h1
# Temporary configure directories
confor*/

# Build dependencies
src/.depend
.depend

####################
# MUD Configuration Files
####################
# These contain sensitive or environment-specific settings
campaign.h
mysql_config
vnums.h
mud_options.h

####################
# Environment Files
####################
.env
.env.local
.env.production
.env.staging

####################
# Log Files
####################
*.log
log/*
!log/.gitkeep
lib/logs/log/*
!lib/logs/log/.gitkeep
syslog
syslog.*
syslog.CRASH

####################
# Debug Files
####################
gdb.txt
log/gdb.txt
.gdbcommands
.gdbinit_mud
core
core.*
vgcore.*
cores/
*.core.*


####################
# MUD Runtime Data
####################

# MRTG stats file (runtime statistics)
lib/mrtg/stats

# Autorun control files (should never be committed)
.killscript
.fastboot
pause
.websocket_policy.pid
.flash_policy.pid
.autorun.lock

# Copyover/hotboot files (should never be committed)
lib/copyover.dat



####################
# World Files (OLC)
####################


####################
# Text Files (In-Game Edited)
####################


####################
# Unit Tests
####################
/unittests/obj
/unittests/CuTest/AllTests.c

####################
# Keep Directory Structure
####################


# Outcast MUD executables
ocm
ocm_new
/ocm
/ocm_new
src/ocm
src/ocm_new

# Ignore tar and gz files in root
/*.tar
/*.gz
/*.tar.gz
