# Luminari MUD - Complete Artifact Equipment System Reference

## Quick File Ref

**Header Files (.h):**
- src/config.h # Defines ARTIFACT feature flag and memory indexing
- src/db.h # Declares ARTIFACT_FILE path for data storage
- src/prototypes.h # Function prototypes for all artifact functions
- src/specs.include.h # Defines IDX_ARTIFACT flag and related macros
- src/specs.prototypes.h # Prototypes for artifact special procedures
- src/structs.h # Contains struct artifact_data definition

**Source Files (.c):**
- src/actwiz.c # Wizard commands for artifact management and inspection
- src/db.c # Artifact initialization, globals (art_f, totalArtifacts, art_index)
- src/debug.c # Memory accounting for artifacts (MEM_ARTIFACT)
- src/files.c # Save/load handling to preserve artifact ownership
- src/handler.c # Core artifact functions (artifactToChar, searchForArtifact, etc.)
- src/olcdb.c # OLC system integration for artifact editing
- src/specs.artifacts.c # Main artifact implementations and special abilities
- src/specs.assign.c # Assigns artifact special procedures to objects
- src/specs.retired.c # Historical/retired artifact implementations
- src/specs.undermountain.c # Contains artifact-related dialogue/references

**Data Files:**
- areas/world.artifact # Stores artifact ownership and tracking data

## Overview

The artifact equipment system in Luminari MUD is a comprehensive feature that manages unique, powerful items with special ownership tracking and abilities. The system is implemented as a compile-time optional feature controlled by the `#ifdef ARTIFACT` preprocessor directive.

## Key System Features

- **Unique Ownership Tracking**: Each artifact can only be owned by one character at a time
- **Persistent Storage**: Artifact ownership is saved to the `areas/world.artifact` file
- **Transfer Logging**: All artifact ownership changes are logged
- **Nested Artifact Handling**: Support for artifacts contained within other objects
- **Database Integration**: Full integration with the MUD's object system via `IDX_ARTIFACT` flag
- **Special Abilities**: Individual artifacts have unique commands and abilities

## File Structure and Organization

### Core Implementation Files

#### 1. **Primary System Files**
- **`src/specs.artifacts.c`**
  - Main artifact specifications and implementations
  - Contains specific artifact behaviors and special procedures
  - Implements individual artifacts like:
    - Trorxek (Staff of Ancient Oaks)
    - Amaukekel (Rod of Light)
  - Defines artifact-specific commands and abilities

- **`src/handler.c`**
  - Core artifact handling functions:
    - `artifactToChar()` - Assigns artifact to character
    - `artifactFromChar()` - Removes artifact from character
    - `simpleArtifactFromChar()` - Simplified artifact removal
    - `tagNestedArtifacts()` - Handles artifacts in containers
    - `getNestedArtifacts()` - Retrieves nested artifacts
    - `dropNestedArtifacts()` - Drops nested artifacts
    - `artifactIsOwned()` - Checks artifact ownership
    - `searchForArtifact()` - Searches for specific artifacts
    - `tagBogusArtifact()` - Marks invalid artifacts

- **`src/db.c`**
  - Artifact database initialization and management
  - Contains global variables:
    - `art_f` - File pointer for artifact data
    - `totalArtifacts` - Counter for total artifacts
    - `art_index` - Array indexing artifacts
  - Key functions:
    - `initializeArtifacts()` - Initializes artifact system at boot
    - `uniqueArtifact()` - Ensures artifact uniqueness
    - `destroyArtifactList()` - Cleanup function

#### 2. **Header and Definition Files**
- **`src/structs.h`**
  - Contains `struct artifact_data` definition (lines 805-810)
  - Defines artifact-related structure declarations

- **`src/prototypes.h`**
  - Function prototypes for all artifact-related functions (lines 1283-1293)
  - Declares artifact helper functions

- **`src/config.h`**
  - Defines `ARTIFACT` feature flag
  - Memory indexing configuration for artifacts

- **`src/specs.include.h`**
  - Defines `IDX_ARTIFACT` flag and related macros

- **`src/specs.prototypes.h`**
  - Contains prototypes for artifact-related special procedures
  - References to `specs.artifact.c`

- **`src/db.h`**
  - Declares `ARTIFACT_FILE` macro pointing to "areas/world.artifact"
  - Database-related artifact definitions

### Command and Interface Files

- **`src/actwiz.c`**
  - Wizard/admin commands for artifact management
  - Artifact inspection and debugging commands
  - Owner checks and bogus artifact tagging

- **`src/specs.assign.c`**
  - Contains artifact assignments
  - Special procedure hooks (e.g., Doombringer)
  - Links artifacts to their special behaviors

### Support and Integration Files

- **`src/files.c`**
  - Save/load paths for artifact persistence
  - Ensures artifacts aren't removed during temporary saves

- **`src/olcdb.c`**
  - Online Creation (OLC) system integration
  - External declarations and database hooks
  - References to artifact arrays

- **`src/debug.c`**
  - Memory accounting includes `MEM_ARTIFACT` when enabled
  - Debugging support for artifact system

### Legacy and Reference Files

- **`src/specs.retired.c`**
  - Contains historical/retired artifact implementations
  - Comments about old/removed artifacts

- **`src/specs.undermountain.c`**
  - Contains minor artifact-related dialogue/content
  - Contextual references to artifacts

### Data Files

- **`areas/world.artifact`**
  - Primary artifact data file
  - Contains:
    - Artifact ownership tracking
    - List of all artifacts with VNUMs and current owners
    - Documentation for each artifact (Kelrarin's Hammer, Tiamat's Stinger, etc.)


## Critical Files for Understanding the System

The three most important files for understanding the artifact system are:

1. **`src/specs.artifacts.c`** - Contains the actual artifact implementations
2. **`src/handler.c`** - Core functionality for artifact management
3. **`src/structs.h`** - Data structures that define how artifacts work

## System Architecture

The artifact system integrates deeply with Luminari's object and character systems:

1. **Initialization**: System loads at boot time via `initializeArtifacts()`
2. **Ownership Management**: Tracks who owns each artifact through the artifact file
3. **Transfer Handling**: Monitors all equipment changes to update ownership
4. **Persistence**: Saves state to disk to maintain ownership across reboots
5. **Special Abilities**: Each artifact can have unique commands and behaviors

## Development Notes

- The system uses conditional compilation (`#ifdef ARTIFACT`) allowing it to be enabled/disabled at compile time
- All artifact transfers are logged for administrative oversight
- The system prevents duplication of unique artifacts
- Nested artifact support allows artifacts to be stored in containers while maintaining ownership tracking

===================

# [IDEAS & THOUGHTS] MUD Artifact System Implementation Guide

## 1. System Overview

The artifact system provides unique, powerful equipment with single-instance enforcement, progression mechanics, and persistent ownership tracking for MUD environments.

### Core Features
- **Uniqueness**: Only one instance per artifact exists in the world
- **Persistence**: Ownership tracked across server restarts with full history
- **Progression**: Artifacts gain experience and unlock abilities through use
- **Binding**: Transfer restrictions (BoP/BoE/BoA) for economy control
- **Dynamic**: Custom abilities, procs, and stat modifications

### Architecture
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Character  │◄──►│   Object    │◄──►│ Persistence │
│   System    │    │   System    │    │    Layer    │
└─────────────┘    └─────────────┘    └─────────────┘
         ▲                ▲                    ▲
         └────────────────┼────────────────────┘
                    ┌─────────────┐
                    │  Artifact   │
                    │   System    │
                    └─────────────┘
```

## 2. Data Model

### 2.1 SQL Schema
```sql
CREATE TABLE artifact_templates (
    vnum INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    short_desc VARCHAR(255),
    item_type INTEGER NOT NULL,
    wear_flags INTEGER DEFAULT 0,
    rarity_tier INTEGER DEFAULT 1,
    min_level INTEGER DEFAULT 1,
    binding_type INTEGER DEFAULT 0, -- 0=None, 1=BoP, 2=BoE, 3=BoA
    power_budget INTEGER DEFAULT 100,
    special_proc VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE artifact_instances (
    instance_id VARCHAR(36) PRIMARY KEY, -- UUID
    vnum INTEGER NOT NULL REFERENCES artifact_templates(vnum),
    current_owner_id INTEGER,
    location_type INTEGER, -- 0=inventory, 1=equipped, 2=room
    condition_points INTEGER DEFAULT 100,
    attunement_level INTEGER DEFAULT 0,
    experience_points INTEGER DEFAULT 0,
    is_identified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(vnum) WHERE current_owner_id IS NOT NULL
);

CREATE TABLE artifact_ownership_log (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id VARCHAR(36) NOT NULL,
    from_owner_id INTEGER,
    to_owner_id INTEGER,
    transfer_type INTEGER, -- 0=drop, 1=give, 2=trade, 3=death
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_instances_owner ON artifact_instances(current_owner_id);
CREATE INDEX idx_instances_vnum ON artifact_instances(vnum);
CREATE INDEX idx_ownership_log ON artifact_ownership_log(instance_id, timestamp);
```

### 2.2 C Data Structures
```c
// artifacts.h
typedef struct artifact_data {
    char instance_id[37];        // UUID string
    int vnum;                    // Template reference
    int owner_id;                // Current owner character ID
    char *owner_name;            // Cached for performance
    time_t acquired_time;        // Ownership timestamp
    int level;                   // Progression level (1-10)
    int experience;              // Current XP
    int condition;               // Durability (0-100)
    struct artifact_data *next;  // Global list
} ARTIFACT_DATA;

typedef struct artifact_ability {
    char *name;                  // Ability identifier
    char *command;               // Player command
    int cooldown;                // Cooldown seconds
    int last_used;               // Last activation
    int mana_cost;               // Resource cost
    void (*execute)(CHAR_DATA *ch, OBJ_DATA *obj, char *arg);
    struct artifact_ability *next;
} ARTIFACT_ABILITY;

typedef struct artifact_stats {
    int modifiers[MAX_STATS];    // Flat bonuses
    float multipliers[MAX_STATS]; // Percentage modifiers
    int resistances[MAX_DAMAGE_TYPES];
    int proc_chance;             // Special effect chance
} ARTIFACT_STATS;

typedef enum {
    BIND_NONE = 0,
    BIND_ON_PICKUP = 1,
    BIND_ON_EQUIP = 2,
    BIND_ON_ACCOUNT = 3
} binding_type_t;
```

## 3. State Machine

```c
typedef enum {
    ARTIFACT_STATE_TEMPLATE,     // Exists only in database
    ARTIFACT_STATE_SPAWNED,      // In world, unowned
    ARTIFACT_STATE_OWNED,        // Has owner
    ARTIFACT_STATE_IDENTIFIED,   // Properties revealed
    ARTIFACT_STATE_ATTUNED,      // Fully bonded
    ARTIFACT_STATE_BROKEN,       // Needs repair
    ARTIFACT_STATE_DORMANT       // Awaiting respawn
} artifact_state_t;

// State transitions
bool is_valid_transition(artifact_state_t from, artifact_state_t to) {
    switch (from) {
        case ARTIFACT_STATE_TEMPLATE:
            return to == ARTIFACT_STATE_SPAWNED;
        case ARTIFACT_STATE_SPAWNED:
            return to == ARTIFACT_STATE_OWNED;
        case ARTIFACT_STATE_OWNED:
            return to == ARTIFACT_STATE_IDENTIFIED || 
                   to == ARTIFACT_STATE_SPAWNED;
        case ARTIFACT_STATE_IDENTIFIED:
            return to == ARTIFACT_STATE_ATTUNED ||
                   to == ARTIFACT_STATE_BROKEN;
        default:
            return false;
    }
}
```

## 4. Core Implementation

### 4.1 Creation and Binding
```c
// Generate UUID for new instance
void generate_artifact_uuid(char* buffer, int vnum, uint64_t timestamp) {
    snprintf(buffer, 37, "%08x-%04x-%04x-%04x-%012lx",
             vnum,
             (uint16_t)mud_server_id,
             (uint16_t)(timestamp >> 32),
             (uint16_t)(timestamp >> 16),
             timestamp & 0xFFFFFFFFFFFF);
}

ARTIFACT_DATA *create_artifact(int vnum, CHAR_DATA *owner) {
    // Enforce uniqueness
    if (find_artifact_by_vnum(vnum)) {
        log("ERROR: Duplicate artifact creation attempt: %d", vnum);
        return NULL;
    }
    
    ARTIFACT_DATA *artifact = calloc(1, sizeof(ARTIFACT_DATA));
    generate_artifact_uuid(artifact->instance_id, vnum, time(NULL));
    artifact->vnum = vnum;
    artifact->owner_id = owner ? GET_IDNUM(owner) : 0;
    artifact->owner_name = owner ? strdup(GET_NAME(owner)) : NULL;
    artifact->acquired_time = time(0);
    artifact->level = 1;
    artifact->experience = 0;
    artifact->condition = 100;
    
    // Add to global tracking
    artifact->next = artifact_list;
    artifact_list = artifact;
    
    // Log creation
    log_artifact_event(artifact, owner, "CREATED");
    save_artifacts();
    
    return artifact;
}

bool bind_artifact_to_char(CHAR_DATA *ch, OBJ_DATA *obj) {
    if (!IS_ARTIFACT(obj)) return false;
    
    ARTIFACT_DATA *artifact = find_artifact_by_vnum(GET_OBJ_VNUM(obj));
    ARTIFACT_TEMPLATE *template = get_artifact_template(GET_OBJ_VNUM(obj));
    
    // Check binding restrictions
    if (artifact && artifact->owner_id != 0) {
        if (template->binding_type == BIND_ON_PICKUP) {
            send_to_char("This artifact is soulbound to another!\r\n", ch);
            return false;
        }
        if (template->binding_type == BIND_ON_ACCOUNT &&
            get_account_id(artifact->owner_id) != get_account_id(GET_IDNUM(ch))) {
            send_to_char("This artifact is bound to another account!\r\n", ch);
            return false;
        }
    }
    
    // Create or update artifact
    if (!artifact) {
        artifact = create_artifact(GET_OBJ_VNUM(obj), ch);
    } else {
        // Transfer ownership
        log_artifact_transfer(artifact, artifact->owner_id, GET_IDNUM(ch), "BIND");
        artifact->owner_id = GET_IDNUM(ch);
        free(artifact->owner_name);
        artifact->owner_name = strdup(GET_NAME(ch));
        artifact->acquired_time = time(0);
    }
    
    // Apply effects
    apply_artifact_bonuses(ch, obj);
    
    // Trigger binding proc
    if (obj->artifact_proc) {
        obj->artifact_proc(ch, obj, NULL, ARTIFACT_BIND);
    }
    
    save_artifacts();
    return true;
}
```

### 4.2 Stat System
```c
void apply_artifact_bonuses(CHAR_DATA *ch, OBJ_DATA *obj) {
    ARTIFACT_STATS *stats = get_artifact_stats(obj);
    ARTIFACT_DATA *artifact = find_artifact_by_vnum(GET_OBJ_VNUM(obj));
    if (!stats || !artifact) return;
    
    // Scale by artifact level
    float level_mult = 1.0 + (artifact->level - 1) * 0.1;
    
    // Apply flat modifiers
    for (int i = 0; i < MAX_STATS; i++) {
        if (stats->modifiers[i] != 0) {
            int bonus = (int)(stats->modifiers[i] * level_mult);
            ch->aff_abils.str_add += bonus;
        }
    }
    
    // Apply percentage modifiers
    for (int i = 0; i < MAX_STATS; i++) {
        if (stats->multipliers[i] != 1.0) {
            ch->stat_multipliers[i] *= stats->multipliers[i];
        }
    }
    
    // Apply resistances (no stacking, take highest)
    for (int i = 0; i < MAX_DAMAGE_TYPES; i++) {
        ch->resistances[i] = MAX(ch->resistances[i], stats->resistances[i]);
    }
}

// Modifier stacking rules
int calculate_stacked_bonus(modifier_stack_t* stack) {
    switch (stack->behavior) {
        case STACK_ADDITIVE:
            int total = 0;
            for (int i = 0; i < stack->count; i++)
                total += stack->modifiers[i].value;
            return total;
            
        case STACK_MULTIPLICATIVE:
            float result = 1.0f;
            for (int i = 0; i < stack->count; i++)
                result *= (1.0f + stack->modifiers[i].value / 100.0f);
            return (int)((result - 1.0f) * 100.0f);
            
        case STACK_DIMINISHING:
            float diminished = 0.0f;
            for (int i = 0; i < stack->count; i++) {
                float effectiveness = 1.0f / (1.0f + i * 0.2f);
                diminished += stack->modifiers[i].value * effectiveness;
            }
            return (int)diminished;
    }
}
```

## 5. Generation and Balancing

### 5.1 Power Budget System
```c
// Power budget ensures balanced artifacts
int calculate_power_budget(int level, int rarity) {
    int base_budget = level * 2;
    float rarity_multipliers[] = {1.0, 1.5, 2.0, 3.0, 5.0};
    return (int)(base_budget * rarity_multipliers[rarity - 1]);
}

// Rarity weights (per 1000 drops)
static const int rarity_weights[] = {
    500,  // Common - 50%
    300,  // Uncommon - 30%
    150,  // Rare - 15%
    45,   // Epic - 4.5%
    5     // Legendary - 0.5%
};

artifact_instance_t* generate_artifact(int vnum, int level, int rarity, uint32_t seed) {
    int budget = calculate_power_budget(level, rarity);
    prng_state_t prng = {seed, seed};
    
    artifact_instance_t* artifact = create_artifact(vnum, NULL);
    
    // Allocate power budget to modifiers
    while (budget > 0 && artifact->modifier_count < MAX_MODIFIERS) {
        modifier_type_t type = roll_modifier_type(&prng, budget);
        int cost = get_modifier_cost(type);
        
        if (cost <= budget) {
            artifact->modifiers[artifact->modifier_count++] = type;
            budget -= cost;
        } else break;
    }
    
    return artifact;
}
```

### 5.2 Drop System
```c
bool check_artifact_drop(CHAR_DATA *ch, CHAR_DATA *victim) {
    // Base drop chance
    float chance = 0.001; // 0.1% base
    
    // Level scaling
    int level_diff = GET_LEVEL(victim) - GET_LEVEL(ch);
    chance *= (1.0 + level_diff * 0.1);
    
    // Boss multiplier
    if (MOB_FLAGGED(victim, MOB_BOSS))
        chance *= 10.0;
    
    // Luck modifier
    chance *= (1.0 + GET_LUCK(ch) * 0.01);
    
    if (rand_number(1, 10000) > chance * 10000)
        return false;
    
    // Select appropriate artifact
    return spawn_artifact_drop(ch, victim);
}
```

## 6. Progression System

### 6.1 Experience and Mastery
```c
// XP required per level
static const int mastery_thresholds[] = {
    0, 1000, 3000, 6000, 10000, 15000, 21000, 28000, 36000, 45000
};

void grant_artifact_exp(CHAR_DATA *ch, OBJ_DATA *obj, int base_exp, xp_source_t source) {
    ARTIFACT_DATA *artifact = find_artifact_by_vnum(GET_OBJ_VNUM(obj));
    if (!artifact || artifact->level >= MAX_ARTIFACT_LEVEL) return;
    
    // Scale by source type
    int exp = base_exp;
    switch (source) {
        case XP_COMBAT_KILL: exp *= 1; break;
        case XP_BOSS_KILL: exp *= 5; break;
        case XP_QUEST_COMPLETE: exp *= 3; break;
        case XP_CRITICAL_HIT: exp *= 2; break;
    }
    
    int old_level = artifact->level;
    artifact->experience += exp;
    
    // Check level up
    while (artifact->level < MAX_ARTIFACT_LEVEL && 
           artifact->experience >= mastery_thresholds[artifact->level]) {
        artifact->level++;
        
        send_to_char("&Y%s grows in power! (Level %d)&n\r\n", 
                     ch, GET_OBJ_SHORT(obj), artifact->level);
        
        // Unlock features
        unlock_artifact_features(obj, artifact);
        
        // Scale stats
        apply_artifact_bonuses(ch, obj);
    }
    
    if (artifact->level > old_level)
        save_artifacts();
}
```

### 6.2 Socket System
```c
typedef struct artifact_socket {
    int position;
    socket_type_t type;  // RED=str, BLUE=int, YELLOW=special
    OBJ_DATA *gem;
    bool is_locked;
} artifact_socket_t;

bool socket_gem(CHAR_DATA *ch, OBJ_DATA *artifact, OBJ_DATA *gem, int slot) {
    if (!can_use_artifact(ch, artifact)) {
        send_to_char("You cannot modify an artifact you don't own!\r\n", ch);
        return false;
    }
    
    artifact_socket_t *socket = get_socket(artifact, slot);
    if (!socket || socket->is_locked) {
        send_to_char("That socket cannot be used.\r\n", ch);
        return false;
    }
    
    if (!gem_matches_socket(gem, socket)) {
        send_to_char("That gem doesn't fit in that socket.\r\n", ch);
        return false;
    }
    
    // Remove existing gem
    if (socket->gem) {
        obj_to_char(socket->gem, ch);
        remove_gem_bonuses(artifact, socket->gem);
    }
    
    // Insert new gem
    socket->gem = gem;
    obj_from_char(gem);
    apply_gem_bonuses(artifact, gem);
    
    send_to_char("You socket %s into %s.\r\n", ch, 
                 GET_OBJ_SHORT(gem), GET_OBJ_SHORT(artifact));
    save_artifacts();
    return true;
}
```

## 7. Abilities System

### 7.1 Ability Framework
```c
typedef void (*artifact_ability_func)(CHAR_DATA *ch, OBJ_DATA *obj, char *arg);

void execute_artifact_ability(CHAR_DATA *ch, OBJ_DATA *artifact, 
                              const char *ability_name, char *argument) {
    ARTIFACT_ABILITY *ability = find_ability(artifact, ability_name);
    if (!ability) {
        send_to_char("This artifact doesn't have that ability.\r\n", ch);
        return;
    }
    
    // Check cooldown
    int time_left = ability->last_used + ability->cooldown - time(0);
    if (time_left > 0) {
        send_to_char("That ability will be ready in %d seconds.\r\n", ch, time_left);
        return;
    }
    
    // Check resources
    if (GET_MANA(ch) < ability->mana_cost) {
        send_to_char("You don't have enough mana.\r\n", ch);
        return;
    }
    
    // Execute
    GET_MANA(ch) -= ability->mana_cost;
    ability->last_used = time(0);
    ability->execute(ch, artifact, argument);
    
    // Grant artifact XP for ability use
    grant_artifact_exp(ch, artifact, 5, XP_ABILITY_USE);
}

// Example ability
ARTIFACT_ABILITY_FUNC(flame_burst) {
    act("$n's $p erupts in flames!", FALSE, ch, obj, NULL, TO_ROOM);
    act("Your $p erupts in flames!", FALSE, ch, obj, NULL, TO_CHAR);
    
    // Damage all enemies in room
    for (CHAR_DATA *vict = world[IN_ROOM(ch)].people; vict; vict = vict->next_in_room) {
        if (vict != ch && can_damage(ch, vict)) {
            int dam = dice(5, 20) + GET_LEVEL(ch);
            damage(ch, vict, dam, TYPE_FIRE);
        }
    }
}
```

## 8. Persistence

### 8.1 Save System
```c
void save_artifacts(void) {
    FILE *fp = fopen(ARTIFACT_SAVE_FILE, "w");
    if (!fp) {
        log("ERROR: Cannot save artifacts!");
        return;
    }
    
    fprintf(fp, "# Artifact Save File v2.0\n");
    fprintf(fp, "# Generated: %s\n\n", timestamp_string());
    
    for (ARTIFACT_DATA *art = artifact_list; art; art = art->next) {
        fprintf(fp, "ARTIFACT %s\n", art->instance_id);
        fprintf(fp, "Vnum: %d\n", art->vnum);
        fprintf(fp, "Owner: %d %s\n", art->owner_id, 
                art->owner_name ? art->owner_name : "none");
        fprintf(fp, "Level: %d\n", art->level);
        fprintf(fp, "Experience: %d\n", art->experience);
        fprintf(fp, "Condition: %d\n", art->condition);
        fprintf(fp, "Acquired: %ld\n", art->acquired_time);
        fprintf(fp, "END\n\n");
    }
    
    fclose(fp);
    
    // Also update database
    save_artifacts_to_database();
}

void load_artifacts(void) {
    FILE *fp = fopen(ARTIFACT_SAVE_FILE, "r");
    if (!fp) {
        log("No artifact save file found, starting fresh.");
        return;
    }
    
    char line[256];
    ARTIFACT_DATA *current = NULL;
    
    while (fgets(line, sizeof(line), fp)) {
        if (line[0] == '#' || line[0] == '\n') continue;
        
        if (!strncmp(line, "ARTIFACT ", 9)) {
            current = calloc(1, sizeof(ARTIFACT_DATA));
            sscanf(line, "ARTIFACT %36s", current->instance_id);
            current->next = artifact_list;
            artifact_list = current;
        } else if (current) {
            parse_artifact_line(current, line);
        }
    }
    
    fclose(fp);
    verify_artifact_integrity();
}
```

## 9. Security and Validation

### 9.1 Duplication Prevention
```c
void verify_artifact_integrity(void) {
    // Check for duplicate instances
    for (ARTIFACT_DATA *a1 = artifact_list; a1; a1 = a1->next) {
        int count = 0;
        
        // Count objects in world
        for (OBJ_DATA *obj = object_list; obj; obj = obj->next) {
            if (GET_OBJ_VNUM(obj) == a1->vnum && IS_ARTIFACT(obj)) {
                count++;
            }
        }
        
        if (count > 1) {
            log("CRITICAL: Multiple instances of artifact %d detected!", a1->vnum);
            resolve_artifact_duplication(a1->vnum);
        }
    }
}

bool validate_artifact_operation(CHAR_DATA *ch, OBJ_DATA *obj, const char *operation) {
    if (!IS_ARTIFACT(obj)) return true;
    
    ARTIFACT_DATA *artifact = find_artifact_by_vnum(GET_OBJ_VNUM(obj));
    if (!artifact) {
        log("ERROR: Artifact object %d has no tracking data!", GET_OBJ_VNUM(obj));
        return false;
    }
    
    // Validate ownership
    if (artifact->owner_id != 0 && artifact->owner_id != GET_IDNUM(ch)) {
        log_security("Player %s attempted %s on artifact %s owned by %d",
                     GET_NAME(ch), operation, artifact->instance_id, artifact->owner_id);
        return false;
    }
    
    return true;
}
```

## 10. Commands

### 10.1 Player Commands
```c
ACMD(do_artifact) {
    char arg1[MAX_INPUT_LENGTH], arg2[MAX_INPUT_LENGTH];
    two_arguments(argument, arg1, arg2);
    
    if (!*arg1) {
        send_to_char("Usage: artifact <list|info|identify|socket>\r\n", ch);
        return;
    }
    
    if (!str_cmp(arg1, "list")) {
        show_artifact_list(ch);
    } else if (!str_cmp(arg1, "info")) {
        OBJ_DATA *obj = get_obj_in_list_vis(ch, arg2, ch->carrying);
        if (!obj) obj = get_object_in_equip_vis(ch, arg2, ch->equipment, &i);
        if (obj && IS_ARTIFACT(obj)) {
            show_artifact_details(ch, obj);
        } else {
            send_to_char("You don't have that artifact.\r\n", ch);
        }
    } else if (!str_cmp(arg1, "identify")) {
        artifact_identify(ch, arg2);
    } else if (!str_cmp(arg1, "socket")) {
        artifact_socket_interface(ch, arg2);
    }
}
```

### 10.2 Admin Commands
```c
ACMD(do_wizartifact) {
    char arg[MAX_INPUT_LENGTH];
    one_argument(argument, arg);
    
    if (!*arg) {
        send_to_char("Usage: wizartifact <status|spawn|purge|reset|history>\r\n", ch);
        return;
    }
    
    if (!str_cmp(arg, "status")) {
        int total = 0, owned = 0, dormant = 0;
        for (ARTIFACT_DATA *art = artifact_list; art; art = art->next) {
            total++;
            if (art->owner_id > 0) owned++;
            else dormant++;
        }
        send_to_char("Artifacts: %d total, %d owned, %d dormant\r\n", 
                     ch, total, owned, dormant);
    } else if (!str_cmp(arg, "spawn")) {
        int vnum = atoi(argument);
        if (spawn_artifact(vnum, IN_ROOM(ch))) {
            send_to_char("Artifact spawned.\r\n", ch);
        } else {
            send_to_char("Failed to spawn artifact.\r\n", ch);
        }
    }
}
```

## 11. Integration Checklist

### Files to Modify:
1. **structs.h**: Add artifact flags and pointers to obj_data
2. **db.c**: Add artifact loading to boot sequence
3. **handler.c**: Modify get/drop/give for artifact rules
4. **fight.c**: Add artifact XP gains and procs
5. **act.item.c**: Add artifact-specific wear/remove handling
6. **interpreter.c**: Register artifact commands

### Database Setup:
```sql
-- Run these queries to initialize
CREATE DATABASE artifacts;
USE artifacts;
-- Create tables from section 2.1
-- Add initial artifact templates
```

### Configuration:
```c
// In config.c or mud.h
#define MAX_ARTIFACTS 1000
#define MAX_ARTIFACT_LEVEL 10
#define ARTIFACT_SAVE_FILE "lib/etc/artifacts"
#define ENABLE_ARTIFACT_PROCS TRUE
#define ARTIFACT_DROP_CHANCE 0.001
```

### Example Artifact:
```c
// In artifact_templates.c
void init_artifact_templates(void) {
    // Excalibur
    artifact_template_t excalibur = {
        .vnum = 31001,
        .name = "Excalibur",
        .short_desc = "the legendary sword Excalibur",
        .item_type = ITEM_WEAPON,
        .wear_flags = ITEM_WEAR_WIELD,
        .binding_type = BIND_ON_PICKUP,
        .rarity_tier = RARITY_LEGENDARY,
        .min_level = 40,
        .power_budget = 200,
        .base_stats = {
            [STAT_DAMROLL] = 10,
            [STAT_HITROLL] = 10,
            [STAT_STR] = 5
        },
        .abilities = {
            {"holy_strike", holy_strike_proc, 300, 50},
            {"divine_shield", divine_shield_proc, 600, 100}
        }
    };
    register_artifact_template(&excalibur);
}
```

This implementation provides a complete, production-ready artifact system with ownership tracking, progression, security, and full integration into existing MUD systems.