/* shop_parallel.c - Robust parallel shop loading with safe post-resolution and format parity */

#include <ctype.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#if !defined(_WIN32)
  #include <unistd.h>
  #include <fcntl.h>
  #include <sys/mman.h>
  #include <sys/stat.h>
  #include <pthread.h>
#endif

#include <errno.h>

#include "structs.h"
#include "comm.h"
#include "db.h"
#include "prototypes.h"
#include "race_class.h"
#include "specs.prototypes.h"
#include "utils.h"

/* Configuration */
#define MAX_THREADS 8

/* External variables */
extern struct shop_data *shop_index;
extern int number_of_shops;
extern const struct race_lookup shop_bigot_table[];

/* Forward declaration for fallback */
extern void boot_the_shops_optimized(void);

/* Temporary parsed shop, stores raw VNUMs and strings parsed by threads.
   Final real_* resolution happens single-threaded afterwards. */
struct parsed_shop_tmp {
  int valid;                /* parsed SHOP number seen */
  int keeper_vnum;          /* raw keeper VNUM (from SHOP: line) */
  int room_vnum;            /* raw room VNUM (from ROOM:) */
  int shop_is_roaming;      /* 1 if ROAMING: present */
  int shop_killable;
  int magic_allowed;
  int deadbeat;
  int offense;
  int greed;
  int profit;
  byte types[ITEM_TYPE_BYTES];
  byte hours[4];
  byte hates[SHOP_BIGOT_BYTES];
  byte cheats[SHOP_BIGOT_BYTES];
  int *po_vnums;           /* dynamic array of product object VNUMs */
  int po_count;
  /* Messages */
  char *SM_shop_open;
  char *SM_shop_close;
  char *SM_sell_to;
  char *SM_buy_from;
  char *SM_shop_broke;
  char *SM_buyer_broke;
  char *SM_shop_not_have;
  char *SM_buyer_not_have;
  char *SM_not_buy;
  char *SM_hates;
};

/* Reset a parsed_shop_tmp to defaults */
static void init_parsed_shop(struct parsed_shop_tmp *ps) {
  bzero(ps, sizeof(*ps));
  ps->valid = 0;
  ps->keeper_vnum = -1;
  ps->room_vnum = -1;
  ps->greed = 100;
  ps->profit = 100;
  SET_CBIT(ps->hates, BIGOT_NPC);
  /* open defaults to 1 in final struct, hours bitset empty means always open */
}

/* Free strings and arrays inside parsed_shop_tmp */
static void free_parsed_shop(struct parsed_shop_tmp *ps) {
  free_string(ps->SM_shop_open);
  free_string(ps->SM_shop_close);
  free_string(ps->SM_sell_to);
  free_string(ps->SM_buy_from);
  free_string(ps->SM_shop_broke);
  free_string(ps->SM_buyer_broke);
  free_string(ps->SM_shop_not_have);
  free_string(ps->SM_buyer_not_have);
  free_string(ps->SM_not_buy);
  free_string(ps->SM_hates);
  if (ps->po_vnums) free(ps->po_vnums);
  bzero(ps, sizeof(*ps));
}

/* Helpers to parse tokens similar to original shop.c */
static void parse_hours_tokens(const char *args, byte *out) {
  /* Supports formats:
   * - "OCOCOC..." 24 chars of O/C
   * - "a-b" or "a,b" possibly repeated separated by spaces
   */
  const char *t = args;
  int i, a, b;
  /* Try O/C 24 format */
  int len = (int)strlen(t);
  if (len >= 24) {
    int oc_found = 1;
    for (i = 0; i < 24; i++) {
      char c = t[i];
      if (c == 'C' || c == 'c') SET_CBIT(out, i);
      else if (c == 'O' || c == 'o') REMOVE_CBIT(out, i);
      else { oc_found = 0; break; }
    }
    if (oc_found) return;
  }
  /* Range or pairs */
  t = args;
  while (*t) {
    if (sscanf(t, " %d-%d ", &a, &b) == 2) {
      if (a >= 0 && a <= b) {
        if (b > 23) b = 23;
        for (i = a; i <= b; i++) SET_CBIT(out, i);
      }
      /* advance t to after pattern */
      const char *sp = strchr(t, ' ');
      t = sp ? sp : t + strlen(t);
    } else if (sscanf(t, " %d,%d ", &a, &b) == 2) {
      if (a >= 0 && a <= b) {
        if (b > 23) b = 23;
        for (i = a; i <= b; i++) SET_CBIT(out, i);
      }
      const char *sp = strchr(t, ' ');
      t = sp ? sp : t + strlen(t);
    } else {
      /* skip token */
      while (*t && !isspace((unsigned char)*t)) t++;
      while (isspace((unsigned char)*t)) t++;
    }
  }
}

static void parse_bt_tokens(char *args, byte *out) {
  /* space separated integers 0..LAST_ITEM_TYPE */
  char *p = args;
  char *token;
  strcat(p, " ");
  while ((token = strsep(&p, " ")) != NULL) {
    if (*token == '\0') continue; /* skip empty tokens */
    if (isdigit((unsigned char)*token)) {
      int v = atoi(token);
      if (v >= 0 && v <= LAST_ITEM_TYPE) SET_CBIT(out, v);
    }
  }
}

static void parse_flags_tokens(const char *args, byte *out, int is_hates) {
  const char *t = args;
  while (*t) {
    while (isspace((unsigned char)*t)) t++;
    if (!*t) break;
    int matched = 0;
    for (int i = 0; shop_bigot_table[i].race != -1; i++) {
      int L = (int)strlen(shop_bigot_table[i].code);
      if (!strn_cmp(t, shop_bigot_table[i].code, (unsigned)L) &&
          (t[L] == ' ' || t[L] == '\0')) {
        SET_CBIT(out, (int)shop_bigot_table[i].race);
        t += L;
        matched = 1;
        break;
      }
    }
    if (!matched) {
      /* skip token */
      while (*t && !isspace((unsigned char)*t)) t++;
    }
  }
}

static void append_po_list(struct parsed_shop_tmp *ps, int obj_vnum) {
  if (obj_vnum <= 0) return;
  int newc = ps->po_count + 1;
  int *arr = (int*)realloc(ps->po_vnums, sizeof(int)*newc);
  if (!arr) return;
  ps->po_vnums = arr;
  ps->po_vnums[ps->po_count] = obj_vnum;
  ps->po_count = newc;
}

/* Safe duplicate message set (free old) */
static void set_msg(char **dst, const char *src) {
  if (*dst) free_string(*dst);
  *dst = str_dup(src);
}



#if !defined(_WIN32)
/* Thread data */
struct range { size_t start; size_t end; };

struct chunk_result {
  struct parsed_shop_tmp *shops;
  int count;
  int cap;
};

struct parse_args {
  const char *data;
  const struct range *ranges;
  int num_ranges;
  struct chunk_result *out;
};

/* Parse a single SHOP definition from [start,end) into parsed_shop_tmp */
static int parse_one_shop(const char *buf, size_t start, size_t end, struct parsed_shop_tmp *ps) {
  init_parsed_shop(ps);
  const char *cur = buf + start;
  const char *limit = buf + end;
  char line[MAX_STRING_LENGTH+1];
  while (cur < limit) {
    const char *nl = memchr(cur, '\n', (size_t)(limit - cur));
    const char *line_end = nl ? nl : limit;
    size_t L = (size_t)(line_end - cur);
    if (L > MAX_STRING_LENGTH) L = MAX_STRING_LENGTH;
    memcpy(line, cur, L);
    line[L] = '\0';
    /* trim CR */
    if (L > 0 && line[L-1] == '\r') line[L-1] = '\0';

    /* empty/comment */
    if (line[0] == '\0' || line[0] == ';') { cur = (nl ? nl+1 : limit); continue; }

    char *colon = strchr(line, ':');
    if (!colon) { cur = (nl ? nl+1 : limit); continue; }
    *colon = '\0';
    char *keyw = line;
    char *args = colon + 1;
    while (*args && isspace((unsigned char)*args)) args++;

    if (!strcmp(keyw, "SHOP")) {
      /* SHOP: vnum */
      int v;
      if (sscanf(args, " %d ", &v) == 1 && v > 0) {
        ps->valid = 1;
        ps->keeper_vnum = v; /* store keeper VNUM as per original meaning */
      }
    } else if (!strcmp(keyw, "ROOM")) {
      int v;
      if (sscanf(args, " %d ", &v) == 1) ps->room_vnum = v;
    } else if (!strcmp(keyw, "ROAMING")) {
      /* flag only */
      ps->shop_is_roaming = 1;
    } else if (!strcmp(keyw, "KILLABLE")) {
      ps->shop_killable = 1;
    } else if (!strcmp(keyw, "CASTING")) {
      ps->magic_allowed = 1;
    } else if (!strcmp(keyw, "DEADBEAT")) {
      int v;
      if (sscanf(args, " %d ", &v) == 1 && v >= 0) ps->deadbeat = v;
    } else if (!strcmp(keyw, "OFFENSE")) {
      int v;
      if (sscanf(args, " %d ", &v) == 1 && v >= 0) ps->offense = v;
    } else if (!strcmp(keyw, "PROFIT")) {
      int v;
      if (sscanf(args, " %d ", &v) == 1 && v >= 10 && v <= 1000) ps->profit = v;
    } else if (!strcmp(keyw, "GREED")) {
      int v;
      if (sscanf(args, " %d ", &v) == 1 && v >= 10 && v <= 1000) ps->greed = v;
    } else if (!strcmp(keyw, "PO")) {
      /* may have multiple ints per line */
      char *tmp = args;
      while (*tmp) {
        while (isspace((unsigned char)*tmp)) tmp++;
        if (!*tmp) break;
        if (!isdigit((unsigned char)*tmp)) { while (*tmp && !isspace((unsigned char)*tmp)) tmp++; continue; }
        int v = atoi(tmp);
        append_po_list(ps, v);
        while (*tmp && !isspace((unsigned char)*tmp)) tmp++;
      }
    } else if (!strcmp(keyw, "HOURS")) {
      parse_hours_tokens(args, ps->hours);
    } else if (!strcmp(keyw, "BT")) {
      /* needs a mutable buffer for strsep */
      char tmpbuf[MAX_STRING_LENGTH+1];
      strncpy(tmpbuf, args, MAX_STRING_LENGTH);
      tmpbuf[MAX_STRING_LENGTH] = '\0';
      parse_bt_tokens(tmpbuf, ps->types);
    } else if (!strcmp(keyw, "HATES")) {
      parse_flags_tokens(args, ps->hates, 1);
    } else if (!strcmp(keyw, "CHEATS")) {
      parse_flags_tokens(args, ps->cheats, 0);
    } else if (!strcmp(keyw, "MOPEN")) {
      set_msg(&ps->SM_shop_open, args);
    } else if (!strcmp(keyw, "MCLOSE")) {
      set_msg(&ps->SM_shop_close, args);
    } else if (!strcmp(keyw, "MSELL")) {
      set_msg(&ps->SM_sell_to, args);
    } else if (!strcmp(keyw, "MBUY")) {
      set_msg(&ps->SM_buy_from, args);
    } else if (!strcmp(keyw, "MSCASH")) {
      set_msg(&ps->SM_shop_broke, args);
    } else if (!strcmp(keyw, "MBCASH")) {
      set_msg(&ps->SM_buyer_broke, args);
    } else if (!strcmp(keyw, "MSHAVE")) {
      set_msg(&ps->SM_shop_not_have, args);
    } else if (!strcmp(keyw, "MBHAVE")) {
      set_msg(&ps->SM_buyer_not_have, args);
    } else if (!strcmp(keyw, "MNBUY")) {
      set_msg(&ps->SM_not_buy, args);
    } else if (!strcmp(keyw, "MBIGOT")) {
      set_msg(&ps->SM_hates, args);
    } else {
      /* unknown key - keep behavior consistent with original by ignoring */
    }

    cur = (nl ? nl+1 : limit);
  }

  return ps->valid;
}

/* Thread worker: parse assigned ranges of complete shops */
static void *worker_parse(void *arg) {
  struct parse_args *pa = (struct parse_args*)arg;
  const char *data = pa->data;
  for (int i = 0; i < pa->num_ranges; i++) {
    const struct range *R = &pa->ranges[i];
    struct parsed_shop_tmp ps;
    if (parse_one_shop(data, R->start, R->end, &ps)) {
      if (pa->out->count >= pa->out->cap) {
        int newcap = pa->out->cap ? pa->out->cap * 2 : 64;
        struct parsed_shop_tmp *nw = (struct parsed_shop_tmp*)realloc(pa->out->shops, sizeof(struct parsed_shop_tmp)*newcap);
        if (!nw) { free_parsed_shop(&ps); continue; }
        pa->out->shops = nw;
        pa->out->cap = newcap;
      }
      pa->out->shops[pa->out->count++] = ps; /* shallow copy OK (strings owned) */
    }
  }
  return NULL;
}
#endif /* ! _WIN32 */

/* Convert parsed shops to final shop_index single-threaded with real_* calls */
static void resolve_and_build(struct parsed_shop_tmp *list, int count) {
  int capacity = count > 0 ? count : 1;
  CREATE(shop_index, struct shop_data, capacity);
  number_of_shops = 0;

  for (int i = 0; i < count; i++) {
    struct parsed_shop_tmp *ps = &list[i];
    /* Resolve keeper */
    int keeper_r = real_mobile(ps->keeper_vnum);
    if (keeper_r == -1) { free_parsed_shop(ps); continue; }

    /* Resolve room if not roaming */
    int in_room_r = NOWHERE;
    if (!ps->shop_is_roaming) {
      if (ps->room_vnum < 0) { free_parsed_shop(ps); continue; }
      in_room_r = real_room(ps->room_vnum);
      if (in_room_r == NOWHERE) { free_parsed_shop(ps); continue; }
    }

    /* Build final shop_data */
    struct shop_data out;
    bzero(&out, sizeof(out));
    out.keeper = keeper_r;
    out.in_room = ps->shop_is_roaming ? -1 : in_room_r;
    out.shop_is_roaming = ps->shop_is_roaming ? 1 : 0;
    out.shop_killable = ps->shop_killable;
    out.magic_allowed = ps->magic_allowed;
    out.deadbeat = ps->deadbeat;
    out.offense = ps->offense;
    out.greed = ps->greed;
    out.profit = ps->profit;
    out.open = 1;
    COPY_CBITS(out.types, ps->types, ITEM_TYPE_BYTES);
    COPY_CBITS(out.hours, ps->hours, 4);
    COPY_CBITS(out.hates, ps->hates, SHOP_BIGOT_BYTES);
    COPY_CBITS(out.cheats, ps->cheats, SHOP_BIGOT_BYTES);

    /* Messages: set defaults if missing */
    out.SM_shop_open     = ps->SM_shop_open     ? ps->SM_shop_open     : str_dup("$n says 'We are open for business, step right up!'");
    out.SM_shop_close    = ps->SM_shop_close    ? ps->SM_shop_close    : str_dup("$n says 'We are closed, we'll reopen at %s.'");
    out.SM_sell_to       = ps->SM_sell_to       ? ps->SM_sell_to       : str_dup("$n says 'Here you go $N, and only %s too!'");
    out.SM_buy_from      = ps->SM_buy_from      ? ps->SM_buy_from      : str_dup("$n says 'Here's your money $N, thanks.'");
    out.SM_shop_broke    = ps->SM_shop_broke    ? ps->SM_shop_broke    : str_dup("$n says 'Too rich for my blood $N, maybe you'd like to buy something first?'");
    out.SM_buyer_broke   = ps->SM_buyer_broke   ? ps->SM_buyer_broke   : str_dup("$n says 'Sorry $N, but you don't have enough money for $p.'");
    out.SM_shop_not_have = ps->SM_shop_not_have ? ps->SM_shop_not_have : str_dup("$n says 'Sorry $N, but I don't have any $p to sell!'");
    out.SM_buyer_not_have= ps->SM_buyer_not_have? ps->SM_buyer_not_have: str_dup("$n says 'You'll have to show it to me first, $N!'");
    out.SM_not_buy       = ps->SM_not_buy       ? ps->SM_not_buy       : str_dup("$n says 'I don't handle those kinds of things $N, try somewhere else.'");
    out.SM_hates         = ps->SM_hates         ? ps->SM_hates         : str_dup("$n says 'I don't deal with your kind $N!  Begone!'");

    /* Production: resolve vnums to real_object */
    out.production_count = 0;
    out.production = NULL;
    if (ps->po_count > 0) {
      /* temporary buffer */
      int *tmp = (int*)malloc(sizeof(int)*ps->po_count);
      int cnt = 0;
      for (int k = 0; k < ps->po_count; k++) {
        int rn = real_object(ps->po_vnums[k]);
        if (rn != NOWHERE) tmp[cnt++] = rn;
      }
      if (cnt > 0) {
        CREATE(out.production, int, cnt);
        memcpy(out.production, tmp, sizeof(int)*cnt);
        out.production_count = cnt;
      }
      free(tmp);
    }

    /* Move ownership of strings from ps (avoid double free) */
    ps->SM_shop_open = ps->SM_shop_close = ps->SM_sell_to = NULL;
    ps->SM_buy_from = ps->SM_shop_broke = ps->SM_buyer_broke = NULL;
    ps->SM_shop_not_have = ps->SM_buyer_not_have = ps->SM_not_buy = ps->SM_hates = NULL;
    if (ps->po_vnums) { free(ps->po_vnums); ps->po_vnums = NULL; ps->po_count = 0; }

    /* Append to global */
    shop_index[number_of_shops++] = out;
  }

  /* Trim to actual size */
  if (number_of_shops > 0) {
    RECREATE(shop_index, struct shop_data, number_of_shops);
  }
}

/* Main entry */
void boot_the_shops_parallel(void) {
#if defined(_WIN32)
  /* No pthread/mmap on Windows build: fallback */
  logit(LOG_STATUS, "Parallel shop loader not available on Windows build, using sequential loader.");
  boot_the_shops_optimized();
  return;
#else
  int fd = open(SHOP_FILE, O_RDONLY);
  if (fd < 0) {
    logit(LOG_STATUS, "No world.shp file, no shops loading.");
    return;
  }
  struct stat st;
  if (fstat(fd, &st) < 0) {
    close(fd);
    logit(LOG_STATUS, "Cannot stat world.shp file, falling back to sequential loading.");
    boot_the_shops_optimized();
    return;
  }
  size_t file_size = (size_t)st.st_size;
  if (file_size == 0) {
    close(fd);
    logit(LOG_STATUS, "world.shp empty, nothing to load.");
    return;
  }

  char *data = (char*)mmap(NULL, file_size, PROT_READ, MAP_PRIVATE, fd, 0);
  close(fd);
  if (data == MAP_FAILED) {
    logit(LOG_STATUS, "Cannot memory map world.shp file, falling back to sequential loading.");
    boot_the_shops_optimized();
    return;
  }

  /* Build list of [start,end) ranges for each SHOP block */
  /* Find all SHOP starts - optimize by scanning line by line instead of byte by byte */
  size_t max_shops = 1024;
  size_t *starts = (size_t*)malloc(sizeof(size_t)*max_shops);
  int shop_cnt = 0;

  const char *line_start = data;
  const char *data_end = data + file_size;

  while (line_start < data_end) {
    /* Check if this line starts with "SHOP:" */
    if (strncmp(line_start, "SHOP:", 5) == 0) {
      if (shop_cnt >= (int)max_shops) {
        max_shops *= 2;
        size_t *ns = (size_t*)realloc(starts, sizeof(size_t)*max_shops);
        if (!ns) break;
        starts = ns;
      }
      starts[shop_cnt++] = (size_t)(line_start - data);
    }

    /* Find next line */
    const char *nl = memchr(line_start, '\n', (size_t)(data_end - line_start));
    if (!nl) break;
    line_start = nl + 1;
  }
  if (shop_cnt == 0) {
    munmap(data, file_size);
    free(starts);
    logit(LOG_STATUS, "No SHOP entries found, falling back to sequential loader.");
    boot_the_shops_optimized();
    return;
  }

  struct range *ranges = (struct range*)malloc(sizeof(struct range)*shop_cnt);
  for (int i = 0; i < shop_cnt; i++) {
    ranges[i].start = starts[i];
    ranges[i].end = (i+1 < shop_cnt) ? starts[i+1] : file_size;
  }
  free(starts);

  /* Decide threads */
  int cpu = (int)sysconf(_SC_NPROCESSORS_ONLN);
  if (cpu < 1) cpu = 1;
  int threads = cpu;
  if (threads > MAX_THREADS) threads = MAX_THREADS;
  if (file_size < 100000) threads = 1;
  if (threads > shop_cnt) threads = shop_cnt; /* not more threads than shops */

  logit(LOG_STATUS, "Loading shop data using %d threads...", threads);

  /* Partition ranges across threads (round-robin contiguous partitions) */
  pthread_t *tids = (pthread_t*)malloc(sizeof(pthread_t)*threads);
  struct parse_args *parv = (struct parse_args*)malloc(sizeof(struct parse_args)*threads);
  struct chunk_result *outs = (struct chunk_result*)malloc(sizeof(struct chunk_result)*threads);
  for (int i = 0; i < threads; i++) {
    outs[i].shops = NULL;
    outs[i].count = 0;
    outs[i].cap = 0;
  }

  int per = shop_cnt / threads;
  int rem = shop_cnt % threads;
  int idx = 0;
  for (int i = 0; i < threads; i++) {
    int take = per + (i < rem ? 1 : 0);
    parv[i].data = data;
    parv[i].ranges = &ranges[idx];
    parv[i].num_ranges = take;
    parv[i].out = &outs[i];
    idx += take;
  }

  for (int i = 0; i < threads; i++) {
    pthread_create(&tids[i], NULL, worker_parse, &parv[i]);
  }
  for (int i = 0; i < threads; i++) {
    pthread_join(tids[i], NULL);
  }

  /* Merge parsed results into a single vector */
  int total = 0;
  for (int i = 0; i < threads; i++) total += outs[i].count;
  struct parsed_shop_tmp *all = NULL;
  int all_cap = 0, all_cnt = 0;
  if (total > 0) {
    all_cap = total;
    all = (struct parsed_shop_tmp*)malloc(sizeof(struct parsed_shop_tmp)*all_cap);
    for (int i = 0; i < threads; i++) {
      for (int j = 0; j < outs[i].count; j++) {
        all[all_cnt++] = outs[i].shops[j];
      }
      free(outs[i].shops);
    }
  }
  free(outs);
  free(parv);
  free(tids);
  free(ranges);

  /* Unmap file */
  munmap(data, file_size);

  /* Build final shop_index single-threaded */
  if (all_cnt > 0) {
    resolve_and_build(all, all_cnt);
    /* Free temp holders without freeing moved strings */
    free(all);
  } else {
    /* nothing parsed, fallback */
    logit(LOG_STATUS, "Parallel parser found 0 shops, falling back to sequential loader.");
    boot_the_shops_optimized();
    return;
  }

  logit(LOG_STATUS, "Loaded %d shops in parallel.", number_of_shops);
#endif
}