/* ***************************************************************************
 *  File: actwiz.c                                             Part of Outcast *
 *  Usage: wizcommands                                                       *
 *  Copyright  1990, 1991 - see 'license.doc' for complete information.      *
 *  Copyright  1994, 1995 - Outcast Systems Ltd.                             *
 *************************************************************************** */

#include <ctype.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#include "assocs.h"
#include "comm.h"
#include "db.h"
#include "events.h"
#include "interp.h"
#include "language.h"
#include "missile.h"
#ifdef NEWJUSTICE
   #include "newjustice.h"
#endif
#ifdef OLDJUSTICE
   #include "justice.h"
#endif
#include "prototypes.h"
#include "race_class.h"
#include "skillrec.h"
#include "specs.prototypes.h"
#include "specs.include.h"
#include "spells.h"
#include "weather.h"
#include "utils.h"
#include "email_reg.h"
#include "olc.h"
#include "bandwidth.h"
#include "utilset.h"

/* external variables */

extern int min_stats_for_class[TOTALCLASS][8];
extern P_olc olc_db;
extern P_char NPC_list;
extern P_char PC_list;
extern int PC_count;
bool pclist_debug = 0;
extern P_desc descriptor_list;
extern P_index mob_index;
extern P_index obj_index;
extern P_obj object_list;
extern P_room world;
extern byte create_locked;
extern byte locked;
extern const char *race_names[];
extern const char *class_abbrev[];
extern const char *action_bits[];
extern const char *affected_bits[];
extern const char *affected2_bits[];
extern const char *apply_types[];
extern const char *bigot_bits[];
extern const char *class_types[];
extern const char *command[];
extern const char *connected_types[];
extern const char *crime_list[];
extern const char *dirs[];
extern const char *drinknames[];
extern const char *equipment_types[];
extern const char *event_names[];
extern const char *exit_bits[];
extern const char *extra_bits[];
extern const char *anti_bits[];
extern const char *trap_bits[];
extern const char *grantable_bits[];
extern const char *item_types[];
extern const char *justice_status[];
extern const char *language_names[];
extern const char *missiles[];
extern const char *player_bits[];
extern const char *player_prompt[];
extern const char *poison_types[];
extern const char *position_types[];
extern const char *race_types[];
extern const char *rangeweapons[];
extern const char *room_bits[];
extern const char *sector_types[];
extern const char *traps[];
extern const char *weapons[];
extern const char *wear_bits[];
extern const char *zone_bits[];
extern const char *rank_names[];
extern const char *assoc_bit_names[];
extern const char *assoc_type_names[];
extern const char *assoc_status_names[];
extern const int Fireweapon_MaxRange[];
extern const int exp_table[TOTALCLASS][52];
extern const struct stat_data stat_factor[];
extern const struct code_control_tags cctags[];
extern ubyte sets_code_control[CODE_CONTROL_BYTES];
extern int Top_Of_OLC_Database;
extern int avail_descs;
extern int bounce_null_sites;
extern int grant_cmds[MAX_GRANT_CMDS][6];
extern int number_of_shops;
extern int pulse;
extern int shutdownflag, reboot, copyover;
extern int spl_table[TOTALLVLS][MAX_CIRCLE];
extern int top_of_mobt;
extern int top_of_objt;
extern int top_of_world;
extern int top_of_zone_table;
extern int guild_locations[][TOTALCLASS];
extern int Email_Registration_Database_Top;
extern struct agi_app_type agi_app[];
extern struct command_info cmd_info[MAX_CMD_LIST];
extern struct con_app_type con_app[];
extern struct shop_data *shop_index;
extern struct socials_head *socials_index[SOCIALS_INDEX_SIZE];
extern struct str_app_type str_app[];
extern struct zone_data *zone_table;
extern struct email_registration_data *EMSD;
extern int cc_thac0_dexMod;
extern const int power_table[];

//extern P_index grp_index; dead code -Azuth
extern P_group group_list;
extern const char *group_flags[];
#ifdef ARTIFACT
extern int totalArtifacts;
extern struct artifact_data *art_index;
#endif

struct ban_t *ban_added = NULL;
struct ban_t *ban_allowed = NULL;
struct ban_t *ban_list = NULL;

static UtilSet  EchoStack = NULL;
void echo_stackpush(P_char ch, char *arg);
void echo_stacksend(P_char ch, P_char tch, int type);
void echo_stackpop(P_char ch);
void echo_stackclr(P_char ch);

/* Macros */

#define ARRAY_SIZE(A)      (sizeof(A)/sizeof(*(A)))

#define MODE_GRANT  0
#define MODE_REVOKE 1

#define GET_VICTIM_ROOM(v, c, a)  (v) = get_char_room_vis((c), (a))

/* protypes for actwiz */
P_char loadmob(int, P_char);
P_obj loaditem(int, P_char);
void do_nothing(P_char ch, char *args);
void followtree(P_char ch, P_char tch, int leadin);
void showFollowTree(P_char ch, char *arg);
void bitbucket(P_char ch, char *arg);


/*  call with an object, recursively calls itself to build a string in the
   format "in <container> in <container>... carried by <name>.  Only limit
   to nesting depth, is MAX_STRING_LENGTH for all output.  Currently only
   used by the new stat routine, probably add it to 'where' later, and the
   god's version of locate spell.  JAB */

char *where_obj(P_obj w_obj, int flag)
{
   if(!flag)
      GS_buf1[0] = 0;

   if(!w_obj)
      {
      debuglog(51, DS_AZUTH, "Lost in the bit bucket! obj = NULL");
      strcat (GS_buf1, "&+RLost in the bit bucket!&N");
      return(GS_buf1);
      }
   if(OBJ_ROOM (w_obj))
      {
      sprintf (GS_buf1 + strlen (GS_buf1), "&+YIn room [&N%d&+Y] &N%s",
               world[w_obj->loc.room].number, world[w_obj->loc.room].name);
      return(GS_buf1);
      }
   if(OBJ_CARRIED (w_obj))
      {
      sprintf (GS_buf1 + strlen (GS_buf1), "&+YBeing carried by &N%s &+Yin [&N%d&+Y]&N",
               GET_NAME (w_obj->loc.carrying),
               (w_obj->loc.carrying->in_room != NOWHERE) ? world[w_obj->loc.carrying->in_room].number : -1);
      return(GS_buf1);
      }
   if(OBJ_WORN (w_obj))
      {
      sprintf (GS_buf1 + strlen (GS_buf1), "&+YEquipped by &N%s &+Y in [&N%d&+Y]&N",
               GET_NAME (w_obj->loc.wearing),
               (w_obj->loc.wearing->in_room != NOWHERE) ? world[w_obj->loc.wearing->in_room].number : -1);
      return(GS_buf1);
      }
   if(OBJ_INSIDE (w_obj))
      {
      sprintf (GS_buf1 + strlen (GS_buf1), "&+YInside &N%s&+Y, ",
               w_obj->loc.inside->short_description ? w_obj->loc.inside->short_description : "No description");
      where_obj (w_obj->loc.inside, TRUE);
      return GS_buf1;
      }
   if(GS_buf1[0] == 0)
      {
      debuglog(51, DS_AZUTH, "Lost in the bit bucket! not found");
      strcat (GS_buf1, "&+RLost in the bit bucket!&N");
      }

   return(GS_buf1);
}

/* make those huge numbers a little more readable, formats a long 000000000 as
   000, 000, 000 and returns the string.  Only used in do_stat() now.  JAB */

char *comma_string(int num)
{
   static char buf1[50] =
   {0}, buf2[50] =
   {0};
   int bp1, bp2, len, j;

   sprintf (buf1, "%d", num);

   len = strlen (buf1);
   bp1 = 0;
   bp2 = 0;

   if(buf1[0] == '-')
      {
      *(buf2 + bp2++) = *(buf1 + bp1++);
      len--;
      }
   if(len < 4)
      return(buf1);      /* doesn't need commas */

   if(len % 3)
      {
      for(j = len % 3; j > 0; j--)
         {
         *(buf2 + bp2++) = *(buf1 + bp1++);
         len--;
         }
      *(buf2 + bp2++) = ',';
      }
   while(len)
      {
      for(j = 0; j < 3; j++)
         {
         *(buf2 + bp2++) = *(buf1 + bp1++);
         len--;
         }
      if(len)
         *(buf2 + bp2++) = ',';
      }

   *(buf2 + bp2) = '\0';

   return(buf2);
}

/* Load a player manually from save files */
/* Ported 9/98 by Shev CRM */

void do_loadchar (P_char ch, char *arg, int cmd)
{
   P_char vict = NULL;
   int tmp;

   if(!*arg)
      {
      send_to_char ("Syntax: loadchar <name>\n", ch);
      return;
      }
   vict = GetNewChar (NEW_PC);

   vict->only.pc->aggressive = -1;
   vict->desc = NULL;

   skip_spaces(&arg);
   if((tmp = restoreCharOnly (vict, arg)) < 0)
      {
      send_to_char ("&+RInvalid Pfile!&n\n", ch);
      logit (LOG_FILE, "Save file of %s is %d", arg, tmp);
      return;
      }
   tmp = restoreItemsOnly (vict, 100);

   /* insert in list
      vict->next = PC_list;
      PC_list = vict; */

   /* saving info for teleport return command */
   vict->specials.was_in_room = vict->in_room;

   char_to_room (vict, ch->in_room, -2);

   act ("$N &+Bappears before you, greatly humbled by your power.", FALSE, ch, 0, vict, TO_CHAR);
   act ("$N &+Bappears before $n, greatly humbled by $s power.", TRUE, ch, 0, vict, TO_NOTVICT);

   PC_count++; // DUH!!!! this was a long standing bug -Azuth 10-10-2003
   logit (LOG_WIZ, "%s loaded %s's char into the game [%d]",
          GET_NAME (ch), GET_NAME (vict), world[ch->in_room].number);
}

void do_release (P_char ch, char *argument, int cmd)
{
   P_desc d = NULL;
   char arg[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];
   int sdesc = -1;

   if(!IS_TRUSTED (ch))
      return;

   one_argument (argument, arg);
   if(!*arg)
      {
      send_to_char ("Usage: release <descriptor #>|<player name>\n", ch);
      return;
      }

   if(is_number (arg))
      {
      sdesc = atoi (arg);
      if((sdesc < 0) || (sdesc > avail_descs))
         {
         send_to_char ("Illegal descriptor number.\n", ch);
         send_to_char ("Usage: release <descriptor #>|<player name>\n", ch);
         return;
         }
      else
         {
         for(d = descriptor_list; d; d = d->next)
            {
            if((d->descriptor == sdesc) && (!d->character || CAN_SEE(ch, d->character)))
               break;
            }

         if(!d)
            {
            send_to_char ("Illegal descriptor number.\n", ch);
            send_to_char ("Usage: release <descriptor #>|<player name>\n", ch);
            return;
            }
         }
      }
   else
      {
      for(d = descriptor_list; d; d = d->next)
         {
         if(d->character && GET_NAME(d->character) && isname (arg, GET_NAME(d->character)) &&
            (d->connected || CAN_SEE(ch, d->character)))
            {
            sdesc = d->descriptor;
            break;
            }
         }
      }

   if(!d || (sdesc < 0) || (sdesc > avail_descs))
      {
      send_to_char ("Illegal argument.\nUsage: release <descriptor #>|<player name>\n", ch);
      return;
      }

   if(d->character)
      {
      if(GET_LEVEL(GET_PLYR(d->character)) > GET_LEVEL(ch))
         d = ch->desc; // opps backfired!
      }

   sprintf(buf, "Closing socket to descriptor #%d (%s)\n",
           sdesc, d->character ? GET_NAME(d->character) : "&+YUnnamed&n");
   send_to_char(buf, ch);
   wizlog(GET_LEVEL(ch), "%s just released socket %d (%s).",
          GET_NAME(ch), sdesc, d->character ? GET_NAME(d->character) : "&+YUnnamed&n");
   logit(LOG_WIZ, "%s just released socket %d (%s).",
         GET_NAME(ch), sdesc, d->character ? GET_NAME(d->character) : "&+YUnnamed&n");
   close_socket(d);
}

void do_emote (P_char ch, char *argument, int cmd)
{
   P_char k;
   int i;
   static char buf[MAX_STRING_LENGTH];

   /*
   if (IS_NPC (ch) && !IS_MORPH (ch))
     return;
 */
   if(IS_CSET (GET_PLYR(ch)->only.pc->pcact, PLR_NOEMOTE))
      {
      send_to_char ("You have NoEmote on.\n", ch);
      return;
      }
   for(i = 0; *(argument + i) == ' '; i++);

   if(!*(argument + i))
      send_to_char ("Yes... But what?\n", ch);
   else
      {
      /*    sprintf(buf, "$n %s", argument + i); */
      for(k = world[ch->in_room].people; k; k = k->next_in_room)
         if((k != ch) && AWAKE (k) && IS_PC(GET_PLYR(k)) && !IS_CSET (GET_PLYR(k)->only.pc->pcact, PLR_NOEMOTE))
            {
            sprintf (buf, "$n %s", language_crypt (ch, k, argument + i));
            act (buf, FALSE, ch, 0, k, TO_VICT);
            }
      if(IS_CSET ((GET_PLYR (ch))->only.pc->pcact, PLR_ECHO))
         {
         sprintf (buf, "$N %s", argument + i);
         act (buf, FALSE, ch, 0, ch, TO_CHAR);
         }
      else
         send_to_char ("Ok.\n", ch);
      }
}

/*
 ** This function now allows a player to transfer anyone
 ** at or below his level
 */
void do_trans(P_char ch, char *argument, int cmd)
{
   P_desc i;
   P_char victim;
   char buf[MAX_INPUT_LENGTH];
   long target, old_room;

   if(IS_NPC(GET_PLYR(ch)))
      return;

   one_argument(argument, buf);

   if(!*buf)
      {
      send_to_char("Who do you wish to transfer?\n", ch);
      return;
      }

   if(str_cmp("all", buf))
      {
      if(!(victim = get_char_in_game_vis(ch, buf, FALSE)))
         {
         send_to_char("No-one by that name around.\n", ch);
         return;
         }

      /* trans PCs only now, add to prevent problems with global NPC stack -Azuth */
      if(!IS_PC(victim))
         {
         send_to_char("Use teleport for non PCs.\n", ch);
         return;
         }

      if((GET_LEVEL(victim) > GET_LEVEL(GET_PLYR(ch))) && IS_TRUSTED(victim))
         {
         send_to_char("You cannot transfer someone higher level than you.\n", ch);
         return;
         }

      if(victim->in_room == NOWHERE)
         {
         send_to_char("Wait til they get into the game!\n", ch);
         return;
         }

      stop_riding(victim);

      if(!can_enter_room(victim, ch->in_room, FALSE) && (GET_LEVEL(ch) < 59))
         {
         send_to_char ("That person can't come here.\n", ch);
         return;
         }

      logit(LOG_WIZ, "%s transfered %s from [%d] to [%d]", GET_NAME(GET_PLYR(ch)), C_NAME(victim),
            world[victim->in_room].number, world[ch->in_room].number);

      act("$n disappears in a mushroom cloud.", FALSE, victim, 0, 0, TO_ROOM);
      target = ch->in_room;
      old_room = victim->in_room;
      char_from_room(victim);
      room_light(old_room, REAL);
      act("$n demands your presence NOW!", FALSE, ch, 0, victim, TO_VICT);
      char_to_room(victim, target, -1);
      char_light(victim);
      room_light(victim->in_room, REAL);
      act("$n arrives from a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
      send_to_char("Ok.\n", ch);
      }
   else
      {        /* Trans All */
      if(GET_LEVEL (ch) < 58)
         {
         send_to_char("Sorry, 'trans all' is a level 58 command.\n", ch);
         return;
         }

      for(i = descriptor_list; i; i = i->next)
         {
         if((i->character != ch) && !i->connected && (GET_LEVEL(i->character) <= GET_LEVEL(ch)))
            {
            victim = i->character;
            act("$n disappears in a mushroom cloud.", FALSE, victim, 0, 0, TO_ROOM);
            target = ch->in_room;
            old_room = victim->in_room;
            char_from_room(victim);
            room_light(old_room, REAL);
            act("$n demands your presence NOW!", FALSE, ch, 0, victim, TO_VICT);
            char_to_room(victim, target, -1);
            char_light(victim);
            room_light(victim->in_room, REAL);
            act("$n arrives from a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
            }
         }

      send_to_char("Ok.\n", ch);
      }
}


void do_at (P_char ch, char *argument, int cmd)
{
   P_char target_mob = NULL;
   P_obj target_obj = NULL;
   char loc_str[MAX_STRING_LENGTH], buf[MAX_STRING_LENGTH];
   int loc_nr, location = NOWHERE, original_loc, bits;
   int   deadlock_protection = 0;

   if(IS_NPC (GET_PLYR(ch)))
      return;

   half_chop (argument, loc_str, buf);
   if(!*loc_str)
      {
      send_to_char ("You must supply a room number or a name.\n", ch);
      return;
      }
   if(is_number (loc_str))
      {
      loc_nr = atoi (loc_str);
      location = real_room (loc_nr);
      if((location == NOWHERE) || (location > top_of_world))
         {
         send_to_char ("No room exists with that number.\n", ch);
         return;
         }
      }
   else
      {
      bits = generic_find (loc_str, (FIND_CHAR_WORLD | FIND_OBJ_WORLD), ch, &target_mob, &target_obj);
      if(!bits)
         {
         send_to_char ("No such creature or object around.\n", ch);
         return;
         }
      if(target_mob && CAN_SEE (ch, target_mob))
         {
         location = target_mob->in_room;
         }
      else
         {
         while(location == NOWHERE)
            {
            deadlock_protection++;
            if(deadlock_protection >= 50)
               {
               send_to_char ("That object is BUGGED! Can't find a location for it.\n", ch);
               wizlog(51, "Danger Will Robinson! abort abort, item could not be found!");
               return;
               }

            if(OBJ_ROOM (target_obj))
               location = target_obj->loc.room;
            else if(OBJ_CARRIED (target_obj))
               location = target_obj->loc.carrying->in_room;
            else if(OBJ_WORN (target_obj))
               location = target_obj->loc.wearing->in_room;
            else if(OBJ_INSIDE (target_obj))
               target_obj = target_obj->loc.inside;
            else
               {
               send_to_char ("That object is BUGGED! Can't find a location for it.\n", ch);
               return;
               }
            }
         if(!CAN_SEE_OBJ (ch, target_obj))
            location = NOWHERE;
         }
      }

   if((location == NOWHERE) || (location > top_of_world))
      {
      send_to_char ("No such creature or object around.\n", ch);
      return;
      }
   if(!can_enter_room (ch, location, TRUE))
      return;

   /* a location has been found. */

   original_loc = ch->in_room;
   char_from_room (ch);
   char_to_room (ch, location, -3);  /* avoid triggering aggros */

   // sprintf(Gbuf1, "&+WDEBUG -> do_at called. location: (%d), buf: (%s)\n", location, buf);
   //send_to_char(Gbuf1, ch);

   command_interpreter (ch, buf);

   if(GET_STAT (ch) != STAT_DEAD)
      {
      char_from_room (ch);
      char_to_room (ch, original_loc, -3);
      }
}

void do_goto (P_char ch, char *argument, int cmd)
{
   char buf[MAX_STRING_LENGTH], output[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH];
   int location = NOWHERE, old_room, i, bits;
   P_char target_mob = NULL, pers;
   P_obj target_obj = NULL;

   if(IS_NPC (GET_PLYR(ch)))
      return;

   one_argument (argument, buf);
   if(!*buf)
      {
      send_to_char ("You must supply a room number or a name.\n", ch);
      return;
      }
   if(is_number (buf))
      {
      location = real_room (atoi (buf));
      if((location == NOWHERE) || (location > top_of_world))
         {
         send_to_char ("No room exists with that number.\n", ch);
         return;
         }
      }
   else
      {
      bits = generic_find (buf, (FIND_CHAR_WORLD | FIND_OBJ_WORLD), ch, &target_mob, &target_obj);
      if(!bits)
         {
         send_to_char ("Nothing by that name.\n", ch);
         return;
         }
      if(target_mob)
         {
         location = target_mob->in_room;
         }
      else
         {
         while(location == NOWHERE)
            {
            if(OBJ_ROOM (target_obj))
               location = target_obj->loc.room;
            else if(OBJ_CARRIED (target_obj))
               location = target_obj->loc.carrying->in_room;
            else if(OBJ_WORN (target_obj))
               location = target_obj->loc.wearing->in_room;
            else if(OBJ_INSIDE (target_obj))
               target_obj = target_obj->loc.inside;
            else
               {
               send_to_char ("That object is BUGGED! Can't find a location for it.\n", ch);
               return;
               }
            }
         }
      }

   if((location == NOWHERE) || (location > top_of_world))
      {
      send_to_char ("No such creature or object around.\n", ch);
      return;
      }
   /* a location has been found. */

   if(IS_CSET (world[location].room_flags, PRIVATE) && (GET_LEVEL (ch) < MAXLVL))
      {
      for(i = 0, pers = world[location].people; pers; pers = pers->next_in_room)
         i++;
      if(i > 1)
         {
         send_to_char ("There's a private conversation there.\n", ch);
         return;
         }
      }
   if(!can_enter_room (ch, location, TRUE))
      return;

   if(IS_NPC(ch) || ch->only.pc->poofOut == NULL)
      {
      strcpy (output, "$n disappears in a puff of smoke.");
      }
   else
      {
      if(!strstr (ch->only.pc->poofOut, "%n"))
         {
         strcpy (output, "$n ");
         strcat (output, ch->only.pc->poofOut);
         }
      else
         {
         strcpy (output, ch->only.pc->poofOut);
         /* bleah, code doubles $ to prevent entering 'act' strings */
         for(i = 0; i < strlen (output); i++)
            if((*(output + i) == '%') && (*(output + i + 1) == 'n'))
               *(output + i) = '$';
         }
      }            /* if */

   act (output, TRUE, ch, 0, 0, TO_ROOM);

   old_room = ch->in_room;
   char_from_room (ch);
   room_light (old_room, REAL);
   char_to_room (ch, location, -1);
   if(IS_NPC(ch) || ch->only.pc->poofIn == NULL)
      {
      strcpy (output, "$n appears with an ear-splitting bang.");
      }
   else
      {
      if(!strstr (ch->only.pc->poofIn, "%n"))
         {
         strcpy (output, "$n ");
         strcat (output, ch->only.pc->poofIn);
         }
      else
         {
         strcpy (output, ch->only.pc->poofIn);
         /* bleah, code doubles $ to prevent entering 'act' strings */
         for(i = 0; i < strlen (output); i++)
            if((*(output + i) == '%') && (*(output + i + 1) == 'n'))
               *(output + i) = '$';
         }
      }            /* if */

   act (output, TRUE, ch, 0, 0, TO_ROOM);

   // Check for olc mode.
   if(ch->desc->olc)
      {
      if(!Check_OLC_Access(ch, world[location].number, 1))
         {
         sprintf(Gbuf1,
                 "&+CWarning! You don't have access to modify room #%d, moving and exiting OLC.\r\n",
                 world[ch->in_room].number);
         SEND_TO_Q(Gbuf1, ch->desc);
         olc_end(ch->desc->olc);
         }
      else
         {
         ch->desc->olc->rnum = ch->in_room;
         olc_show_menu(ch->desc->olc);
         }
      }
}

void stat_room(P_char ch, char *arg, int paging);
void stat_zone(P_char ch, char *arg, int paging);
void stat_obj(P_char ch, char *arg, int paging, int extra_desc);
void stat_char(P_char ch, char *arg, int paging, int extra_desc);
void stat_shop(P_char ch, char *arg, int paging);
void stat_skill(P_char ch, char *arg, int paging);
void stat_trophy(P_char ch, char *arg, int paging);
void stat_group(P_char ch, char *arg, int paging);
void stat_assoc(P_char ch, char *arg, int paging);
void stat_social(P_char ch, char *arg, char *arg1, int paging);
void stat_house(P_char ch, char *arg, int paging);

#define STAT_SYNTAX "\
Syntax:\n \
   stat Char|Mob # 'name'\n \
   stat Expanded char|mob #|'name'\n \
   stat Group 'name of any group member'\n \
   stat sKill 'name'\n \
   stat Obj|Item  #|'name'\n \
   stat eXpanded obj|item #|'name'\n \
   stat Room <room #>\n \
   stat Shop #|'name'\n \
   stat Trophy 'name'\n \
   stat Zone <zone #>\n \
   stat sociaL #|'name'\n \
   stat Assoc #\n \
(Note that only the single uppercase letter is required for type of stat)\n"

void do_stat(P_char ch, char *argument, int cmd)
{
   int paging, extra_desc;
   char arg1[MAX_STRING_LENGTH] = "", arg2[MAX_STRING_LENGTH] = "";


   if(IS_NPC(GET_PLYR(ch)))
      return;

   argument_interpreter(argument, arg1, arg2);

   if(cmd == CMD_STAT_NOPAGE)
      paging = 0;
   else
      paging = 1;

   if(!*arg1)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   switch(tolower(*arg1))
      {
      case 'r':
         stat_room(ch, arg2, paging);
         break;

      case 'z':
         stat_zone(ch, arg2, paging);
         break;

      case 'o':
      case 'i':
         extra_desc = 0;
         stat_obj(ch, arg2, paging, extra_desc);
         break;

      case 'c':
      case 'm':
         extra_desc = 0;
         stat_char(ch, arg2, paging, extra_desc);
         break;

      case 's': /* stat is done on the shopkeeper actually */
         stat_shop(ch, arg2, paging);
         break;

      case 'k':
         stat_skill(ch, arg2, paging);
         break;

      case 't':
         stat_trophy(ch, arg2, paging);
         break;

      case 'g':
         stat_group(ch, arg2, paging);
         break;

      case 'a':
         stat_assoc(ch, arg2, paging);
         break;

      case 'l':
         stat_social(ch, arg2, arg1, paging);
         break;

      case 'h':
         stat_house(ch, arg2, paging);
         break;

      case 'x':
         extra_desc = 1;
         stat_obj(ch, arg2, paging, extra_desc);
         break;

      case 'e':
         extra_desc = 1;
         stat_char(ch, arg2, paging, extra_desc);
         break;

      default:
         send_to_char(STAT_SYNTAX, ch);
      }
}

void stat_room(P_char ch, char *arg, int paging)
{
   int   i;
   P_obj j = NULL;
   P_char k = NULL;
   P_room rm = NULL;
   P_event e1 = NULL;
   struct extra_descr_data *desc = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";
   char buf[MAX_STRING_LENGTH] = "", buf2[MAX_STRING_LENGTH] = "";
   uint  weight = 0;


   if(!*arg)
      i = ch->in_room;
   else
      { /* accept a room number as second arg */
      if(!is_number(arg) || ((i = real_room(atoi(arg))) < 0) || (i > top_of_world))
         {
         send_to_char("Room not in world.\n", ch);
         return;
         }
      }

   rm = &world[i];

   sprintf(o_buf, "&+YNumber: [&N%d&+Y](&N%d&+Y)  Zone: &N%d&+Y  Name: &N%s\n",
           rm->number, i, rm->zone, rm->name);

   sprintf(o_buf + strlen(o_buf), "&+YDimensions: Length(x): &N%d&+Y Width(y): &N%d&+Y Height(z): &N%d\n",
           rm->length, rm->width, rm->height);
   sprinttype(rm->sector_type, sector_types, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YSpecial procedure:&N %s &+YSector type:&N %s\n",
           (rm->funct) ? "&+RExists&N" : "None", buf2);

   sprint_cbit(rm->room_flags, MAX_ROOM_FLAG_BITS, room_bits, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YRoom flags:&N %s\n", buf2);

#if TRAPS
   if(ROOM_HAS_TRAP(rm))
      sprintf(o_buf + strlen(o_buf), "&+RRoom is trapped: &+Ylevel&N %d &+Yby&N %s\n",
              rm->trap_level, rm->trapper);
#endif

   sprintf(o_buf + strlen(o_buf),
           "&+YLevel Restrictions: (&N%d&+Y)-(&N%d)&+Y  Chance of falling:&N %d&+Y%%  Light sources:&N %d\n&+YDescription:&N\n",
           rm->minlvl, rm->maxlvl, rm->chance_fall, rm->light);

   if(rm->description)
      strcat(o_buf, rm->description);
   else
      strcat(o_buf, "(NULL)  *FIX IT*\n");

   if(rm->ex_description)
      {
      strcpy(buf, "\n&+YExtra description keywords(s):\n");
      for(desc = rm->ex_description; desc; desc = desc->next)
         {
         if(desc->keyword)
            strcat(buf, desc->keyword);
         else
            strcat(buf, "(NULL)  *FIX IT*\n");
         strcat(buf, "\n");
         }
      strcat(buf, "\n");
      strcat(o_buf, buf);
      }

   strcpy(buf, "&+Y------- Chars present -------\n");
   for(k = rm->people; k; k = k->next_in_room)
      {
      weight += total_ch_weight(k);
      if(IS_PC(k) && !CAN_SEE(ch, k))
         continue;

      strcat(buf, IS_PC(k) ? " &+Y(PC)&N " : "&+R(NPC)&N ");
      strcat(buf, C_NAME(k));
      strcat(buf, "\n");
      }
   strcat(buf, "\n");
   strcat(o_buf, buf);

   if(rm->contents)
      {
      strcpy(buf, "&+Y--------- Contents ---------\n");
      for(j = rm->contents; j; j = j->next_content)
         {
         weight += (j->weight < 0 ? 0 : j->weight);
         strcat(buf, j->name);
         strcat(buf, "\n");
         }
      strcat(buf, "\n");
      strcat(o_buf, buf);
      }

   sprintf(buf, "&+YTotal Weight in Room: %d\n", weight);
   strcat(o_buf, buf);

   strcat(o_buf, "&+Y------- Exits defined -------\n");
   for(i = 0; i <= 5; i++)
      {
      if(rm->dir_option[i])
         {
         sprintbit((uint) rm->dir_option[i]->exit_info, exit_bits, buf2);
         sprintf(buf, "&+YDirection &+R%5s  &+YKeyword: &+G%s  &+YKey:&N %d  &+YExit flag: &N%s\n&+YTo room: [&N%d&+Y](&N%d&+Y)&N  %s\n\n",
                 dirs[i], rm->dir_option[i]->keyword ? rm->dir_option[i]->keyword : "NONE",
                 rm->dir_option[i]->key, buf2,
                 (rm->dir_option[i]->to_room != NOWHERE) ?
                 world[rm->dir_option[i]->to_room].number : -1,
                 rm->dir_option[i]->to_room,
                 (rm->dir_option[i]->general_description) ?
                 rm->dir_option[i]->general_description : "UNDEFINED");
         strcat(o_buf, buf);
         if(IS_SET(rm->dir_option[i]->exit_info, EX_TRAPPED))
            {
            sprintf(buf, "&+YTrap state &+R%s  &+YType: &+G%s  &+YMin dam:&N %d  &+YMax dam: &N%d\n&+YEffect: &+R%s  &+YHardness: &+C%d&N\n\n",
                    rm->dir_option[i]->trap_state ? "ENABLED" : "DISABLED",
                    traps[rm->dir_option[i]->trap_type],
                    rm->dir_option[i]->trap_min_damage,
                    rm->dir_option[i]->trap_max_damage,
                    rm->dir_option[i]->trap_effect ? "AREA" : "PLAYER",
                    rm->dir_option[i]->trap_hardness);
            strcat(o_buf, buf);
            }
         }
      }

   if(rm->events)
      {
      strcat(o_buf, "&+Y------- Events:\n&+Y-------\n");
      for(e1 = rm->events; e1; e1 = e1->next)
         {
         sprintf(o_buf + strlen(o_buf), "%6d&+Y seconds,&n %s%s&+Y.\n",
                 event_time(e1, T_SECS), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
         }
      strcat(o_buf, "\n");
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}

char *map_cmd_status(int stat)
{
   static char lbuf[MAX_STRING_LENGTH] = "";

   switch(stat)
      {
      case CMD_STAT_SUCCESS:
         strcpy(lbuf, "SUCCESS");
         break;
      case CMD_STAT_NO_MOB:
         strcpy(lbuf, "NO_MOB");
         break;
      case CMD_STAT_SKIP_IF:
         strcpy(lbuf, "SKIP_IF");
         break;
      case CMD_STAT_MOB_FAILED_LOAD:
         strcpy(lbuf, "MOB_FAILED_LOAD");
         break;
      case CMD_STAT_OBJ_FAILED_LOAD:
         strcpy(lbuf, "OBJ_FAILED_LOAD");
         break;
      case CMD_STAT_RARE_PCT_FAILED:
         strcpy(lbuf, "RARE_PCT_FAILED");
         break;
      case CMD_STAT_UNKNOWN_CMD:
         strcpy(lbuf, "UNKNOWN_CMD");
         break;
      case CMD_STAT_DISABLED:
         strcpy(lbuf, "DISABLED");
         break;
      case CMD_STAT_TIME_FAIL:
         strcpy(lbuf, "TIME_FAIL");
         break;
      case CMD_STAT_MAX_LIMIT_HIT:
         strcpy(lbuf, "MAX_LIMIT_HIT");
         break;
      case CMD_STAT_OBJ_TO_OBJ_FAIL:
         strcpy(lbuf, "OBJ_TO_OBJ_FAIL");
         break;
      case CMD_STAT_BAD_DOOR:
         strcpy(lbuf, "BAD_DOOR");
         break;
      case CMD_STAT_REMOVE_OBJ_FAIL:
         strcpy(lbuf, "REMOVE_OBJ_FAIL");
         break;
      case CMD_STAT_REMOVE_MOB_FAIL:
         strcpy(lbuf, "REMOVE_MOB_FAIL");
         break;
      case CMD_STAT_EQUIP_MOB_FAIL:
         strcpy(lbuf, "EQUIP_MOB_FAIL");
         break;
      case CMD_STAT_ERROR:
         strcpy(lbuf, "ERROR");
         break;
      default:
         strcpy(lbuf, "UNKNOWN CMD STATUS");
      }

   return lbuf;
}

#define ZCMD zone_table[zn].cmd[cmd_no]

void stat_zone(P_char ch, char *arg, int paging)
{
   int   i, yo, count, ii, ij, i2, i3, zn, cmd_no, alloc_str;
   bool  show_cmds = FALSE;
   P_char tch = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";
   char Gbuf1[MAX_STRING_LENGTH] = "";
   struct zone_data *zone = NULL;
   char  *z_buf = NULL;
   struct time_info_data realtime;


   if(!*arg)
      zone = &zone_table[world[ch->in_room].zone];
   else
      { /* accept a zone number as second arg */
      if(is_number(arg) && ((i = atoi(arg)) > -1) && (i <= top_of_zone_table))
         zone = &zone_table[i];
      else if(!str_cmp(arg, "cmds"))
         {
         if(GET_LEVEL(GET_PLYR(ch)) < 54)
            {
            send_to_char("Sorry, this is a restricted cmd, see your FORGER if you need it.\n", ch);
            return;
            }

         zn = world[ch->in_room].zone;
         zone = &zone_table[zn];
         show_cmds = TRUE;
         for(cmd_no = 0; ZCMD.command != 'S'; cmd_no++)
            ;

         alloc_str = (cmd_no + 10) * 100;
         CREATE(z_buf, char, alloc_str);
         sprintf(z_buf + strlen(z_buf), "&+W  Total Zone Commands %4d&n\n", cmd_no);
         sprintf(z_buf + strlen(z_buf), "&+WCmd# [C] IF_fl   arg1   arg2   arg3   arg4 Reason&n\n");
         for(cmd_no = 0; ZCMD.command != 'S'; cmd_no++)
            {
            sprintf(z_buf + strlen(z_buf), "&+W%4d &+Y[%c] if(%d) %6d %6d %6d %6d &+R%s&n\n",
                    cmd_no, (ZCMD.command == '!' ? ZCMD.old_command : ZCMD.command),
                    (ZCMD.if_flag ? (ZCMD.if_flag == 2 ? 2 : 1) : 0), ZCMD.arg1v, ZCMD.arg2v, ZCMD.arg3v, ZCMD.arg4v,
                    map_cmd_status(ZCMD.status));

            if(strlen(z_buf) + 100 > alloc_str)
               {
               sprintf(z_buf + strlen(z_buf), "&+Wtoo many to show...&n\n");
               break;
               }
            }

         page_string(ch->desc, z_buf, 1);
         free(z_buf);
         return;
         }
      else
         {
         send_to_char("Invalid zone #.\n", ch);
         return;
         }
      }

   sprintf(Gbuf1, "&+YZone: (&N%d&+Y)  Name:&N %s\n", world[zone->real_bottom].zone, zone->name);
   sprintf(Gbuf1 + strlen(Gbuf1), "&+YZone filename: &n%s\n", zone->filename);
   sprintf(Gbuf1 + strlen(Gbuf1),
           "&+YRooms: &N%d  &+YRange: [&N%d&+Y](&N%d&+Y) to [&N%d&+Y](&N%d&+Y)  Limit: &N%d\n",
           zone->actual_rooms,
           world[zone->real_bottom].number, zone->real_bottom,
           world[zone->real_top].number, zone->real_top, zone->top);
   sprintf(Gbuf1 + strlen(Gbuf1),
           "&+YUnused room numbers: &N%d  &+YOLC free-room pool for this zone: &N%d\n",
           zone->free, zone->avail_to_olc);
   sprintf(Gbuf1 + strlen(Gbuf1), "&+YArea makers currently assigned to this zone:&+C");

   //fprintf(stderr, "filename: (%s), top olc: (%d) \n", zone->filename, Top_Of_OLC_Database);

   yo = 0;
   count = 0;
   if(zone->filename)
      {
      for(count = 1; count <= Top_Of_OLC_Database; count++)
         {
         if(olc_db[count].AreaFileName)
            {
            if(!strcmp(olc_db[count].AreaFileName, zone->filename))
               {
               fprintf(stderr, "User owning this zone: (%s)\n", olc_db[count].UserName);
               sprintf(Gbuf1 + strlen(Gbuf1), " %s  ", olc_db[count].UserName);
               yo++;
               }
            }
         }
      }

   if(yo == 0)
      sprintf(Gbuf1 + strlen(Gbuf1), " &+rNone\n");
   else
      sprintf(Gbuf1 + strlen(Gbuf1), "\n");

   strcpy(o_buf, Gbuf1);

   sprintf(o_buf + strlen(o_buf), "&+YLifespan: &N%d  &+YAge: &N%d  &+YReset Mode: &+R",
           zone->lifespan, zone->age);

   switch(zone->reset_mode)
      {
      case 0:
         strcat(o_buf, "Zone never resets.\n");
         break;
      case 1:
         strcat(o_buf, "Zone resets when empty.\n");
         break;
      case 2:
         strcat(o_buf, "Zone resets regardless.\n");
         break;
      case 3:
         strcat(o_buf, "Zone resets when empty and can majorly reset.\n");
         break;
      case 4:
         strcat(o_buf, "Zone resets regardless and can majorly reset.\n");
         break;
      default:
         strcat(o_buf, "Invalid reset mode!\n");
         break;
      }

   if(zone->reset_mode == 3 || zone->reset_mode == 4)
      sprintf(o_buf + strlen(o_buf), "&+YPct chance restock:&N %d  &+YHours between req:&N %d  &+YEmpty count req:&N %d\n",
              zone->mzr_pct, zone->hrs_since_req, zone->zone_empty_cnt_req);

   real_time_passed(&realtime, time(0), zone->time_last_boot);
   sprintf(o_buf + strlen(o_buf), "&+YZone empty count:&N %d  &+YHours since last major reset:&N %d\n",
           zone->zone_empty_cnt, realtime.hour);

   if(zone->hometown)
      {
      ii = ij = 0;
      for(tch = NPC_list; tch; tch = tch->next)
         {
         if((tch->in_room == NOWHERE) || (CHAR_IN_TOWN(tch) != CHAR_IN_TOWN(ch)))
            continue;

         if(tch->specials.arrest_by)
            ij++;
         }
#if 0
      for(guard_list = hometowns[zone->hometown - 1].guard_list; guard_list; guard_list = guard_list->next)
         {
         ii++;
         if(guard_list->target || guard_list->ch->specials.arrest_by)
            ij++;
         }
#endif

      sprintf(o_buf + strlen(o_buf), "&+YJustice Hometown:&N %s  &+YActive Justice guards:&N %d\n",
              town_name_list[zone->hometown], ij);
      }

   sprintbit(zone->flags, zone_bits, Gbuf1);
   sprintf(o_buf + strlen(o_buf), "&+YZone flags:&N %s\n\nExits from this zone:\n", Gbuf1);

   // Display all connecting rooms to other zones.
   for(i3 = 0, i = zone->real_bottom; (i != NOWHERE) && (i <= zone->real_top); i++)
      {
      for(i2 = 0; i2 < 6; i2++)
         {
         if(IS_CSET(world[i].room_flags, RESERVED_OLC))
            continue;

         if(world[i].dir_option[i2])
            {
            if((world[i].dir_option[i2]->to_room == NOWHERE) ||
               ((world[world[i].dir_option[i2]->to_room].zone != world[i].zone) &&
                (!IS_CSET(world[world[i].dir_option[i2]->to_room].room_flags, RESERVED_OLC))))
               {
               if(!i3)
                  i3 = 1;
               if(world[i].dir_option[i2]->to_room == NOWHERE)
                  {
                  sprintf(o_buf + strlen(o_buf), " &+Y[&n%5d&+Y](&n%5d&+Y)&n &+R%-5s&n to &+WNOWHERE\n",
                          world[i].number, i, dirs[i2]);
                  }
               else if(world[world[i].dir_option[i2]->to_room].name != world[0].name)
                  {
                  sprintf(o_buf + strlen(o_buf),
                          " &+Y[&n%5d&+Y](&n%5d&+Y)&n &+R%-5s&n to &+Y[&+R%3d&n:&+Y%5d&+Y](&n%5d&+Y)&n\n",
                          world[i].number, i, dirs[i2], world[world[i].dir_option[i2]->to_room].zone,
                          world[world[i].dir_option[i2]->to_room].number,
                          world[i].dir_option[i2]->to_room);
                  //MIAX                 world[world[i].dir_option[i2]->to_room].name);
                  }
               }
            }
         }
      }

   if(!i3)
      strcat(o_buf, "&+RNONE!&n\n");

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}
#undef ZCMD

void stat_obj(P_char ch, char *arg, int paging, int extra_desc)
{
   int   i, vnum, t_stamp, sector, invisobjs = 0;
   P_event e1 = NULL;
   P_obj j = NULL, t_obj = NULL, k = NULL;
   struct extra_descr_data *desc = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";
   char buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "", buf2[MAX_STRING_LENGTH] = "";
   struct func_attachment *fn = NULL;


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      {
      if((i = real_object(atoi(arg))) < 0)
         {
         send_to_char("Illegal object number.\n", ch);
         return;
         }

      /* load one to stat, extract after statting */
      t_obj = read_object(i, REAL);

      /* skip the lookup part, causes probs, we'll stat the one we loaded */
      j = t_obj;
      if(!t_obj)
         {
         logit(LOG_DEBUG, "do_stat(): obj %d [%d] not loadable", i, obj_index[i].virtual);
         return;
         }
      else
         obj_to_char(t_obj, ch);
#ifdef ARTIFACT
      /* this prevents the artifact from being removed from
       * the owner if a temp artifact is created -- DDW */
      tagBogusArtifact(t_obj);
#endif
      }

   if(!j && !(j = get_obj_vis(ch, arg)))
      {
      send_to_char("No such object.\n", ch);
      if(t_obj)
         extract_obj(t_obj);
      return;
      }

   vnum = (j->R_num >= 0) ? obj_index[j->R_num].virtual : 0;

   sprinttype(GET_ITEM_TYPE(j), item_types, buf2);
   sprintf(o_buf, "&+YObject:\n&+YNumber: [&N%d&+Y](&N%d&+Y)  Type: &N%s  &+YName: &N%s\n",
           vnum, j->R_num, buf2, j->short_description ? j->short_description : "None");
   sprintf(o_buf + strlen(o_buf),
           "&+YKeywords: &N%s\n&+YLong description:\n%s\n",
           j->name ? j->name : "None",
           j->description ? j->description : "None");

   if(j->ex_description)
      {
      strcpy(buf, "&+YExtra description keyword(s):\n&+Y----------\n");
      for(desc = j->ex_description; desc; desc = desc->next)
         {
         if(desc->keyword)
            strcat(buf, desc->keyword);
         else
            strcat(buf, "(NULL)  *FIX IT*\n");
         strcat(buf, "\n");
         }

      strcat(buf, "&+Y----------\n");
      strcat(o_buf, buf);
      }

   invisobjs = 0;
   for(k = object_list; k; k = k->next)
      {
      if(k->R_num == j->R_num)
         {
         if(!CAN_SEE_OBJ(ch, k))
            invisobjs++;

         if(OBJ_INSIDE(k))
            { // only checking one lvl deep on bags
            if(!CAN_SEE_OBJ(ch, k->loc.inside))
               invisobjs++;
            }
         }
      }

   sprintf(o_buf + strlen(o_buf), "&+YNumber in game : &N%d\n",
           (obj_index[j->R_num].number - ((t_obj != NULL) ? 1 : 0) - invisobjs));

   sprintbit(j->wear_flags, wear_bits, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YCan be worn on : &N%s\n", buf2);

   sprint_cbit(j->sets_affs, MAX_AFF_BITS, affected_bits, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YSet char bits  : &N%s\n", buf2);

   sprintbit(j->extra_flags, extra_bits, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YExtra flags    : &N%s\n", buf2);

   sprintbit(j->anti_flags, anti_bits, buf2);
   sprintf(o_buf + strlen(o_buf), "&+YAnti flags     : &N%s\n", buf2);

   sprintf(o_buf + strlen(o_buf), "&+YWeight: &N%d&+Y lbs,   Value: &N%s"
           "   &+YDurability: &N%d\n", j->weight,
           comma_string((int) (j->cost)), j->durability);
   strcat(o_buf, buf1);

   if(!t_obj)
      {
      strcat(o_buf, "&+YLocation: ");
      strcat(o_buf, where_obj(j, FALSE));
      strcat(o_buf, "\n");
      }

   switch(j->type)
      {
      case ITEM_LIGHT:
         sprintf(buf, "&+YColor: [&N%d&+Y]  Type: [&N%d&+Y]  Hours: [&N%d&+Y]",
                 j->value[0], j->value[1], j->value[2]);
         break;

      case ITEM_POTION:
      case ITEM_SCROLL:
         sprintf(buf, "&+Y Level: &N%d&+Y  Spells:&N ", j->value[0]);

         for(i = 1; (i < 4) && (j->value[i] > 0); i++)
            {
            sprinttype(j->value[i] - 1, spells, buf2);
            sprintf(buf, "%s%d&+Y) &+C%s&n, ", buf, j->value[i], buf2);
            }

         i = strlen(buf);
         if(buf[i - 2] != ',')
            strcat(buf, "&+RBUGGED!&N\n");
         else
            {
            buf[i - 2] = ',';
            buf[i - 1] = '\0';
            }
         break;

      case ITEM_STAFF:
      case ITEM_WAND:
         if(j->value[3] > 0)
            sprinttype(j->value[3] - 1, spells, buf2);
         else
            strcpy(buf2, "&+RBUGGED!&");
         sprintf(buf, "%d(%d)&+Y charges, Level &N%d&+Y spell: %d) &+C%s&N",
                 j->value[1], j->value[2], j->value[0], j->value[3], buf2);
         break;

      case ITEM_FIREWEAPON:
         if(Get_Fireweapon_Type(j) > ENDOF_THROWN_WEAPONS)
            strcpy(buf2, "&+RBUGGED!&N");
         else
            sprinttype(Get_Fireweapon_Type(j) - 1, rangeweapons, buf2);
         if(IS_ARCHERY_WEAPON(j))
            sprintf(buf, "&+YType: &n%s  &+YFiring Delay: &N%d  &+YMax ROF: &N%d  &+YDurability: &N%d\n&+YDamage per hit: &n%d&+Yd&n%d   &+YNormal Hit Message: &n%d",
                    buf2, GET_FIREWEAPON_DELAY(j), GET_FIREWEAPON_ROF(j),
                    GET_FIREWEAPON_DURABILITY(j),
                    j->value[1], j->value[2], j->value[3]);
         else
            sprintf(buf, "&+YType: &n%s  &+YFiring Delay: &n%d  &+YMax ROF: &n%d  &+YDurability: &n%d\n&+YDamage per hit: &n%d&+Yd&n%d  &+YNormal Hit Message: &n%d  &+YMax Range: &n%d",
                    buf2, GET_FIREWEAPON_DELAY(j), GET_FIREWEAPON_ROF(j),
                    GET_FIREWEAPON_DURABILITY(j),
                    j->value[1], j->value[2], j->value[3],
                    Fireweapon_MaxRange[Get_Fireweapon_Type(j)]);
         break;

      case ITEM_WEAPON:
         if((j->value[3] < 1) || (j->value[3] > 11))
            strcpy(buf2, "&+RBUGGED!&N");
         else
            sprinttype(j->value[3] - 1, weapons, buf2);
         sprintf(buf, "%s &+Yfor &N%dD%d&N  &+RProcVal: &N%d", buf2, j->value[1],
                 j->value[2], j->value[0]);
         break;

      case ITEM_QUIVER:
         sprintf(buf2, "&+YQuiver Holds: &n%s", IS_ARCHERY_QUIVER(j) ? "Arrows" : "Range Weapons");
         sprintf(buf, "%s  &+YContainer Flags: &N%d  &+YKey Number: &N%d\n&+YMax No. Missiles: &N%d  &+YCurrent No. Missiles. &N%d",
                 buf2, j->value[1], j->value[2], j->value[0], AMOUNT_OF_AMMO(j));
         break;

      case ITEM_MISSILE:
         if((j->value[3] < 1) || (j->value[3] > 16))
            strcpy(buf2, "&+RBUGGED!&N");
         else
            sprinttype(j->value[3] - 1, missiles, buf2);
         sprintf(buf, "&+YType: &n%s  &+YCondition: &n%d  &+YDamage: &N%dD%d&N  &+YMax Range: &n%d",
                 buf2, j->value[2], j->value[0], j->value[1],
                 Fireweapon_MaxRange[j->value[3]]);
         break;

      case ITEM_ARMOR:
         sprintf(buf, "&+YAC-apply: &N%d  &+rWarmth: &N%d  &+YPrestige: &N%d  &+RProcVal: &N%d",
                 j->value[0], j->value[1], j->value[2], j->value[3]);
         break;

      case ITEM_TRAP:
         sprintf(buf, "&+YSpell: &N%d&+Y - Hitpoints:&N %d", j->value[0], j->value[1]);
         break;

      case ITEM_CONTAINER:
         sprintf(buf, "&+YHolds: &N%d  &+YLocktype: &N%d  &+YKey: &N%d",
                 j->value[0], j->value[1], j->value[2]);
         break;

      case ITEM_CORPSE:
         if(j->action_description)
            {
            if(sscanf(j->action_description, " %d %s ", &t_stamp, buf) == 2)
               {
               t_stamp = (time(0) - t_stamp);
               sprintf(buf, "&+YCreated: &n%d&+Y days&n %2d&+YH&n %2d&+YM&n %2d&+YS ago.\n",
                       t_stamp / 86400, (t_stamp % 86400) / 3600, (t_stamp / 60) % 60, t_stamp % 60);
               }
            else
               buf[0] = '\0';

            if(j->value[1] == PC_CORPSE)
               sprintf(buf + strlen(buf), "&+mPlayer Corpse&n &+YHolding:&n %d &+Ylbs&N &+m Unlooted: %s&N", j->value[0],
                       j->value[2] ? "Y" : "N");
            else
               sprintf(buf + strlen(buf), "&+bNPC Corpse&n&+Y (&n%d&+Y) Holding:&n %d &+Ylbs.&N",
                       j->value[3], j->value[0]);
            }
         break;

      case ITEM_DRINKCON:
         sprinttype(j->value[2], drinknames, buf2);
         sprintf(buf, "&+YHolds: &N%d  &+YContains:&N %d  &+YPoisoned:&N %d  &+YLiquid:&N %s",
                 j->value[0], j->value[1], j->value[3], buf2);
         break;

      case ITEM_NOTE:
         sprintf(buf, "&+YTongue:&N %d", j->value[0]);
         break;

      case ITEM_KEY:
         sprintf(buf, "&+YKeytype:&N %d  &+YBreak Chance:&N %d &+RProcVal: &N%d", j->value[0], j->value[1], j->value[2]);
         break;

      case ITEM_FOOD:
         sprintf(buf, "&+YMakes full:&N %d  &+YPoisoned:&N %d  &+RProcVal: &N%d",
                 j->value[0], j->value[3], j->value[1]);
         break;

      case ITEM_MONEY:
         sprintf(buf, "&+YCopper:&N %d  &+YSilver:&N %d  &+YGold:&N %d  &+YPlatinum:&N %d",
                 j->value[0], j->value[1], j->value[2], j->value[3]);
         break;

      case ITEM_WORN:
         sprintf(buf, "&+rWarmth:&N %d &+YPrestige:&N %d", j->value[1], j->value[2]);
         break;

      case ITEM_TELEPORT:
         i = real_room(j->value[0]);
         if(j->value[1] >= 1) /* patch if command was left as 0 -Azuth */
            sprintf(buf, "&+YTo room: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] %s  Charges: [&N%d&+Y]  MinLevel: [&N%d&+Y] MaxLevel: [&N%d&+Y]&N",
                    j->value[0], ((i > 1) && (i <= top_of_world)) ? world[real_room(j->value[0])].name : "",
                    j->value[1], command[(int) j->value[1] - 1], j->value[2], j->value[3], j->value[4]);
         else
            sprintf(buf, "&+YTo room: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] &+RError&+Y  Charges: [&N%d&+Y]  MinLevel: [&N%d&+Y] MaxLevel: [&N%d&+Y]",
                    j->value[0], ((i > 1) && (i <= top_of_world)) ? world[real_room(j->value[0])].name : "",
                    j->value[1], j->value[2], j->value[3], j->value[4]);
         break;

      case ITEM_POISON:
         sprintf(buf, "&+YPoison Type: &N%s  &+YPoison Level:&N %d\n"
                 "&+YRemaining Applications:&N %d  &+YHits/Application:&N %d",
                 poison_types[j->value[0]], j->value[1],
                 j->value[2], j->value[3]);
         break;

      case ITEM_SUMMON:
         i = real_mobile(j->value[1]);
         if(j->value[0] >= 1) /* patch if command was left as 0 -Azuth */
            sprintf(buf, "&+YSummon Mobile: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] %s  Charges: [&N%d&+Y]  "
                    "Summon Type: [&N%d&+Y]",
                    j->value[1], (i == NOWHERE) ? "NONE" : mob_index[i].desc2,
                    j->value[0], command[(int) j->value[0] - 1], j->value[2], j->value[3]);
         else
            sprintf(buf, "&+YSummon Mobile: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] &+RError&+Y  Charges: [&N%d&+Y]  "
                    "Summon Type: [&N%d&+Y]",
                    j->value[1], (i == NOWHERE) ? "NONE" : mob_index[i].desc2,
                    j->value[0], j->value[2], j->value[3]);

         break;

      case ITEM_SWITCH:
         i = real_room(j->value[1]);
         if(j->value[0] >= 1) /* patch if command was left as 0 -Azuth */
            sprintf(buf, "&+YRoom w/ blocked exit: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] %s  Direction: &+R%5s  &+YSwitch-Type: [&N%d&+Y]",
                    j->value[1], ((i > 1) && (i <= top_of_world)) ? world[real_room(j->value[1])].name : "",
                    j->value[0], command[(int) j->value[0] - 1], ((j->value[2] >= 0) && (j->value[2] < 6)) ?
                    dirs[j->value[2]] : "&-RBUG!&N", j->value[3]);
         else
            sprintf(buf, "&+YRoom w/ blocked exit: [&N%d&+Y]&N %s\n"
                    "&+YCommand #: [&N%d&+Y] &+RError&+Y  Direction: &+R%5s  &+YSwitch-Type: [&N%d&+Y]",
                    j->value[1], ((i > 1) && (i <= top_of_world)) ? world[real_room(j->value[1])].name : "",
                    j->value[0], ((j->value[2] >= 0) && (j->value[2] < 6)) ?
                    dirs[j->value[2]] : "&-RBUG!&N", j->value[3]);
         break;

      case ITEM_PSP_CRYSTAL:
         sprintf(buf, "&+YMax Charge: [&N%d&+Y]&N&+Y Current Charge [&N%d&+Y]&N",
                 j->value[0], j->value[1]);
         break;

      case ITEM_DISTRIBUTION:
         sprintf(buf, "&+YDistribution Odds  : &+G1 in %d&n\n", j->value[0]);
         sprintf(buf + strlen(buf), "&+YDistribution Rooms : &+G%d - %d&n\n", j->value[1], j->value[2]);
         sprintf(buf + strlen(buf) ,"&+YDistro Sector Types: &+G");
         for(sector = 0; sector < 31; sector++)
            if(IS_SET(j->value[3], power_table[sector]))
               {
               sprintf(buf2, "%s ", sector_types[sector]);
               strcat(buf, buf2);
               }
         break;

      default:
         sprintf(buf, "&+YValues 0-3: [&N%d&+Y] [&N%d&+Y] [&N%d&+Y] [&N%d&+Y]",
                 j->value[0], j->value[1], j->value[2], j->value[3]);
         break;
      }

   strcat(buf, "\n&+YProcs:&N ");
   if((j->R_num >= 0) && obj_index[j->R_num].func)
      {
      fn = obj_index[j->R_num].func;
      while(fn && (fn->type == FUNC_OBJ))
         {
         strcat(buf, " &+yO:&n");
         strcat(buf, fn->name);
         fn = fn->next;
         }
      strcat(buf, "\n");
      }
   else
      strcat(buf, "No\n");

   strcat(o_buf, buf);

   for(i = 0; i < MAX_OBJ_AFFECT; i++)
      {
      if(j->affected[i].location != APPLY_NONE)
         {
         sprinttype(j->affected[i].location, apply_types, buf2);
         sprintf(o_buf + strlen(o_buf), "   &+YAffects: &+c%s&+y By &N%d\n", buf2, j->affected[i].modifier);
         }
      }

   /* Stat the obj trap.. */
   if(j->trap_eff)
      {
      sprintf(o_buf + strlen(o_buf), "&+RTrap     :&N &+RCharges: &N%d&+R   Type: &N%d   &+RLevel: &N%d    &+RDamage:&N %dd%d\n",
              j->trap_charge, j->trap_dam, j->trap_level, j->trap_dnum, j->trap_dsize);
      sprintbit(j->trap_eff, trap_bits, buf2);
      sprintf(o_buf + strlen(o_buf), "&+RAffects  :&N %s\n", buf2);
      }

   if(j->events)
      {
      strcat(o_buf, "&+YEvents:\n&+Y-------\n");
      for(e1 = j->events; e1; e1 = e1->next)
         {
         if(event_time(e1, T_DAYS))
            sprintf(o_buf + strlen(o_buf), "%6d&+Y days,&n %d&+Y hours,&n %s%s&+Y.\n",
                    event_time(e1, T_DAYS), (event_time(e1, T_HOURS) - event_time(e1, T_DAYS)*24), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
         else if(event_time(e1, T_HOURS))
            sprintf(o_buf + strlen(o_buf), "%6d&+Y hours,&n %d&+Y minutes,&n %s%s&+Y.\n",
                    event_time(e1, T_HOURS), (event_time(e1, T_MINS) - event_time(e1, T_HOURS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
         else if(event_time(e1, T_MINS))
            sprintf(o_buf + strlen(o_buf), "%6d&+Y minutes,&n %d&+Y seconds,&n %s%s&+Y.\n",
                    event_time(e1, T_MINS), (event_time(e1, T_SECS) - event_time(e1, T_MINS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
         else
            sprintf(o_buf + strlen(o_buf), "%6d&+Y seconds,&n %s%s&+Y.\n",
                    event_time(e1, T_SECS), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
         }
      strcat(o_buf, "\n");
      }

   if(j->contains)
      strcat(o_buf, "\n&+YContains:\n");

   if(extra_desc)
      {
      if(j->ex_description)
         {
         strcpy(buf, "\n&+YExtra description(s):\n&+Y----------\n");
         for(desc = j->ex_description; desc; desc = desc->next)
            {
            if(desc->description)
               strcat(buf, desc->description);
            else
               strcat(buf, "(NULL)  *FIX IT*\n");
            }

         strcat(buf, "&+Y----------\n");
         strcat(o_buf, buf);
         }
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);

   if(j->contains)
      list_obj_to_char(j->contains, ch, 2, TRUE);

   if(t_obj)
      extract_obj(t_obj);
}

void stat_char(P_char ch, char *arg, int paging, int extra_desc)
{
   int i, i2, i3, i4, tmpac, count;
   Memory *mem = NULL;
   P_event e1 = NULL;
   char *eventFnName = NULL;
   struct affected_type *aff = NULL;
   struct follow_type *fol = NULL;
   struct func_attachment *fn = NULL;
   struct moneyStruct *money = NULL;
   P_char k = NULL, t_mob = NULL;
   struct time_info_data playing_time, ch_age;
   char buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "", buf2[MAX_STRING_LENGTH] = "";
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      {
      if((i = real_mobile(atoi(arg))) == NOWHERE)
         {
         send_to_char("Illegal mob number.\n", ch);
         return;
         }

      /* load one to stat, extract after statting */
      t_mob = read_mobile(i, REAL);

      /* skip the finding part, we'll use this one to prevent silliness */
      k = t_mob;
      if(!t_mob)
         {
         logit(LOG_DEBUG, "do_stat(): mob %d [%d] not loadable", i, mob_index[i].virtual);
         send_to_char("Error loading mob to stat.\n", ch);
         return;
         }
      else
         {
         char_to_room(t_mob, 0, -2);
         if(zone_table[world[t_mob->in_room].zone].hometown)
            GET_EXP(t_mob) /= 10; /* can this ever be hit? -Azuth */
         }
      }

   if(!k && !(k = get_char_vis(ch, arg, TRUE)))
      {
      send_to_char("No such character.\n", ch);
      if(t_mob)
         extract_char(t_mob);
      return;
      }

   switch(k->player.sex)
      {
      case SEX_NEUTRAL:
         strcpy(buf, "Neuter");
         break;
      case SEX_MALE:
         strcpy(buf, "&+BMale&N");
         break;
      case SEX_FEMALE:
         strcpy(buf, "&+RFemale&N");
         break;
      default:
         strcpy(buf, "&+MILLEGAL-SEX!!&N");
         break;
      }

   if(k->in_room != NOWHERE)
      sprintf(buf1, "  &+YIn room [&N%d&+Y]", world[k->in_room].number);
   else
      sprintf(buf1, "  &+YIn room [&+W-1&+Y]");

   sprintf(buf2, "%s %s%s  ", buf, IS_PC(k) ? "&+YPC" : "&+GMOB", t_mob ? "" : buf1);

   if(IS_NPC(k))
      sprintf(buf, "Numbers: [&N%d&+Y] (&N%d&+Y)   # in game: &n%d\n",
              mob_index[k->nr].virtual, k->nr,
              (mob_index[k->nr].number - ((t_mob != NULL) ? 1 : 0)));
   else
      sprintf(buf, " Name: &N%s\n", GET_NAME(k));

   strcat(buf2, buf);
   strcpy(o_buf, buf2);

   if(IS_NPC(k))
      sprintf(buf, "&+YName: &N%s\n&+YKeywords: &N%s\n&+YDescription:\n%s\n",
              k->player.short_descr, GET_NAME(k), k->player.long_descr);
   else
      sprintf(buf, "&+YTitle: &N%s\n&+YDescription:\n%s\n",
              k->only.pc->title ? k->only.pc->title : "&+rNone",
              k->player.short_descr ? k->player.short_descr : "&+rNone");
   strcat(o_buf, buf);

   if(IS_NPC(k))
      sprintf(buf2, "&+Y+(&N%s&+Y)", comma_string((int) (GET_LEVEL(k) * GET_HIT(k) * .4)));
   else
      {
      if((GET_CLASS(k) < 1) || (GET_CLASS(k) >= TOTALCLASS) || (GET_LEVEL(k) < 1) || IS_TRUSTED(k))
         strcpy(buf1, "Unknown");
      else
         strcpy(buf1, comma_string((int) (EXP_TABLE(k, 1) - GET_EXP(k))));
      sprintf(buf2, "&+Y Exp to Level: &N%s", IS_TRUSTED(k) ? "Unknown" : buf1);
      }

   sprintf(buf, "&+YLevel: &N%d  &+YExperience: &N%s %s  &+YAlignment [&N%d&+Y]\n",
           GET_LEVEL(k), comma_string((int) GET_EXP(k)), buf2, GET_ALIGNMENT(k));
   strcat(o_buf, buf);

   sprinttype(k->player.race, race_types, buf2);
   sprintf(buf, "&+YRace: &N%s  &+YClass: &N", buf2);
   sprinttype(k->player.class, class_types, buf2);
   strcat(buf, buf2);

   if(IS_PC(k))
      sprintf(buf2, "  &+YHometown: &N%d  &+YBirthplace: &N%d &+YPrestige:&N "
              "%d\n", GET_HOME(k), GET_BIRTHPLACE(k), GET_PRESTIGE(k));
   else
      sprintf(buf2, "  &+YHometown: &N%d  &+YBirthplace: &N%d &+YPrestige:&N %d\n",
              GET_HOME(k), GET_BIRTHPLACE(k), GET_PRESTIGE_BONUS(k));
   strcat(buf, buf2);
   strcat(o_buf, buf);

   if(IS_PC(k))
      {
      age(&ch_age, k);
      sprintf(buf, "&+YAge: &N%4d &+Yyears  &N%2d &+Ymonths  &N%2d &+Ydays  &N%2d &+YHours\n",
              ch_age.year, ch_age.month, ch_age.day, ch_age.hour);
      strcat(o_buf, buf);

      real_time_passed(&playing_time, (int) (k->player.time.played + (time(0) - k->player.time.logon)), 0);
      sprintf(buf, "&+YPlayed:  &N%3d &+Ydays  &N%2d &+Yhours  &N%2d &+Yminutes\n",
              playing_time.day, playing_time.hour, playing_time.minute);
      strcat(o_buf, buf);

      real_time_passed(&playing_time, time(0), k->player.time.logon);
      sprintf(buf, "&+YSession: &N%3d &+Ydays  &N%2d &+Yhours  &N%2d &+Yminutes\n",
              playing_time.day, playing_time.hour, playing_time.minute);
      strcat(o_buf, buf);
      }
   else
      {
      real_time_passed(&playing_time, time(0), k->player.time.birth);
      sprintf(buf, "&+YLived: &N%2d &+Ydays  &N%2d &+Yhours  &N%2d &+Yminutes\n",
              playing_time.day, playing_time.hour, playing_time.minute);
      strcat(o_buf, buf);
      }

   strcat(o_buf, "      &+gCur (Bas)      Cur (Bas)\n");

   for(i = 0, i3 = 0; i < MAX_WEAR; i++)
      {
      if(k->equipment[i])
         i3++;
      }
   i2 = GET_HEIGHT(k);
   i = i2 / 12;
   i2 -= i * 12;

   sprintf(buf, "&+YStr: &n%3d&+Y (&n%3d&+Y)    Pow: &n%3d&+Y (&n%3d&+Y)    Height: &n%3d&+Y\' &n%2d&+Y\" (&n%d&+Yin)\n",
           GET_C_STR(k), k->base_stats.Str, GET_C_POW(k), k->base_stats.Pow, i, i2, GET_HEIGHT(k));
   strcat(o_buf, buf);

   sprintf(buf, "&+YDex: &n%3d&+Y (&n%3d&+Y)    Int: &n%3d&+Y (&n%3d&+Y)    Weight: &n",
           GET_C_DEX(k), k->base_stats.Dex, GET_C_INT(k), k->base_stats.Int);
   strcat(o_buf, buf);

   if(GET_WEIGHT(k) > 9999)
      sprintf(buf, "%4.1f&+Y tons\n", (float) (GET_WEIGHT(k) / 2000.));
   else
      sprintf(buf, "%4d&+Y lbs\n", GET_WEIGHT(k));
   strcat(o_buf, buf);

   sprintf(buf, "&+YAgi: &n%3d&+Y (&n%3d&+Y)    Wis: &n%3d&+Y (&n%3d&+Y)\n",
           GET_C_AGI(k), k->base_stats.Agi, GET_C_WIS(k), k->base_stats.Wis);
   strcat(o_buf, buf);

   sprintf(buf, "&+YCon: &n%3d&+Y (&n%3d&+Y)    Cha: &n%3d&+Y (&n%3d&+Y)   Equipped Items: &n%3d&+Y     Carried weight:&n%5d\n",
           GET_C_CON(k), k->base_stats.Con, GET_C_CHA(k), k->base_stats.Cha, i3, IS_CARRYING_W(k));
   strcat(o_buf, buf);

   sprintf(buf, "&+YKar: &n%3d&+Y (&n%3d&+Y)    Luc: &n%3d&+Y (&n%3d&+Y)    Carried Items: &n%3d&+Y   Max Carry Weight:&n%5d\n",
           GET_C_KARMA(k), k->base_stats.Karma, GET_C_LUCK(k), k->base_stats.Luck, IS_CARRYING_N(k), CAN_CARRY_W(k));
   strcat(o_buf, buf);

   i = GET_C_STR(k) + GET_C_DEX(k) + GET_C_AGI(k) + GET_C_CON(k) +
       GET_C_POW(k) + GET_C_INT(k) + GET_C_WIS(k) + GET_C_CHA(k);

   i2 = k->base_stats.Str + k->base_stats.Dex + k->base_stats.Agi + k->base_stats.Con +
        k->base_stats.Pow + k->base_stats.Int + k->base_stats.Wis + k->base_stats.Cha;

   sprintf(buf, "&+YAvg: &n%3d&+Y (&n%3d&+Y)  Total mod: (&n%3d&+Y)              Load modifier: &n%3d\n",
           (int) (i / 8), (int) (i2 / 8), (i - i2), load_modifier(k));
   strcat(o_buf, buf);

   /*
    * Print out NPC spell slot information: # spells left in each circle and # of
    * spells left to regain overall. - SKB 7 Apr 1995
    */

   /* Nah, print out everyone's.  Why be class biased? - DA 2/13/01 */

   if(IS_NPC(k))
      {
      sprintf(buf, "&+mSpells left in circles:  (%d to regain)\n&+m", k->only.npc->spells_in_circle[0]);
      strcat(o_buf, buf);

      for(i4 = 1; i4 < MAX_CIRCLE + 1; i4++)
         {
         sprintf(buf, "%d:%d/%d", i4, k->only.npc->spells_in_circle[i4], spl_table[GET_LEVEL(k)][i4 - 1]);
         sprintf(o_buf + strlen(o_buf), "%-8s", buf);
         }
      strcat(o_buf, "\n");
      }

   if(IS_PC(k))
      money = getCarriedMoneyStruct(k);

   sprintf(buf, "&+YHits: [&N%5d&+Y/&N%5d&+Y/&N%5d&+Y+&N%3d&+Y]&+W   Pcoins: &N%5d",
           GET_HIT(k), GET_MAX_HIT(k), hit_limit(k), hit_regen(k), GET_PLATINUM(k));
   if(IS_PC(k))
      sprintf(buf, "%s  &+WPbank: &N%5d  &+WPbag: &N%5d\n", buf, GET_BALANCE_PLATINUM(k), money->platinum);
   else
      sprintf(buf, "%-52s  &+YTimer: &N%d\n", buf, k->specials.timer);
   strcat(o_buf, buf);

   sprintf(buf, "&+YMana: [&N%5d&+Y/&N%5d&+Y/&N%5d&+Y+&N%3d&+Y]   Gcoins: &N%5d",
           GET_MANA(k), GET_MAX_MANA(k), k->points.base_mana, mana_regen(k), GET_GOLD(k));

   if(IS_PC(k))
      sprintf(buf, "%s  &+YGbank: &N%5d  &+YGbag: &N%5d\n", buf, GET_BALANCE_GOLD(k), money->gold);
   else
      {
      sprintf(buf, "%-50s  &+YProcs:&N ", buf);
      if((k->nr >= 0) && mob_index[k->nr].func)
         {
         fn = mob_index[k->nr].func;
         while(fn && fn->type && (fn->type == FUNC_MOB))
            {
            strcat(buf, " &+yM:&n");
            strcat(buf, fn->name);
            fn = fn->next;
            }
         strcat(buf, "\n");
         }
      else
         strcat(buf, "None\n");
      }
   strcat(o_buf, buf);

   sprintf(buf, "&+YMove: [&N%5d&+Y/&N%5d&+Y/&N%5d&+Y+&N%3d&+Y]&n   Scoins: %5d",
           GET_MOVE(k), GET_MAX_MOVE(k), move_limit(k), move_regen(k), GET_SILVER(k));
   if(IS_PC(k))
      sprintf(buf, "%s  &nSbank: %5d  &nSbag: %5d\n", buf, GET_BALANCE_SILVER(k), money->silver);
   else
      sprintf(buf, "%s \n", buf);
   strcat(o_buf, buf);

   sprintf(buf, "                                &+yCcoins: &N%5d", GET_COPPER(k));
   if(IS_PC(k))
      sprintf(buf, "%s  &+yCbank: &N%5d  &+yCbag: &N%5d\n", buf, GET_BALANCE_COPPER(k), money->copper);
   else
      strcat(buf, "\n");
   strcat(o_buf, buf);

#ifdef NEWCOMBAT
   i2 = 1;
#else
   i2 = CalcToHit(k, TYPE_UNDEFINED);
#endif

   i = BOUNDED(-100, GET_AC(k), 100);

   if(load_modifier(k) > 299)
      i += 40;
   else if(load_modifier(k) > 199)
      i += 25;
   else if(load_modifier(k) > 99)
      i += 10;

   if(GET_CLASS(k) == CLASS_MONK)
      i += (MonkAcBonus(k) * 3);

   if(AWAKE(k) && !IS_AFFECTED(k, AFF_MINOR_PARALYSIS) && !IS_AFFECTED(k, AFF_MAJOR_PARALYSIS))
      i += agi_app[STAT_INDEX(GET_C_AGI(k))].defensive;

   tmpac = BOUNDED(-100, i, 100);

   if((GET_CLASS(k) == CLASS_MONK) && (tmpac < 0))
      tmpac = 0;

   sprintf(buf, "&+YAC: &N%d&+Y(&N%d&+Y)  thAC0: &N%d &+Y  +Hit: &N%d",
           i, tmpac, i2, GET_HITROLL(k) + cc_thac0_dexMod / 100 * DexHitBonus(STAT_INDEX(GET_C_DEX(k))));
   if(IS_NPC(k) || ((GET_CLASS(k) == CLASS_MONK) && !k->equipment[WIELD] &&
                    !k->equipment[WEAR_SHIELD] && !k->equipment[HOLD] && !k->equipment[SECONDARY_WEAPON]))
      sprintf(buf, "%s   &+YUnarmed damage: &N%d&+Yd&N%d  &+Y+Dam: &N%d &+YMR: &N%d\n",
              buf, k->points.damnodice, k->points.damsizedice,
              GET_DAMROLL(k) + StrDamBonus(STAT_INDEX(GET_C_STR(k))),
              GET_MAGIC_RESISTANCE(k));
   else
      sprintf(buf, "%s  &+Y+Dam: &N%d &+YMR: &N%d\n", buf,
              GET_DAMROLL(k) + StrDamBonus(STAT_INDEX(GET_C_STR(k))),
              GET_MAGIC_RESISTANCE(k));
   strcat(o_buf, buf);

   strcat(o_buf, "&+YSaves:    Para   Rod    Petri  Breath Spell\n");
   sprintf(buf, "&+Y (actual) [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]\n",
           BOUNDED(1, (find_save(k, SAVING_PARA) + k->specials.apply_saving_throw[0] * 5), 100),
           BOUNDED(1, (find_save(k, SAVING_ROD) + k->specials.apply_saving_throw[1] * 5), 100),
           BOUNDED(1, (find_save(k, SAVING_PETRI) + k->specials.apply_saving_throw[2] * 5), 100),
           BOUNDED(1, (find_save(k, SAVING_BREATH) + k->specials.apply_saving_throw[3] * 5), 100),
           BOUNDED(1, (find_save(k, SAVING_SPELL) + k->specials.apply_saving_throw[4] * 5), 100));
   strcat(o_buf, buf);
   sprintf(buf, "&+Y (mods)   [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]  [&N%3d&+Y]\n",
           k->specials.apply_saving_throw[0],
           k->specials.apply_saving_throw[1],
           k->specials.apply_saving_throw[2],
           k->specials.apply_saving_throw[3],
           k->specials.apply_saving_throw[4]);
   strcat(o_buf, buf);

   if(IS_PC(k))
      {
      if(k->desc)
         sprinttype(k->desc->connected, connected_types, buf2);
      else
         strcpy(buf2, "");
      sprintf(buf, "&+YHunger: &N%2d  &+YThirst: &N%2d  &+YDrunk: &N%2d                      &+Y%s%s\n",
              k->specials.conditions[FULL], k->specials.conditions[THIRST], k->specials.conditions[DRUNK],
              (k->desc) ? "Connected: " : "Linkdead", buf2);
      strcat(o_buf, buf);
      }

   sprinttype(GET_POS(k), position_types, buf1);
   strcat(buf1, " ");
   sprintbit((GET_STAT(k) * 4), position_types, buf1 + strlen(buf1));
   if(IS_NPC(k))
      {
      sprinttype((k->only.npc->default_pos & 3), position_types, buf2);
      strcat(buf2, " ");
      sprintbit(((k->only.npc->default_pos & STAT_MASK) * 4), position_types, buf2 + strlen(buf2));
      sprintf(buf, "&+YPosition/Default: &N%s&+Y/&N%s", buf1, buf2);
      }
   else
      sprintf(buf, "&+YPosition: &N%s", buf1);
   sprintf(buf1, "%s  &+YFighting: %s", buf, k->specials.fighting ? C_NAME(k->specials.fighting) : "---");
   if(IS_NPC(k))
      {
      strcat(buf1, "\n");
      strcpy(buf, buf1);
      }
   else
      sprintf(buf, "%-61s  &+YTimer: &N%d\n", buf1, k->specials.timer);
   strcat(o_buf, buf);

   if(IS_NPC(k))
      {
      sprint_cbit(k->only.npc->npcact, MAX_NPCACT_BITS, action_bits, buf2);
      sprintf(buf, "&+YACT flags: &N%s\n", buf2);
      strcat(o_buf, buf);
      }
   else
      {
      sprintbit(k->only.pc->prompt, player_prompt, buf2);
      sprintf(buf, "&+YFlags (Prompt)      : &N%s\n", buf2);
      strcat(o_buf, buf);
      sprint_cbit(k->only.pc->pcact, MAX_PACT, player_bits, buf2);
      sprintf(buf, "&+YFlags (Specials Act): &N%s\n", buf2);
      strcat(o_buf, buf);
      }

   if(IS_NPC(k))
      {
      sprint_cbit(k->only.npc->aggressive, MAX_AGG_BITS, race_types, buf2);
      sprintf(buf, "&+YHates races: &N%s\n", buf2);
      strcat(o_buf, buf);
      }

   sprint_cbit(k->specials.affects, MAX_AFF_BITS, affected_bits, buf2);
   sprintf(buf, "&+YAffected by:\n");
   if(IS_AFFECTED(k, AFF_ULTRAVISION) && (k->in_room != NOWHERE) && IS_SUNLIT(k->in_room))
      strcat(buf, "Sun_Blind ");
   sprintf(buf + strlen(buf), "%s\n", buf2);
   strcat(o_buf, buf);

   sprintf(buf, "&+YFollowers:           Master is: &N%s\n", k->following ? C_NAME(k->following) : "---");
   strcat(o_buf, buf);
   for(fol = k->followers; fol; fol = fol->next)
      {
      sprintf(buf, " &N&+Y[&N%5d&+Y] %s\n", world[fol->follower->in_room].number, C_NAME(fol->follower));
      strcat(o_buf, buf);
      }

   if(IS_PC(k))
      {
      sprintf(buf, "&+YCarts owned:&N %s\n",
              k->specials.cart ? k->specials.cart->short_description : "---");
      strcat(o_buf, buf);
      sprintf(buf, "&+YLanguages known:&n\n");
      for(i = 1; i <= TONGUE_GOD; i++)
         {
         sprintf(buf + strlen(buf), "&+Y%-12s&n%3d%% ", language_names[i - 1], GET_LANGUAGE(k, i));
         if(!(i % 4))
            strcat(buf, "\n");
         }
      if(TONGUE_GOD % 4)
         strcat(buf, "\n");
      strcat(o_buf, buf);
      }

   /* Show player on mobs piss list */
   if(IS_NPC(k) && IS_CSET(k->only.npc->npcact, ACT_MEMORY))
      {
      sprintf(buf, "\n&+RAggressive To&n:\r\n&+Y--------------&n\n");
      mem = (Memory *) k->only.npc->memory;

      if(mem && mem->m_size)
         {
         for(count = 0, i = 0; count < mem->m_size; count++, i++)
            {
            if(i >= mem->m_size)
               i = 0;
            sprintf(buf2, "  %s\r\n", mem->m_names[i]);
            strcat(buf, buf2);
            }
         }

      strcat(buf, "\r\n");
      strcat(o_buf, buf);
      }

   if(k->affected)
      {
      strcat(o_buf, "&+YAffecting Spells:\n&+Y-----------------\n");
      /* tmp, why is this here??? -Azuth 5/02 */
      /*      sprintf(buf, "&+RnumSkills: %d\n", numSkills);
            strcat(o_buf, buf);*/
      /* tmp */
      for(aff = k->affected; aff; aff = aff->next)
         {
         sprintf(buf, "%13s &+Yby &N%4d &+Yfor &N%3d &+Yfrom &N'%s'\n",
                 apply_types[(int) aff->location], aff->modifier, aff->duration,
                 (aff->type <= numSkills || aff->type == SPELL_BLUR) ? spells[aff->type - 1] :
                 ((aff->type == SKILL_CON_BONUS) ? "Con Bonus" :
                  (aff->type == SKILL_RES_PENALTY) ? "Res Penalty" :
                  (aff->type == SKILL_CAMP) ? "Setting Camp" :
                  (aff->type == SKILL_PKILL_TIMEOUT) ? "engaged in pkill" :
                  (aff->type == BARD_PROTECTION) ? "song of protection" :
                  (aff->type == BARD_TRAVEL) ? "song of travel" :
                  (aff->type == BARD_REVELATION) ? "song of revelation" : "???"));

         sprint_cbit(aff->sets_affs, MAX_AFF_BITS, affected_bits, buf2);
         if(str_cmp(buf2, "NOBITS"))
            sprintf(buf, "%-61s &+YSets: &N%s\n", buf, buf2);
         else
            strcat(buf, "\n");
         strcat(o_buf, buf);
         }
      }

   if(k->events)
      {
      strcat(o_buf, "&+YEvents:\n&+Y-------\n");

      for(e1 = k->events; e1; e1 = e1->next)
         {
         switch(e1->type)
            {
            case EVENT_JUSTICE_DEBT:
            case EVENT_SCHAR_EXECUTE:
               eventFnName = getFunctionName(e1->target.t_sced->t_func);
               if(event_time(e1, T_DAYS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y days,&n %d&+Y hours,&n %s%s&+Y: %s\n",
                          event_time(e1, T_DAYS), (event_time(e1, T_HOURS) - event_time(e1, T_DAYS)*24), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)",
                          eventFnName ? eventFnName : "Unknown Function");
               else if(event_time(e1, T_HOURS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y hours,&n %d&+Y minutes,&n %s%s&+Y: %s\n",
                          event_time(e1, T_HOURS), (event_time(e1, T_MINS) - event_time(e1, T_HOURS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)",
                          eventFnName ? eventFnName : "Unknown Function");
               else if(event_time(e1, T_MINS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y minutes,&n %d&+Y seconds,&n %s%s&+Y: %s\n",
                          event_time(e1, T_MINS), (event_time(e1, T_SECS) - event_time(e1, T_MINS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)",
                          eventFnName ? eventFnName : "Unknown Function");
               else
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y seconds,&n %s%s&+Y: %s\n",
                          event_time(e1, T_SECS), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)",
                          eventFnName ? eventFnName : "Unknown Function");
               break;

            default:
               if(event_time(e1, T_DAYS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y days,&n %d&+Y hours,&n %s%s&+Y.\n",
                          event_time(e1, T_DAYS), (event_time(e1, T_HOURS) - event_time(e1, T_DAYS)*24), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
               else if(event_time(e1, T_HOURS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y hours,&n %d&+Y minutes,&n %s%s&+Y.\n",
                          event_time(e1, T_HOURS), (event_time(e1, T_MINS) - event_time(e1, T_HOURS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
               else if(event_time(e1, T_MINS))
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y minutes,&n %d&+Y seconds,&n %s%s&+Y.\n",
                          event_time(e1, T_MINS), (event_time(e1, T_SECS) - event_time(e1, T_MINS)*60), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
               else
                  sprintf(o_buf + strlen(o_buf), "%6d&+Y seconds,&n %s%s&+Y.\n",
                          event_time(e1, T_SECS), event_names[(int) e1->type], (e1->one_shot) ? "" : "&+Y(&N&+RR&+Y)");
               break;
            }
         }

      strcat(o_buf, "\n");
      }

   if(extra_desc)
      {
      if(k->player.description)
         {
         strcpy(buf, "\n&+YExtra description:\n&+Y----------\n");
         strcat(o_buf, buf);
         if(k->player.description)
            sprintf(buf, "%s\r\n", k->player.description);
         else
            strcat(buf, "(NULL)  *FIX IT*\n");

         strcat(buf, "&+Y----------\n");
         strcat(o_buf, buf);
         }
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);

   if(t_mob)
      extract_char(t_mob);
}

/* shop data on a mobile in world, similar to statting a mobile, but gives info on the shop proc, rather than
   the mob.  Due to the way things are setup, the mob must have been loaded by a zone command at bootup (or
   the shop is not setup properly).  Even if mob has been killed/purged, you can stat-by-number.  JAB */
void stat_shop(P_char ch, char *arg, int paging)
{
   int   i, i2, i3, i4, vnum;
   P_obj t_obj = NULL;
   P_char k = NULL, t_mob = NULL;
   struct func_attachment *fn = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      {
      if((i = real_mobile(atoi(arg))) == NOWHERE)
         {
         send_to_char("Illegal mob number.\n", ch);
         return;
         }

      /* load one to stat, extract after statting */
      t_mob = read_mobile(i, REAL);
      k = t_mob;
      if(!t_mob)
         {
         logit(LOG_DEBUG, "do_stat(): mob %d [%d] not loadable", i, mob_index[i].virtual);
         send_to_char("Error loading mob to stat.\n", ch);
         return;
         }
      else
         char_to_room(t_mob, 0, -2);
      }

   i2 = FALSE;
   if((!k && !(k = get_char_vis(ch, arg, TRUE))) || IS_PC(k))
      {
      send_to_char("No such character.\n", ch);
      i2 = TRUE;
      }
   else
      {
      for(i = 0; (i < number_of_shops) && (shop_index[i].keeper != k->nr); i++)
         ;

      fn = mob_index[k->nr].func;
      while(fn)
         {
         if(fn->func.ch == shop_keeper)
            break;
         fn = fn->next;
         }

      if(!fn || (i >= number_of_shops))
         {
         send_to_char("No shop data for this mob.\n", ch);
         i2 = TRUE;
         }
      }

   if(i2)
      {
      if(t_mob)
         extract_char(t_mob);
      return;
      }

   sprintf(o_buf, "&+Y%sShop: &N%d&+Y  for %s\n\n",
           shop_index[i].shop_is_roaming ? "Roaming " : "", mob_index[k->nr].virtual,
           k->player.short_descr ? k->player.short_descr : "&+rNone");
   sprintf(o_buf + strlen(o_buf),
           "&+YAttackable?: %c   Allow Casting?: %c  &+YHours: &Ncccccccccccccccccccccccc\n",
           shop_index[i].shop_killable ? 'Y' : 'N', shop_index[i].magic_allowed ? 'Y' : 'N');

   for(i3 = 0, i4 = strlen(o_buf) - 25; i3 < 24; i3++)
      {
      if(IS_CSET(shop_index[i].hours, i3))
         o_buf[i4 + i3] = ((i3 % 12) % 10) + 48;
      }

   sprintf(o_buf + strlen(o_buf), "&+YSells for: &N%d%%&+Y, Buys for: &N%d%%&+Y, Produces &N%d &+YItems\n",
           shop_index[i].greed, (int) ((shop_index[i].greed * 100) / (shop_index[i].profit + 100)),
           shop_index[i].production_count);

   /* various messages that shop has stored. */
   sprintf(o_buf + strlen(o_buf), "&+YOpening      :&N %s\n", shop_index[i].SM_shop_open);
   sprintf(o_buf + strlen(o_buf), "&+YClosing      :&N %s\n", shop_index[i].SM_shop_close);
   sprintf(o_buf + strlen(o_buf), "&+YDon't have   :&N %s\n", shop_index[i].SM_shop_not_have);
   sprintf(o_buf + strlen(o_buf), "&+YCh don't have:&N %s\n", shop_index[i].SM_buyer_not_have);
   sprintf(o_buf + strlen(o_buf), "&+YToo poor     :&N %s\n", shop_index[i].SM_shop_broke);
   sprintf(o_buf + strlen(o_buf), "&+YCh too poor  :&N %s\n", shop_index[i].SM_buyer_broke);
   sprintf(o_buf + strlen(o_buf), "&+YWrong Type   :&N %s\n", shop_index[i].SM_not_buy);
   sprintf(o_buf + strlen(o_buf), "&+YSOLD!        :&N %s\n", shop_index[i].SM_sell_to);
   sprintf(o_buf + strlen(o_buf), "&+YBought       :&N %s\n", shop_index[i].SM_buy_from);

   sprintf(o_buf + strlen(o_buf), "\n&+YRacist       :&N %s\n&+YCHEATS: ", shop_index[i].SM_hates);
   for(i2 = 0; i2 < MAX_BIGOT_BITS; i2++)
      if(IS_CSET(shop_index[i].cheats, i2))
         sprintf(o_buf + strlen(o_buf), " %s", bigot_bits[i2]);

   strcat(o_buf, "\n&+YHATES:  ");
   for(i2 = 0; i2 < MAX_BIGOT_BITS; i2++)
      {
      if(IS_CSET(shop_index[i].hates, i2))
         sprintf(o_buf + strlen(o_buf), " %s", bigot_bits[i2]);
      }

   strcat(o_buf, "\n\n&+YItems traded: &N");
   for(i2 = 0; i2 < LAST_ITEM_TYPE; i2++)
      {
      if(IS_CSET(shop_index[i].types, i2))
         sprintf(o_buf + strlen(o_buf), "%s ", item_types[i2]);
      }

   strcat(o_buf, "\n\n&+YItems produced:\n");
   for(i2 = 0; i2 < shop_index[i].production_count; i2++)
      {
      if((t_obj = read_object(shop_index[i].production[i2], REAL)))
         {
         vnum = t_obj->R_num >= 0 ? obj_index[t_obj->R_num].virtual : 0;

         sprintf(o_buf + strlen(o_buf), "&+Y[&N%5d&+Y] (&N%5d&+Y)&N %12s %s\n",
                 vnum, t_obj->R_num, item_types[(int) t_obj->type],
                 t_obj->short_description ? t_obj->short_description : "None");
         extract_obj(t_obj);
         }
      else
         {
         logit(LOG_DEBUG, "do_stat(): obj %d [%d] not loadable (shop stat)",
               shop_index[i].production[i2], obj_index[shop_index[i].production[i2]].virtual);
         sprintf(o_buf + strlen(o_buf), "&+RNon-existent object: &N%d\n", shop_index[i].production[i2]);
         }
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);

   if(t_mob)
      extract_char(t_mob);
}

void stat_skill(P_char ch, char *arg, int paging)
{
   int   i, i2;
   P_char k = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      {
      send_to_char("Skill stat only works on PCs.\n", ch);
      return;
      }

   if(!(k = get_char_in_game_vis(ch, arg, TRUE)))
      {
      send_to_char("No such character.\n", ch);
      return;
      }

   if(IS_NPC(k))
      {
      send_to_char("Skill stat only works on PCs.\n", ch);
      return;
      }

   sprintf(o_buf, "&+gSkill statistics for %s\n&+YCurt Limt Max   Name\n", GET_NAME(k));

   for(i = 0; i < MAX_SKILLS; i++)
      {
      i2 = pindex2Skill[i];
      if(i2 < 0)
         continue;      /* unused skill */
      if(skills[i2].spell_pointer)
         continue;      /* don't list spells */
      if(!skills[i2].class[GET_CLASS(k) - 1].rlevel && !k->only.pc->skills[i].learned)
         continue;      /* class doesn't have skill and it's not setbit */
      if((skills[i2].class[GET_CLASS(k) - 1].rlevel > GET_LEVEL(k)) && !k->only.pc->skills[i].learned)
         continue;      /* they aren't high enough level, and it hasn't been setbit */

      sprintf(o_buf + strlen(o_buf), "%4d %4d %4d  %s\n",
              k->only.pc->skills[i].learned, CharMaxSkill(k, i2),
              skills[i2].class[GET_CLASS(k) - 1].maxlearn, skills[i2].name);
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}

void stat_trophy(P_char ch, char *arg, int paging)
{
   int   i2, count = 0, len = 0;
   P_char k = NULL;
   char *o_buf;
   struct Trophy_data *t_troph = NULL, *t_troph_last = NULL;


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      {
      send_to_char("Trophy stat only works on PCs.\n", ch);
      return;
      }

   k = get_char_vis(ch, arg, TRUE);
   if(!k)
      {
      send_to_char("No such character.\n", ch);
      return;
      }

   if(IS_DEAD(k) || IS_NPC(k))
      {
      send_to_char("Trophy stat only works on non-dead PCs.\n", ch);
      return;
      }

   if(GET_EXP(k) < 1)
      {
      send_to_char("Targeted feeb has 0 XP, thus, no trophies.\n", ch);
      return;
      }

   for(t_troph = k->only.pc->Trophies; t_troph; t_troph = t_troph->next)
      count++;

   o_buf = (char *)malloc(count * 50 + 1024);
   if(!o_buf)
      dump_core(); // might as well grab it here :P

   sprintf(o_buf, "&+gKill statistics for %s\n&+Y      Exp   %%   Vnum  Name\n", GET_NAME(k));

   for(t_troph = k->only.pc->Trophies; t_troph; t_troph = t_troph->next)
      {
      i2 = real_mobile(t_troph->Vnum);
      if(t_troph->XP > 0)
         {
         len = strlen(o_buf);
         sprintf(o_buf + len, "%9d %4.1f %5d  %s\n",
                 t_troph->XP, 100. * ((float) t_troph->XP / (float) (GET_EXP(k))), t_troph->Vnum,
                 ((i2 != -1) && mob_index[i2].desc2) ? mob_index[i2].desc2 : "<UNLOADED>");
         }
      else
         {
         /* bah, have bogus trophy record, let's nuke the swine now */
         //         debuglog(51, DS_AZUTH, "Bad record, t_troph_last = %s attempting to nuke mob #%d ", t_troph_last ? "!null" : "null", t_troph->Vnum);
         if(!t_troph_last)
            {
            /* this troph is k->only.pc->Trophies, easy to nuke */
            k->only.pc->Trophies = t_troph->next;
            free((char *) t_troph);
            /* in this case, we've lost our place in the list, so let's abort */
            strcat(o_buf, "\nNuked one bogus trophy, aborting list, try again.\n");
            break;
            }
         else
            {
            t_troph_last->next = t_troph->next;
            free((char *) t_troph);
            }
         }

      if(len + 256 > count * 50 + 1024)
         {
         strcat(o_buf, "\nToo many records to display, truncating...\n");
         wizlog(51, "Post to code board for Azuth: stat trophy trunc len = %d, count = %d, victim = %s",
                len, count, GET_NAME(k));
         break;
         }

      t_troph_last = t_troph;
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
   free(o_buf);
}

/* Group stats: Show detailed listing of literally everything there */
/* is to know about groups... use the name of a group member to get */
/* the appropriate output. For PC's must be name of PC in the group */
/* and for mobs can be mob name. Can also be vnum of the group */
void stat_group(P_char ch, char *arg, int paging)
{
   int   i;
   P_char k = NULL;
   P_group grp = NULL;
   P_gmember t_member = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(!(k = get_char_in_game_vis(ch, arg, TRUE)))
      {
      send_to_char("No such character.\n", ch);
      return;
      }

   if(!(grp = GET_GROUP(k)))
      {
      send_to_char("Char is not a member of a group.\n", ch);
      return;
      }

   sprintf(o_buf, "&+WPC GROUP\n");
   sprintf(o_buf + strlen(o_buf), "&+YGroup Name: &N%s\n", grp->long_descr);
   sprintf(o_buf + strlen(o_buf), "&+YDescription: &N\n%s\n", grp->description);

#ifdef NEW_GROUP_CACHE
   /* unsplit coins */
   sprintf(o_buf + strlen(o_buf),
           "&+YUnsplit Coins: &+W%4d platinum&N  &+Y%4d gold&N  &n%4d silver&N  &+y%4d copper&N\n",
           GET_GROUP_PLATINUM(grp), GET_GROUP_GOLD(grp), GET_GROUP_SILVER(grp), GET_GROUP_COPPER(grp));
#endif

   i = 0;
   /* count total members */
   for(t_member = grp->members; t_member; t_member = t_member->next, i++)
      ;

   /* two versions..one for mobs...and one for PC's */
   sprintf(o_buf + strlen(o_buf), "&+YMembers: [&N%2d&+Y] %39c[vnum]: In Room:\n", i, ' ');
   for(t_member = grp->members; t_member; t_member = t_member->next)
      {
      if(t_member->this == grp->leader)
         sprintf(o_buf + strlen(o_buf), "&+W(Leader)&N ");
      else
         sprintf(o_buf + strlen(o_buf), "         ");

      // Display Class/Race/Level of members -- CRM
      if(IS_PC(t_member->this))
         {
         sprintf(o_buf + strlen(o_buf), "%-23s", GET_NAME(t_member->this));
         sprintf(o_buf + strlen(o_buf), "  %2d [%3s&n] %-14.14s", GET_LEVEL(t_member->this),
                 class_abbrev[GET_CLASS(t_member->this)], race_names[GET_RACE(t_member->this)]);
         }

      if(IS_NPC(t_member->this))
         {
         sprintf(o_buf + strlen(o_buf), "%-43s", GET_NAME(t_member->this));
         sprintf(o_buf + strlen(o_buf), " &N&+W[&N%5d&+W]  &N%5d\n",
                 mob_index[t_member->this->nr].virtual,
                 world[t_member->this->in_room].number);
         }
      else
         sprintf(o_buf + strlen(o_buf), "  %8c&N%5d\n", ' ', world[t_member->this->in_room].number);
      }

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}

/* Stat Assoc via number -- CRM */
void stat_assoc(P_char ch, char *arg, int paging)
{
   int   i;
   P_assoc assoc = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(!is_number(arg))
      {
      send_to_char("Illegal assoc number.\n", ch);
      return;
      }

   i = atoi(arg);
   assoc = find_assoc_num((ush_int) i);

   if(!assoc)
      {
      send_to_char("Invalid assoc number.\n", ch);
      return;
      }

   sprintf(o_buf, "&+YName         :&N %s&N     &+G(&N%u&+G)&N\n", assoc->name, assoc->number);
   sprintf(o_buf + strlen(o_buf), "&+YStatus        :&N %s\n", assoc_status_names[assoc->status]);

   sprintf(o_buf + strlen(o_buf), "&+YType          :&N %s\n", assoc_type_names[assoc->type]);
   sprintf(o_buf + strlen(o_buf), "&+YTotal Members :&N %d\n", assoc->num_members);
   sprintf(o_buf + strlen(o_buf), "&+YFlags         :&N ");

   for(i = 1; i < 6; i++)
      {
      if(IS_CSET(assoc->bits, i))
         sprintf(o_buf + strlen(o_buf), "%s ", assoc_bit_names[i]);
      }

   strcat(o_buf, "\n\n");
   sprintf(o_buf + strlen(o_buf), "&+YPcoins:&N %d    &+YGcoins:&N %d    &+YScoins:&N %d    &+YCcoins:&N %d&N\n",
           assoc->pcoins, assoc->gcoins, assoc->scoins, assoc->ccoins);

   strcat(o_buf, "\n\n");
   sprintf(o_buf + strlen(o_buf), "&+YMember Title     :&N %s&N\n", assoc->rank_names[RANK_MEMBER - 1]);
   sprintf(o_buf + strlen(o_buf), "&+YOfficer Title    :&N %s&N\n", assoc->rank_names[RANK_OFFICER - 1]);
   sprintf(o_buf + strlen(o_buf), "&+YSub-Leader Title :&N %s&N\n", assoc->rank_names[RANK_SUBLEADER - 1]);
   sprintf(o_buf + strlen(o_buf), "&+YLeader Title     :&N %s&N\n", assoc->rank_names[RANK_LEADER - 1]);

   strcat(o_buf, "\n&+YMembers:\n&+Y--------------------------------------&N\n");
   for(i = 0; i < assoc->num_members; i++)
      sprintf(o_buf + strlen(o_buf), "%s &+R(%s)&N\n", assoc->members[i].name, rank_names[assoc->members[i].rank]);

   strcat(o_buf, "\n");

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}

/* Handle stating the mob social action for a mob  -- Diirinka 30 Jun 97 */
void stat_social(P_char ch, char *arg, char *arg1, int paging)
{
   int   i;
   P_char k = NULL, t_mob = NULL;
   char o_buf[MAX_STRING_LENGTH] = "";


   if(!*arg)
      {
      send_to_char(STAT_SYNTAX, ch);
      return;
      }

   if(is_number(arg))
      { /* A vnum was specified... */
      i = real_mobile(atoi(arg));
      if(i == NOWHERE)
         {
         send_to_char("Illegal mob number.\n", ch);
         return;
         }
      }
   else
      { /* Otherwise they might be trying to stat a mob thats in the room */
      k = get_char_in_game_vis(ch, arg, TRUE);
      if(!k)
         {
         send_to_char("No such character.\n", ch);
         return;
         }

      if(IS_PC(k))
         {
         send_to_char("PC's do not have social definitions.\n", ch);
         return;
         }

      i = k->nr;
      }

   t_mob = read_mobile(i, REAL);
   if(!t_mob)
      {
      send_to_char("Cannot load mobile for stating. Bug a coder.\n", ch);
      return;
      }
   else
      char_to_room(t_mob, 0, -2);


   /* See if we got something defined for that mobile */
   if(!socials_index[i])
      {
      send_to_char("No behaviour definitions for this mobile.\n", ch);
      if(t_mob) // how about we extract the mob too :P 10/20/03 -Azuth
         extract_char(t_mob);
      return;
      }

   sprintf(o_buf + strlen(o_buf), "&+BSocial Definitions for: &N%s&N [%d]\n", C_NAME(t_mob), mob_index[t_mob->nr].virtual);
   strcat(o_buf, "--------------------------------------------------------------------------------\n");

   /* Narrow down what to stat */
   switch(tolower(arg1[1]))
      {
      case 'l':
         statSocList(o_buf, i);
         break;

      case 'r':
         statSocReaction(o_buf, i);
         break;

      case 'p':
         statSocPeriodic(o_buf, i);
         break;

      case 't':
         statSocTimed(o_buf, i);
         break;

      case 'g':
         statSocPath(o_buf, i);
         break;

      case 'e':
         statSocErrors(o_buf, i);
         break;

      default:
         statSocList(o_buf, i);
         statSocReaction(o_buf, i);
         statSocPeriodic(o_buf, i);
         statSocTimed(o_buf, i);
         statSocPath(o_buf, i);
         statSocErrors(o_buf, i);
         break;
      }

   if(t_mob)
      extract_char(t_mob);

   if(paging)
      page_string(ch->desc, o_buf, 1);
   else
      send_to_char(o_buf, ch);
}

void stat_house(P_char ch, char *arg, int paging)
{
#ifdef KINGDOM
   do_stathouse(ch, arg, 0);
#endif
}


void statSocList (char *o_buf, int i)
{

   struct socData_list *list;
   struct socList *elem, *socChain;
   int j;


   list = socials_index[i]->list;
   if(!list)
      return;

   sprintf (o_buf + strlen (o_buf), "&+BPeriodic Lists:&N\n");

   do
      {
      elem = list->listArray;
      for(j = 0; j < list->numActions; j++)
         {
         sprintf (o_buf + strlen (o_buf), " Periodic: %d\n", j);
         socChain = elem;
         do
            {
            sprintf (o_buf + strlen (o_buf), "  Periodic Cmd: %s [%d] Chance: %d Delay: %d Flag: %d\n",
                     (socChain->action > MAX_CMD_LIST) ? "special" : command[socChain->action - 1],
                     socChain->action,
                     socChain->chance,
                     socChain->delay,
                     socChain->flag);
            sprintf (o_buf + strlen (o_buf), "  Args: %s\n", socChain->arg);
            socChain = socChain->next;
            } while(socChain);
         elem++;
         }
      list = list->nextList;
      } while(list);

   return;
}

void statSocPeriodic (char *o_buf, int i)
{
   struct socData_periodic *socData, *socChain;

   socData = socials_index[i]->periodic;
   if(!socData)
      return;

   sprintf (o_buf + strlen (o_buf), "&+BSingle Periodics:&N\n");

   do
      {
      socChain = socData;
      do
         {
         sprintf (o_buf + strlen (o_buf), "  Periodic Cmd: %s [%d] Chance: %d Delay: %d Flag: %d\n",
                  (socChain->action > MAX_CMD_LIST) ? "special" : command[socChain->action - 1],
                  socChain->action,
                  socChain->chance,
                  socChain->delay,
                  socChain->flag);
         sprintf (o_buf + strlen (o_buf), "  Args: %s\n", socChain->arg ? socChain->arg : "None");
         socChain = socChain->chainNext;
         if(socChain)
            strcat (o_buf, " Next in chain:\n");
         } while(socChain);
      socData = socData->next;
      } while(socData);

   return;

}

void statSocReaction (char *o_buf, int i)
{
   struct socData_trigger *socData, *socChain;

   socData = socials_index[i]->trigger;
   if(!socData)
      return;

   sprintf (o_buf + strlen (o_buf), "&+BReactions:&N\n");

   do
      {
      sprintf (o_buf + strlen (o_buf), "  Trigger Command: %s [%d]\n", command[socData->trigger_action - 1], socData->trigger_action);
      socChain = socData;
      do
         {
         sprintf (o_buf + strlen (o_buf), "    Response: %s [%d]  Chance: %d  Delay: %d  Flags: %d\n",
                  (socChain->response_action > MAX_CMD_LIST) ? "special" : command[socChain->response_action - 1],
                  socChain->response_action,
                  socChain->chance,
                  socChain->delay,
                  socChain->flag);
         sprintf (o_buf + strlen (o_buf), "    Response Arg: %s\n\n", socChain->arg ? socChain->arg : "None");
         socChain = socChain->chainNext;
         } while(socChain);

      socData = socData->next;
      } while(socData);

   return;
}

void statSocTimed (char *o_buf, int i)
{
   struct socData_timed *socData;

   socData = socials_index[i]->timed;
   if(!socData)
      return;

   sprintf (o_buf + strlen (o_buf), "&+BTimed Actions:&N\n");

   return;
}

void statSocPath (char *o_buf, int i)
{
   struct socData_path *socData;

   socData = socials_index[i]->path;
   if(!socData)
      return;

   sprintf (o_buf + strlen (o_buf), "&+BPath:&N\n");

   return;
}

void statSocErrors (char *o_buf, int i)
{

   if(!socials_index[i])
      return;

   if(!socials_index[i]->error)
      return;

   sprintf (o_buf + strlen (o_buf), "&+rERRORS:&N\n");


   sprintf (o_buf + strlen (o_buf), "%s",
            socials_index[i]->error ? socials_index[i]->error : "  None\n");

   return;

}

#undef STAT_SYNTAX

typedef struct MsgStack_struct
   {
   char *msg;
   struct MsgStack_struct *next;
   } MsgStack;

typedef struct EchoElement_struct
   {
   char name[17];
   MsgStack *msgchain;
   } EchoElement;

void echo_stackpush(P_char ch, char *arg)
{
   int   x;
   char  name[17];
   bool  found = FALSE;
   MsgStack    *msgptr = NULL;
   EchoElement *element = NULL;

   // create main container if non-existent yet
   if(!EchoStack)
      EchoStack = SetCreate(5, 2);

   strcpy(name, GET_NAME(GET_PLYR(ch)));

   // find the god by name (linear search)
   for(x = 0; x < SetNitems(EchoStack); x++)
      {
      SetRetreive(EchoStack, x, (void *)&element);
      if(!element)
         continue;

      if(!strcmp(element->name, name))
         {
         found = TRUE;
         break;
         }
      }

   // if not found, create an entry, add it
   if(!found)
      {
      CREATE(element, EchoElement, 1);
      strcpy(element->name, name);
      CREATE(element->msgchain, MsgStack, 1);
      element->msgchain->next = NULL;
      element->msgchain->msg = str_dup(arg);
      EchoStack = SetAdd(EchoStack, element);
      return;
      }

   // walk msgchain until null found and add new msg
   msgptr = element->msgchain;
   while(msgptr->next)
      msgptr = msgptr->next;

   CREATE(msgptr->next, MsgStack, 1);
   msgptr->next->next = NULL;
   msgptr->next->msg = str_dup(arg);
}

void echo_stackpop(P_char ch)
{
   int   x;
   bool  found = FALSE, last = FALSE;
   char  name[17];
   MsgStack    *msgptr = NULL, *msgptr_prev = NULL;
   EchoElement *element = NULL;

   if(!EchoStack)
      {
      send_to_char("First you need to create a stack, try echo stackpush msg\n", ch);
      return;
      }

   strcpy(name, GET_NAME(GET_PLYR(ch)));

   // find the god by name (linear search)
   for(x = 0; x < SetNitems(EchoStack); x++)
      {
      SetRetreive(EchoStack, x, (void *)&element);
      if(!element)
         continue;

      if(!strcmp(element->name, name))
         {
         found = TRUE;
         break;
         }
      }

   if(!found)
      {
      send_to_char("First you need to create a stack, try echo stackpush msg\n", ch);
      return;
      }

   msgptr = element->msgchain;
   msgptr_prev = msgptr;
   while(msgptr->next)
      {
      msgptr_prev = msgptr;
      msgptr = msgptr->next;
      }

   if(msgptr_prev == msgptr)
      last = TRUE;

   msgptr_prev->next = NULL;
   free(msgptr->msg);
   free(msgptr);

   if(last)
      SetRemove(EchoStack, x, free);

   if(!SetNitems(EchoStack))
      {
      free(EchoStack);
      EchoStack = NULL;
      }
}

void echo_stackclr(P_char ch)
{
   int   x;
   bool  found = FALSE, last = FALSE;
   char  name[17];
   MsgStack    *msgptr = NULL, *msgptr_prev = NULL;
   EchoElement *element = NULL;

   if(!EchoStack)
      return;

   strcpy(name, GET_NAME(GET_PLYR(ch)));

   // find the god by name (linear search)
   for(x = 0; x < SetNitems(EchoStack); x++)
      {
      SetRetreive(EchoStack, x, (void *)&element);
      if(!element)
         continue;

      if(!strcmp(element->name, name))
         {
         found = TRUE;
         break;
         }
      }

   if(!found)
      return;

   // just pop until empty, aren't stacks cool :P
   while(!last)
      {
      msgptr = element->msgchain;
      msgptr_prev = msgptr;
      while(msgptr->next)
         {
         msgptr_prev = msgptr;
         msgptr = msgptr->next;
         }

      if(msgptr_prev == msgptr)
         last = TRUE;

      msgptr_prev->next = NULL;
      free(msgptr->msg);
      free(msgptr);
      }

   SetRemove(EchoStack, x, free);

   if(!SetNitems(EchoStack))
      {
      free(EchoStack);
      EchoStack = NULL;
      }
}

void echo_stacksend(P_char ch, P_char tch, int type)
{
   char  name[17];
   char  vbuf[MAX_STRING_LENGTH];
   int   x;
   bool  found = FALSE;
   MsgStack    *msgptr = NULL;
   EchoElement *element = NULL;
   P_desc d;

   if(!EchoStack)
      {
      send_to_char("First you need to create a stack, try echo stackpush msg\n", ch);
      return;
      }

   strcpy(name, GET_NAME(GET_PLYR(ch)));

   // find the god by name (linear search)
   for(x = 0; x < SetNitems(EchoStack); x++)
      {
      SetRetreive(EchoStack, x, (void *)&element);
      if(!element)
         continue;

      if(!strcmp(element->name, name))
         {
         found = TRUE;
         break;
         }
      }

   if(!found)
      {
      send_to_char("First you need to create a stack, try echo stackpush msg\n", ch);
      return;
      }

   switch(type)
      {
      case TO_ROOM:
         msgptr = element->msgchain;
         do
            {
            sprintf(vbuf, "%s\n", msgptr->msg);
            send_to_room(vbuf, ch->in_room);
            // off per shevy for now
            //            logit(LOG_WIZ, "%s echo %s", GET_NAME(GET_PLYR(ch)), msgptr->msg);
            msgptr = msgptr->next;
            } while(msgptr);
         break;

      case TO_ALL:
         msgptr = element->msgchain;
         do
            {
            logit(LOG_WIZ, "%s echoa %s", GET_NAME(GET_PLYR(ch)), msgptr->msg);
            msgptr = msgptr->next;
            } while(msgptr);

         for(d = descriptor_list; d; d = d->next)
            {
            if(d->connected != CON_PLYNG)
               continue;

            msgptr = element->msgchain;
            do
               {
               if(GET_LEVEL(d->character) >= GET_LEVEL(GET_PLYR(ch)))
                  {
                  sprintf(vbuf, "A[%s]", GET_NAME(GET_PLYR(ch)));
                  send_to_char(vbuf, d->character);
                  }

               sprintf(vbuf, "%s\n", msgptr->msg);
               send_to_char(vbuf, d->character);
               msgptr = msgptr->next;
               } while(msgptr);
            }
         break;

      case TO_ZONE:
         msgptr = element->msgchain;
         do
            {
            logit(LOG_WIZ, "%s echoz [%d] %s", GET_NAME(GET_PLYR(ch)), world[ch->in_room].zone, msgptr->msg);
            msgptr = msgptr->next;
            } while(msgptr);

         for(d = descriptor_list; d; d = d->next)
            {
            if(d->connected != CON_PLYNG)
               continue;

            if(world[ch->in_room].zone == world[d->character->in_room].zone)
               {
               msgptr = element->msgchain;
               do
                  {
                  if(GET_LEVEL(d->character) >= GET_LEVEL(GET_PLYR(ch)))
                     {
                     sprintf(vbuf, "Z[%s]", GET_NAME(GET_PLYR(ch)));
                     send_to_char(vbuf, d->character);
                     }

                  sprintf(vbuf, "%s\n", msgptr->msg);
                  send_to_char(vbuf, d->character);
                  msgptr = msgptr->next;
                  } while(msgptr);
               }
            }
         break;

      case TO_VICT:
         // off per shevy for now
         //         msgptr = element->msgchain;
         //         do
         //            {
         //            logit(LOG_WIZ, "%s echot %s %s", GET_NAME(GET_PLYR(ch)), GET_NAME(GET_PLYR(tch)), msgptr->msg);
         //            msgptr = msgptr->next;
         //            } while(msgptr);

         if(ch->desc)
            {
            if(IS_CSET(ch->only.pc->pcact, PLR_ECHO))
               {
               msgptr = element->msgchain;
               do
                  {
                  sprintf(vbuf, "&+WYou echot %s '&N%s&+W'\n", GET_NAME(tch), msgptr->msg);
                  send_to_char(vbuf, ch);

                  sprintf(vbuf, "%s\n", msgptr->msg);
                  send_to_char(vbuf, tch);
                  msgptr = msgptr->next;
                  } while(msgptr);
               }
            else
               {
               msgptr = element->msgchain;
               do
                  {
                  sprintf(vbuf, "%s\n", msgptr->msg);
                  send_to_char(vbuf, tch);
                  msgptr = msgptr->next;
                  } while(msgptr);

               send_to_char("Ok.\n", ch);
               }
            }
         break;

      case TO_CHAR:
         msgptr = element->msgchain;
         do
            {
            sprintf(vbuf, "%s\n", msgptr->msg);
            send_to_char(vbuf, ch);
            msgptr = msgptr->next;
            } while(msgptr);
         break;

      default:
         send_to_char("For some reason only you are seeing this echo (report it please)\n", ch);
         msgptr = element->msgchain;
         do
            {
            sprintf(vbuf, "%s\n", msgptr->msg);
            send_to_char(vbuf, ch);
            msgptr = msgptr->next;
            } while(msgptr);
      }
}

void do_echo(P_char ch, char *argument, int cmd)
{
   char vbuf[MAX_STRING_LENGTH];

   if(IS_NPC(GET_PLYR(ch)))
      return;

   while(*argument == ' ' && *argument != '\0')
      argument++;

   if(!*argument)
      send_to_char("That must be a mistake...\n", ch);
   else
      {
      if(!strncmp(argument, "stackpushne ", 12))
         echo_stackpush(ch, argument + 12);
      else if(!strncmp(argument, "stackpush ", 10))
         {
         echo_stackpush(ch, argument + 10);
         send_to_char("Current Stack:\n", ch);
         echo_stacksend(ch, NULL, TO_CHAR);
         }
      else if(!strncmp(argument, "stackpop", 8))
         {
         echo_stackpop(ch);
         send_to_char("Current Stack:\n", ch);
         echo_stacksend(ch, NULL, TO_CHAR);
         }
      else if(!strncmp(argument, "stackview", 9))
         {
         send_to_char("Current Stack:\n", ch);
         echo_stacksend(ch, NULL, TO_CHAR);
         }
      else if(!strncmp(argument, "stackclr", 8))
         {
         echo_stackclr(ch);
         send_to_char("Stack Cleared.\n", ch);
         }
      else if(!strncmp(argument, "stacksend", 9))
         echo_stacksend(ch, NULL, TO_ROOM);
      else
         { // standard 1 line echo
         sprintf(vbuf, "%s\n", argument);
         send_to_room(vbuf, ch->in_room);
         }
      }
}

void do_echoa(P_char ch, char *argument, int cmd)
{
   P_desc d;
   char vbuf[MAX_STRING_LENGTH];

   if(IS_NPC(GET_PLYR(ch)))
      return;

   while(*argument == ' ' && *argument != '\0')
      argument++;

   if(!*argument)
      {
      send_to_char("Yes, fine, we must echoa something, but what!?\n", ch);
      return;
      }

   if(!strncmp(argument, "stacksend", 9))
      {
      echo_stacksend(ch, NULL, TO_ALL);
      return;
      }

   for(d = descriptor_list; d; d = d->next)
      {
      if(d->connected != CON_PLYNG)
         continue;

      if(GET_LEVEL(d->character) >= GET_LEVEL(GET_PLYR(ch)))
         {
         sprintf(vbuf, "A[%s]", GET_NAME(GET_PLYR(ch)));
         send_to_char(vbuf, d->character);
         }

      send_to_char(argument, d->character);
      send_to_char("\n", d->character);
      }

   logit(LOG_WIZ, "%s echoa %s", GET_NAME(GET_PLYR(ch)), argument);
}

void do_echoz(P_char ch, char *argument, int cmd)
{
   P_desc d;
   char vbuf[MAX_STRING_LENGTH];

   if(IS_NPC(GET_PLYR(ch)))
      return;

   while(*argument == ' ' && *argument != '\0')
      argument++;

   if(!*argument)
      {
      send_to_char("Yes, fine, we must echoz something, but what?!\n", ch);
      return;
      }

   if(!strncmp(argument, "stacksend", 9))
      {
      echo_stacksend(ch, NULL, TO_ZONE);
      return;
      }

   for(d = descriptor_list; d; d = d->next)
      {
      if(d->connected != CON_PLYNG)
         continue;

      if(world[ch->in_room].zone == world[d->character->in_room].zone)
         {
         if(GET_LEVEL(d->character) >= GET_LEVEL(GET_PLYR(ch)))
            {
            sprintf(vbuf, "Z[%s]", GET_NAME(GET_PLYR(ch)));
            send_to_char(vbuf, d->character);
            }

         send_to_char(argument, d->character);
         send_to_char("\n", d->character);
         }
      }

   logit(LOG_WIZ, "%s echoz [%d] %s", GET_NAME(GET_PLYR(ch)), world[ch->in_room].zone, argument);
}

void do_echot(P_char ch, char *argument, int cmd)
{
   P_char vict;
   P_desc d;
   char name[MAX_INPUT_LENGTH], message[MAX_STRING_LENGTH];
   char vbuf[MAX_STRING_LENGTH];

   half_chop(argument, name, message);

   if(!*name || !*message)
      {
      send_to_char("Who do you wish to echot to??\n", ch);
      return;
      }

   vict = NULL;

   /*
   * switching to descriptor list, rather than get_char_vis, since it
   * was lagging hell out of things. JAB
    */
   for(d = descriptor_list; d; d = d->next)
      {
      if(!d->character || d->connected || !d->character->player.name)
         continue;

      if(!isname(d->character->player.name, name))
         continue;

      vict = d->character;
      break;
      }

   if(!vict)
      {
      send_to_char("No-one by that name here...\n", ch);
      return;
      }
   else if(ch == vict)
      {
      send_to_char("You try to echot yourself something.\n", ch);
      return;
      }
   else if(!vict->desc)
      {
      act("$E can't hear you.", FALSE, ch, 0, vict, TO_CHAR);
      return;
      }
   else if(!CAN_SEE(ch, vict))
      {
      send_to_char("No-one by that name here...\n", ch);
      return;
      }

   if(!strncmp(message, "stacksend", 9))
      {
      echo_stacksend(ch, vict, TO_VICT);
      return;
      }

   if(ch->desc)
      {
      if(IS_CSET(ch->only.pc->pcact, PLR_ECHO))
         {
         sprintf(vbuf, "&+WYou echot %s '&N%s&+W'\n", GET_NAME(vict), message);
         send_to_char(vbuf, ch);
         }
      else
         send_to_char("Ok.\n", ch);
      }

   strcat(message, "\n");
   send_to_char(message, vict);
}

void do_shutdow (P_char ch, char *argument, int cmd)
{
   send_to_char ("If you want to shut something down - say so!\n", ch);
}

void do_wizlock (P_char ch, char *arg, int cmd)
{
   P_desc d;
   char buf[MAX_STRING_LENGTH];
   char buf1[MAX_STRING_LENGTH];

   if(!*arg)
      {
      send_to_char ("Usage: wizlock <create | ems | connections | maxplayers>\n", ch);
      return;
      }
   one_argument (arg, buf1);

   if(is_abbrev (buf1, "create"))
      {
      if(game_locked & LOCK_CREATE)
         {
         game_locked = game_locked & ~(LOCK_CREATE);
         sprintf (buf, "&+GOutcast MUD ->&n Restrictions on new character creations lifted.\n");
         }
      else
         {
         game_locked = game_locked | LOCK_CREATE;
         sprintf (buf, "&+ROutcast MUD ->&n Game is being locked;  no more character creation.\n");
         }
      }
   else if(is_abbrev (buf1, "ems"))
      {
      if(IS_ENABLED(CODE_EMS))
         sprintf(buf, " ems 0");
      else
         sprintf(buf, " ems 1");
      do_code(ch, buf, CMD_CCONTROL);
      return;
      }
   else if(is_abbrev (buf1, "connections"))
      {
      if(game_locked & LOCK_CONNECTIONS)
         {
         game_locked = game_locked & ~(LOCK_CONNECTIONS);
         sprintf (buf, "&+GOutcast MUD ->&n Restrictions on new connections lifted.\n");
         }
      else
         {
         game_locked = game_locked | LOCK_CONNECTIONS;
         sprintf (buf, "&+ROutcast MUD ->&n Game is being locked; no more connections.\n");
         }
      }
   else if(is_abbrev (buf1, "maxplayers"))
      {
      if(game_locked & LOCK_MAX_PLAYERS)
         {
         game_locked = game_locked & ~(LOCK_MAX_PLAYERS);
         sprintf (buf, "&+GOutcast MUD ->&n Restrictions on max players lifted.\n");
         }
      else
         {
         game_locked = game_locked | LOCK_MAX_PLAYERS;
         sprintf (buf, "&+ROutcast MUD ->&n Game is limited to a MAX of %d players\n",
                  MAX_PLAYERS_BEFORE_LOCK);
         }
      }
   else
      {
      send_to_char ("Usage: wizlock <create | connections | maxplayers>\n", ch);
      return;
      }

   for(d = descriptor_list; d; d = d->next)
      {
      if(!d->connected)
         {
         send_to_char (buf, d->character);
         }
      }
}

void do_wizmsg (P_char ch, char *arg, int cmd)
{
   //  P_desc d;
   P_char tch = NULL;
   char Gbuf1[MAX_STRING_LENGTH];
   char Gbuf2[MAX_STRING_LENGTH], Gbuf3[MAX_STRING_LENGTH];
   int min_level = 0, flag = 0;
   char lcolor = 'r';

   if(!ch || IS_NPC (GET_PLYR(ch)))
      return;

   if(!IS_TRUSTED (GET_PLYR(ch)))
      {
      send_to_char ("Pardon?\n", ch);
      return;
      }
   if(IS_CSET (GET_PLYR(ch)->only.pc->pcact, PLR_WIZMUFFED))
      {
      send_to_char ("You can't hear wiz messages.\n", ch);
      return;
      }
   if(!*arg)
      {
      send_to_char ("Yes, yes, but WHAT do you want to tell them all?\n", ch);
      return;
      }
   half_chop (arg, Gbuf1, Gbuf2);

   if(is_number (Gbuf1))
      {
      flag = 1;
      min_level = atoi (Gbuf1);
      if((min_level < 51) || (min_level > FORGER))
         {
         min_level = 51;
         flag = 0;
         }
      }
   if(!flag)
      {
      strcat (Gbuf1, " ");
      strcat (Gbuf1, Gbuf2);
      }
   else
      {
      if(Gbuf2)
         strcpy (Gbuf1, Gbuf2);
      }

   min_level = BOUNDED (51, min_level, 60);

   if(min_level > 51)
      lcolor = 'R';

   /* FOR visible */

   sprintf (Gbuf2, "%s : (&+%c%d&n) [ ", GET_NAME (GET_PLYR(ch)), lcolor, min_level);
   strcat (Gbuf2, Gbuf1);
   strcat (Gbuf2, " ]\n");

   /* For invisible */

   sprintf (Gbuf3, "Someone : (&+%c%d&n) [ ", lcolor, min_level);
   strcat (Gbuf3, Gbuf1);
   strcat (Gbuf3, " ]\n");

   for(tch = PC_list; tch; tch = tch->next)
      {
      if(!IS_TRUSTED(tch))
         continue;
      if(!tch->desc && tch->only.pc->switched && (GET_LEVEL(tch) >= min_level))
         {
         if(CAN_SEE(tch, ch))
            send_to_char(Gbuf2, tch->only.pc->switched);
         else
            send_to_char(Gbuf3, tch->only.pc->switched);
         continue;
         }
      if(tch->desc && (tch->desc->connected == CON_PLYNG) && (GET_LEVEL(tch) >= min_level) &&
         !IS_CSET(tch->only.pc->pcact, PLR_WIZMUFFED))
         {
         if(CAN_SEE(tch, ch))
            send_to_char(Gbuf2, tch);
         else
            send_to_char(Gbuf3, tch);
         continue;
         }
      }

#if 0
   for(d = descriptor_list; d; d = d->next)
      {
      if((d->connected == CON_PLYNG) && (GET_LEVEL (d->character) >= min_level) &&
         !IS_CSET (d->character->only.pc->pcact, PLR_WIZMUFFED))
         {

         if(CAN_SEE (d->character, ch))
            send_to_char (Gbuf2, d->character);
         else
            send_to_char (Gbuf3, d->character);
         }
      }
#endif

}

void do_shutdown (P_char ch, char *argument, int cmd)
{
   char buf[100], arg[MAX_STRING_LENGTH];

   if(IS_NPC (ch))
      return;

   one_argument (argument, arg);

   if(!*arg)
      {
      send_to_char ("Syntax: shutdown <ok | reboot | copyover | segfault>\n", ch);
      }
   else if(!str_cmp (arg, "ok"))
      {
      sprintf (buf, "Shutdown by %s.\n", GET_NAME (ch));
      send_to_all (buf);
      logit (LOG_STATUS, buf);
      shutdownflag = 1;
      }
   else if(!str_cmp (arg, "doom"))
      {
      shutdownflag = 1;
      dump_core ();
      }
   else if(!str_cmp (arg, "reboot"))
      {
      sprintf (buf, "Reboot by %s.\n", GET_NAME (ch));
      send_to_all (buf);
      logit (LOG_STATUS, buf);
      shutdownflag = reboot = 1;
      }
   else if(!str_cmp (arg, "copyover"))
      {
      sprintf (buf, "Copyover by %s.\n", GET_NAME (ch));
      send_to_all (buf);
      logit (LOG_STATUS, buf);
      shutdownflag = copyover = 1;
      }
   else if(!str_cmp (arg, "segfault"))
      {
      sprintf (buf, "Segfault by %s.\n", GET_NAME (ch));
      logit (LOG_STATUS, buf);
      dump_core ();
      }
   else
      send_to_char ("Go shut down someone your own size.\n", ch);
}

void do_snoop (P_char ch, char *argument, int cmd)
{
   P_char victim;
   static char arg[MAX_STRING_LENGTH];

   if(!ch->desc)
      return;

   one_argument (argument, arg);

   if(!*arg)
      {
      send_to_char ("Snoop who ?\n", ch);
      return;
      }
   if(!(victim = get_char_vis (ch, arg, FALSE)))
      {
      send_to_char ("No such person around.\n", ch);
      return;
      }
   if(!victim->desc)
      {
      send_to_char ("There's no link.. nothing to snoop.\n", ch);
      return;
      }
   /* if they are already snooping the victim, give them an appropriate insult -Neb */

   if(ch->desc->snoop.snooping && (victim == ch->desc->snoop.snooping->character))
      {
      send_to_char ("Duh!  You already ARE snooping that person!\n", ch);
      return;
      }
   if(victim == ch)
      {
      send_to_char ("Ok, you just snoop yourself.\n", ch);
      if(ch->desc->snoop.snooping)
         {
         if((GET_LEVEL (ch) < 55) || (GET_LEVEL (ch) <= GET_LEVEL (ch->desc->snoop.snooping->character)))
            act ("&+C$n stops snooping you.", FALSE, ch, 0, ch->desc->snoop.snooping->character, TO_VICT);
         ch->desc->snoop.snooping->snoop.snoop_by = 0;
         ch->desc->snoop.snooping = 0;
         }
      return;
      }
   if(victim->desc->snoop.snoop_by)
      {
      send_to_char ("Busy already.\n", ch);
      return;
      }
   if((GET_LEVEL (victim) >= GET_LEVEL (ch)) &&
      (!victim->specials.consent || (victim->specials.consent != ch)))
      {
      send_to_char ("You failed.\n", ch);
      return;
      }
   send_to_char ("Ok.\n", ch);

   if(ch->desc->snoop.snooping)
      {
      if((GET_LEVEL (ch) < 55) || (GET_LEVEL (ch) <= GET_LEVEL (ch->desc->snoop.snooping->character)))
         act ("&+C$n stops snooping you.", FALSE, ch, 0, ch->desc->snoop.snooping->character, TO_VICT);
      ch->desc->snoop.snooping->snoop.snoop_by = 0;
      }
   ch->desc->snoop.snooping = victim->desc;
   victim->desc->snoop.snoop_by = ch->desc;

   if((GET_LEVEL (ch) < 55) || (GET_LEVEL (ch) <= GET_LEVEL (victim)))
      act ("&+C$n starts snooping you.", FALSE, ch, 0, victim, TO_VICT);
   return;
}


// Updated on 03-09-03 --MIAX.
void do_switch(P_char ch, char *argument, int cmd)
{
   P_char victim;
   static char arg[MAX_STRING_LENGTH];

   if(IS_NPC(ch) || !ch->desc)
      return;

   one_argument(argument, arg);

   if(!*arg)
      {
      send_to_char("Switch with who?\n", ch);
      return;
      }

   if(!(victim = get_char_in_game_vis(ch, arg, TRUE)))
      {
      send_to_char("They aren't here.\n", ch);
      return;
      }

   if(ch == victim)
      {
      send_to_char("He he he... We are jolly funny today, eh?\n", ch);
      return;
      }

   if(victim->desc || (IS_PC (victim)))
      {
      send_to_char("You can't do that, the body is already in use!\n", ch);
      return;
      }

   wizlog(GET_LEVEL(ch), "%s has switched into '%s'", GET_NAME(ch), GET_NAME(victim));
   logit(LOG_WIZ, "%s has switched into '%s'", GET_NAME(ch), GET_NAME(victim));
   send_to_char("Ok.\n", ch);

   /* added by DTS to hide switched gods */
   if(IS_TRUSTED(ch))
      {
      if(GET_WIZINVIS(ch) < 50)
         GET_WIZINVIS(ch) = 50;
      }

   ch->desc->character = victim;
   ch->desc->original = ch;
   ch->only.pc->switched = victim;

   victim->desc = ch->desc;
   ch->desc = 0;
}



// Function to see if the mob is switched.
int check_switched(P_char ch)
{
   if(!ch->desc)
      return(-1);

   if(!ch->desc->original || IS_NPC (ch->desc->original))
      {
      send_to_char ("Pardon?\n", ch);
      return(-1);
      }
   if(ch->desc->original->only.pc->switched)
      return(YES);
   else
      return(NO);
}

void do_return(P_char ch, char *argument, int cmd)
{
   if(IS_AFFECTED(ch, AFF_WRAITHFORM))
      {
      BackToUsualForm(ch);
      return;
      }

   if(!ch->desc)
      return;

   if(!ch->desc->original || IS_NPC(ch->desc->original))
      {
      send_to_char("Pardon?\n", ch);
      return;
      }

   if(IS_MORPH(ch))
      {
      send_to_char("Use \"shapechange me\" to return to your normal form.\n", ch);
      return;
      }

   if(ch->desc->original->only.pc->switched)
      {
      send_to_char("You return to your original body.\n", ch);

      ch->desc->character = ch->desc->original;
      ch->desc->original = 0;
      ch->desc->character->only.pc->switched = 0;

      ch->desc->character->desc = ch->desc;
      ch->desc = 0;
      }
   else       /* switched body due to shape change */
      send_to_char("No effect.\n", ch);
}

int forced_command = 0;

void do_force (P_char ch, char *argument, int cmd)
{
   P_desc i;
   P_char vict;
   char name[MAX_INPUT_LENGTH], to_force[MAX_INPUT_LENGTH], buf[MAX_INPUT_LENGTH + 60];

   if(IS_NPC (GET_PLYR(ch)))
      return;

   half_chop (argument, name, to_force);

   if(!*name || !*to_force)
      send_to_char ("Who do you wish to force to do what?\n", ch);
   else if(str_cmp ("all", name))
      {
      if(!(vict = get_char_in_game_vis (ch, name, TRUE)))
         {
         send_to_char ("No-one by that name here..\n", ch);
         return;
         }
      else if(!str_cmp ("quit", to_force))
         {
         send_to_char ("Cannot force that player to quit.\n", ch);
         return;
         }
      else if(!strn_cmp ("jun", to_force, 3))
         {
         send_to_char ("Cannot force that player to junk.\n", ch);
         return;
         }
      else
         {
         if((GET_LEVEL (ch) < GET_LEVEL (vict)) && IS_PC (vict))
            send_to_char ("Ok.\n", ch);
         else
            {
            sprintf (buf, "$n has forced you to '%s'.", to_force);
            act (buf, FALSE, ch, 0, vict, TO_VICT);
            wizlog (GET_LEVEL (ch), "%s has forced %s to '%s'", GET_NAME (ch), GET_NAME (vict), to_force);
            logit (LOG_FORCE, "%s has forced %s to '%s'", GET_NAME (ch), GET_NAME (vict), to_force);
            send_to_char ("Ok.\n", ch);
            forced_command = 1;
            command_interpreter (vict, to_force);
            forced_command = 0;
            }
         }
      }
   else
      {        /* force all */
      wizlog (GET_LEVEL (ch), "%s has forced all to '%s'", GET_NAME (ch), to_force);
      logit (LOG_FORCE, "%s has forced all to '%s'", GET_NAME (ch), to_force);
      for(i = descriptor_list; i; i = i->next)
         if(i->character != ch && !i->connected)
            {
            vict = i->character;
            if((GET_LEVEL (ch) >= GET_LEVEL (vict)))
               {
               sprintf (buf, "$n has forced you to '%s'.", to_force);
               act (buf, FALSE, ch, 0, vict, TO_VICT);
               forced_command = 1;
               command_interpreter (vict, to_force);
               forced_command = 0;
               }
            }
      send_to_char ("Ok.\n", ch);
      }
}


void do_load (P_char ch, char *argument, int cmd)
{
   char type[MAX_INPUT_LENGTH], num[MAX_INPUT_LENGTH];
   int l_num;
   P_char loaded;
   P_obj oloaded;

   if(IS_NPC (GET_PLYR(ch)))
      return;

   argument_interpreter (argument, type, num);

   if(!*type || !*num || !isdigit (*num))
      {
      send_to_char ("Syntax:\nload <'char' | 'obj'> <number>\n", ch);
      return;
      }
   if((l_num = atoi (num)) < 0)
      {
      send_to_char ("A NEGATIVE number??\n", ch);
      return;
      }
   if(is_abbrev (type, "char") || is_abbrev (type, "mobile"))
      {
      loaded = loadmob(l_num, ch);
      if(loaded != NULL)
         {
         wizcmd (GET_LEVEL (GET_PLYR(ch)), "%s loaded mob %s '%s' in [%d]",
                 GET_NAME (GET_PLYR(ch)), num, GET_NAME (loaded), world[ch->in_room].number);
         logit (LOG_WIZLOAD, "%s loaded mob %s '%s' in [%d]",
                GET_NAME (GET_PLYR(ch)), num, GET_NAME (loaded), world[ch->in_room].number);

         }

      }
   else if(is_abbrev (type, "object") || is_abbrev (type, "item"))
      {
      oloaded = loaditem(l_num, ch);
      if(oloaded != NULL)
         {
         wizcmd (GET_LEVEL (GET_PLYR(ch)), "%s loaded obj %s '%s' in [%d]",
                 GET_NAME (GET_PLYR(ch)), num, oloaded->short_description, world[ch->in_room].number);
         logit (LOG_WIZLOAD, "%s loaded obj %s '%s' [%d]",
                GET_NAME (GET_PLYR(ch)), num, oloaded->short_description, world[ch->in_room].number);

         }
      }
   else
      send_to_char ("That'll have to be either 'char' or 'obj'.\n", ch);
}





P_char loadmob(int number, P_char ch)
{
   int r_num = 0;
   P_char mob;

   if((r_num = real_mobile (number)) < 0)
      {
      send_to_char ("There is no monster with that number.\n", ch);
      return NULL;
      }
   mob = read_mobile (r_num, REAL);
   if(!mob)
      {
      logit (LOG_DEBUG, "do_load(): mob %d [%d] not loadable", r_num, mob_index[r_num].virtual);
      return NULL;
      }
   GET_BIRTHPLACE (mob) = world[ch->in_room].number;
   GET_HOME (mob) = GET_BIRTHPLACE (mob);
   char_to_room (mob, ch->in_room, 0);
   if(zone_table[world[mob->in_room].zone].hometown)
      {
      if(IS_NPC_GUARD (mob) && !IS_CSET(mob->only.npc->npcact, ACT_SENTINEL))
         justice_register_guard (mob);
      }
   /* added by DTS 5/19/95 to get rid of excessive hps bug */
   if(GET_HIT (mob) > GET_MAX_HIT (mob))
      GET_HIT (mob) = GET_MAX_HIT (mob);

   act ("$n makes a quaint, magical gesture with one hand.", TRUE, ch, 0, 0, TO_ROOM);
   act ("$n has created $N!", FALSE, ch, 0, mob, TO_ROOM);
   act ("You have created $N!", FALSE, ch, 0, mob, TO_CHAR);
   return mob;
}


P_obj loaditem(int number, P_char ch)
{

#ifdef ARTIFACT
   char buf[MAX_STRING_LENGTH];
   int arti;
#endif
   int r_num = 0;
   P_obj obj;



   if((r_num = real_object (number)) < 0)
      {
      send_to_char ("There is no object with that number.\n", ch);
      return NULL;
      }
   obj = read_object (r_num, REAL);
   if(!obj)
      {
      logit (LOG_DEBUG, "do_load(): obj %d [%d] not loadable", r_num, obj_index[r_num].virtual);
      return NULL;
      }
#ifdef ARTIFACT
   /* god load is allowed if no other objects exist. */
   /* if not owned, then item is trans'ed to God */
   /* if owned, then owner info is given */
   if(art_index)
      {
      if(IS_SET (obj_index[obj->R_num].spec_flag, IDX_ARTIFACT))
         {
         if(artifactIsOwned (obj_index[obj->R_num].virtual))
            {
            arti = searchForArtifact (obj_index[obj->R_num].virtual);
            if(!strcmp (C_NAME (ch), art_index[arti].owner))
               {
               act ("You already possess that artifact!", TRUE, ch, obj, 0, TO_CHAR);
               extract_obj (obj);
               return NULL;
               }
            else
               {
               /* give info regarding owner */
               sprintf (buf, "Artifact [$p] owned by %s.", art_index[arti].owner);
               act (buf, TRUE, ch, obj, 0, TO_CHAR);
               extract_obj (obj);
               return NULL;
               }
            }
         else
            {
            /* artifact not owned: handle trans */
            if(obj_index[obj->R_num].number > 1)
               {
               act ("&+RArtifact already in game!", TRUE, ch, obj, 0, TO_CHAR);
               strcpy (buf, obj->name);
               extract_obj (obj);
               do_where (ch, buf, 0);
               return NULL;
               }
            /* final case loads the artifact (default) */
            }
         }
      }
#endif

   act ("$n makes a strange magical gesture.", TRUE, ch, 0, 0, TO_ROOM);
   act ("$n has created $p!", TRUE, ch, obj, 0, TO_ROOM);
   act ("You have created $p!", FALSE, ch, obj, 0, TO_CHAR);

   if(IS_SET (obj->wear_flags, ITEM_TAKE))
      obj_to_char (obj, ch);
   else
      obj_to_room (obj, ch->in_room);
   return obj;
}




/* clean a room of all mobiles and objects */
void do_purge (P_char ch, char *argument, int cmd)
{
   P_char vict, next_v;
   P_obj obj, next_o;
   char name[MAX_INPUT_LENGTH];

   if(IS_NPC (ch))
      return;

   one_argument (argument, name);

   if(*name)
      {       /* argument supplied. destroy single object or char */
      if((vict = get_char_room_vis (ch, name)))
         {
         if((GET_LEVEL (vict) >= GET_LEVEL (ch)) && IS_PC (vict))
            {
            send_to_char ("Oh no you don't!\n", ch);
            return;
            }
         if(IS_PC (vict) && (GET_LEVEL (ch) < (MAXLVL - 2)))
            {
            send_to_char ("You are too lame to be able to purge chars!\n", ch);
            return;
            }
         act ("$n disintegrates $N, who is reduced to a small pile of ashes.", FALSE, ch, 0, vict, TO_NOTVICT);
         wizlog (GET_LEVEL (ch), "%s has purged %s [%d]",
                 GET_NAME (ch), GET_NAME (vict), world[ch->in_room].number);
         logit (LOG_WIZ, "%s has purged %s [%d]", GET_NAME (ch), GET_NAME (vict), world[ch->in_room].number);
         if(IS_NPC (vict))
            {
            extract_char (vict);
            vict = NULL;
            }
         else
            {
            /* player will lose all objects! */
            /* @@ */
            if(ch->specials.cart)
               {
               ch->specials.cart->owner = NULL;
               ch->specials.cart = NULL;
               writeCart (ch);
               }
            /* @@ */
            writeCharacter (vict, 2, NOWHERE);
            if(vict->desc)
               close_socket (vict->desc);
            vict->desc = 0;
            extract_char (vict);
            vict = NULL;
            }
         }
      else if((obj = get_obj_in_list_vis (ch, name, world[ch->in_room].contents)))
         {
         act ("$n destroys $p.", TRUE, ch, obj, 0, TO_ROOM);
         wizlog (GET_LEVEL (ch), "%s has purged %s [%d]",
                 GET_NAME (ch), obj->short_description ? obj->short_description : "No description", world[ch->in_room].number);
         logit (LOG_WIZ, "%s has purged %s [%d]", GET_NAME (ch),
                obj->short_description ? obj->short_description : "No description", world[ch->in_room].number);
         extract_obj (obj);
         obj = NULL;
         }
      else
         {
         send_to_char ("I don't know anyone or anything by that name.\n", ch);
         return;
         }

      send_to_char ("Ok.\n", ch);
      }
   else
      {        /* no argument. clean out the room */
      if(IS_NPC (ch))
         {
         send_to_char ("Don't... You would only kill yourself...\n", ch);
         return;
         }
      wizlog (GET_LEVEL (ch), "%s has purged the room %d", GET_NAME (ch), world[ch->in_room].number);
      logit (LOG_WIZ, "%s has purged the room %d", GET_NAME (ch), world[ch->in_room].number);
      act ("$n gestures... you are surrounded by godly power!", FALSE, ch, 0, 0, TO_ROOM);
      send_to_room ("The world seems a little cleaner.\n", ch->in_room);

      for(vict = world[ch->in_room].people; vict; vict = next_v)
         {
         next_v = vict->next_in_room;
         if(IS_NPC (vict) && !IS_MORPH (ch))
            {
            /* @@ */
            if(ch->specials.cart)
               {
               ch->specials.cart->owner = NULL;
               ch->specials.cart = NULL;
               writeCart (ch);
               }

            /* Return switched PC back to its char */
            if(vict->desc && vict->desc->original && IS_PC(vict->desc->original))
               {
               do_return(vict, NULL, 0);
               }

            /* @@ */
            extract_char (vict);
            }
         }

      for(obj = world[ch->in_room].contents; obj; obj = next_o)
         {
         next_o = obj->next_content;
         if(obj->wear_flags & ITEM_TAKE)
            extract_obj (obj);
         }
      }
}

/* completely rewritten for new stats, flag is for skewing the totals:
   -1, range from   9 to 319, ie, used as a punishment
   0, range from 301 to 600, ie, the normal range, prevents useless and godlikes both
   1, range from 401 to 600, ie, 'good' stats
   2, range from 501 to 700, ie, 'great' stats
   3, range from 651 to 800, ie, 'exceptional' stats
   4, range from   8 to 800, ie, 'unbounded' stats (default for new chars til wipe)
 */

void pachinko_roller(P_char ch)
{
   int i, rolls[8], total, starting = 0;
   static int limits = 600;

   for(i = 0; i < 8; i++)
      {
      rolls[i] = MAX(13, min_stats_for_class[GET_CLASS(ch)][i] / 2);
      starting += rolls[i];
      }

   for(total = (limits - starting); total;)
      {
      i = number(0, 7);
      if(rolls[i] < 77 ||
         (rolls[i] < 83 && number(0, 2)) ||
         (rolls[i] < 89 && !number(0, 1)) ||
         (rolls[i] < 100 && !number(0, 2)))
         {
         total -= 1;
         rolls[i]++;
         }
      }

   ch->base_stats.Str = ch->curr_stats.Str = rolls[0];
   ch->base_stats.Dex = ch->curr_stats.Dex = rolls[1];
   ch->base_stats.Agi = ch->curr_stats.Agi = rolls[2];
   ch->base_stats.Con = ch->curr_stats.Con = rolls[3];
   ch->base_stats.Pow = ch->curr_stats.Pow = rolls[4];
   ch->base_stats.Int = ch->curr_stats.Int = rolls[5];
   ch->base_stats.Wis = ch->curr_stats.Wis = rolls[6];
   ch->base_stats.Cha = ch->curr_stats.Cha = rolls[7];

   ch->base_stats.Karma = ch->curr_stats.Karma = number (1, 100);
   ch->base_stats.Luck = ch->curr_stats.Luck = number (1, 100);
}

void roll_basic_abilities (P_char ch, int flag)
{
   int i, rolls[8], total;
   static int limits[7][2] =
   {
      {9, 300},
      {301, 600},
      {401, 600},
      {501, 700},
      {651, 800},
      {9, 800},
      /* Added to test roller - Iyachtu */
      {301, 660}
   };

   do
      {
      for(i = 0, total = 0; i < 8; i++)
         {
         /* die rolls are 3d34-2 for belled 1-100
            rolls[i] = dice(3, 34) - 2; */
         /* die rolls modified to be 5d16+20 to move avg to approx 62 */
         rolls[i] = dice (3, 26) + 22;

         switch(flag)
            {
            case -1:
               rolls[i] = BOUNDED (1, rolls[i] - 20, 74);
               break;
            case 1:
               rolls[i] = BOUNDED (10, rolls[i] + 5, 100);
               break;
            case 2:
               rolls[i] = BOUNDED (15, rolls[i] + 10, 100);
               break;
            case 3:
               rolls[i] = BOUNDED (33, rolls[i] + 25, 100);
               break;
            case 0:
            case 4:
            default:
               rolls[i] = BOUNDED (25, rolls[i], 100);
               break;
            }
         total += rolls[i];
         }
      } while((total < limits[BOUNDED (0, (flag + 1), 3)][0]) ||
              (total > limits[BOUNDED (0, (flag + 1), 3)][1]));

   ch->base_stats.Str = ch->curr_stats.Str = rolls[0];
   ch->base_stats.Dex = ch->curr_stats.Dex = rolls[1];
   ch->base_stats.Agi = ch->curr_stats.Agi = rolls[2];
   ch->base_stats.Con = ch->curr_stats.Con = rolls[3];
   ch->base_stats.Pow = ch->curr_stats.Pow = rolls[4];
   ch->base_stats.Int = ch->curr_stats.Int = rolls[5];
   ch->base_stats.Wis = ch->curr_stats.Wis = rolls[6];
   ch->base_stats.Cha = ch->curr_stats.Cha = rolls[7];

   ch->base_stats.Karma = ch->curr_stats.Karma = number (1, 100);
   ch->base_stats.Luck = ch->curr_stats.Luck = number (1, 100);
}

void NewbySkillSet (P_char ch)
{
   int i, j;

   for(i = 0; i < MAX_SKILLS; i++)
      {
      j = pindex2Skill[i];
      if(j < 0)
         continue;
      if(skills[j].class[GET_CLASS (ch) - 1].maxlearn &&
         (skills[j].class[GET_CLASS (ch) - 1].rlevel == 1))
         ch->only.pc->skills[i].learned = MAX (1, skills[j].class[GET_CLASS (ch) - 1].maxlearn / 6);
      }
}

void do_start (P_char ch)
{
   int i;

   if(IS_NPC (ch))
      return;

   send_to_char ("Welcome. This is now your character on Outcast.\n"
                 "May your journey here never end.....\n\n"
                 " NOTE:  Type TOGGLE and HELP for useful information!\n"
                 " &+YRead the rules! Type HELP RULES, HELP MULTIi&N\n\n"
                 " &+CIf you are new to Outcast, you may use the Newbie Help Channel\n"
                 " &+Cto get some tips and advice from other players.  Type: nhc <message>\n"
                 " &+Cto get started!\n", ch);
   init_defaultlanguages (ch);
   set_title (ch);

   ch->points.base_hit = 10;   /* These are BASE numbers   */

   /* problem, need to clear the skills array */
   for(i = 0; i < MAX_SKILLS; i++)
      {
      ch->only.pc->skills[i].learned = 0;
      ch->only.pc->skills[i].memorized = 0;
      }

   NewbySkillSet (ch);

#if 0
   for(i = 0; i < MAX_SKILLS; i++)
      ch->only.pc->skills[i].total = ch->only.pc->skills[i].learned;
#endif

   gain_exp_regardless (ch, 1);   /* should take them from level 0 to level 1 */

   GET_HIT (ch) = hit_limit (ch) + con_app[STAT_INDEX (GET_C_CON (ch))].hitp;
   GET_MAX_HIT (ch) = GET_HIT (ch);
#ifdef PCS_USE_MANA
   GET_MANA (ch) = mana_limit (ch);
   GET_MAX_MANA (ch) = GET_MANA (ch);
#endif
   GET_MOVE (ch) = move_limit (ch);
   GET_MAX_MOVE (ch) = GET_MOVE (ch);

   GET_COND (ch, THIRST) = 96;
   GET_COND (ch, FULL) = 96;
   GET_COND (ch, DRUNK) = 0;

   /* set some defaults. */
   SET_CBIT (ch->only.pc->pcact, PLR_PETITION);
   SET_CBIT (ch->only.pc->pcact, PLR_ECHO);
   SET_CBIT (ch->only.pc->pcact, PLR_SNOTIFY);
   SET_CBIT (ch->only.pc->pcact, PLR_PAGING_ON);
   SET_CBIT (ch->only.pc->pcact, PLR_NO_KILL);
   SET_CBIT (ch->only.pc->pcact, PLR_NCC);
   ch->only.pc->wimpy = 10;
   ch->only.pc->aggressive = -1;
   ch->only.pc->prompt = (PROMPT_HIT | PROMPT_MAX_HIT | PROMPT_MOVE | PROMPT_MAX_MOVE);

   ch->player.time.played = 0;
   ch->player.time.logon = time (0);
}

void do_advance (P_char ch, char *argument, int cmd)
{
   P_char victim;
   char name[MAX_INPUT_LENGTH], level[MAX_INPUT_LENGTH], buf[MAX_INPUT_LENGTH];
   int adv, newlevel = 0, oldlevel;

   if(IS_NPC (ch))
      return;

   argument_interpreter (argument, name, level);

   if(*name)
      {
      if(!(victim = get_char_in_game_vis (ch, name, FALSE)))
         {
         send_to_char ("That player is not here.\n", ch);
         return;
         }
      }
   else
      {
      send_to_char ("Advance who?\n", ch);
      return;
      }

   if(IS_NPC (victim))
      {
      send_to_char ("NO! Not on NPC's.\n", ch);
      return;
      }
   oldlevel = GET_LEVEL (victim);

   if(oldlevel == 0)
      adv = 1;
   else if(!*level)
      {
      send_to_char ("You must supply a level number.\n", ch);
      return;
      }
   else
      {
      if(!isdigit (*level))
         {
         send_to_char ("Second argument must be a positive integer.\n", ch);
         return;
         }
      newlevel = atoi (level);

      if(newlevel <= oldlevel)
         {
         send_to_char ("Can't diminish a players status (yet).\n", ch);
         return;
         }
      }

   adv = newlevel - oldlevel;

   if(newlevel > GET_LEVEL (ch))
      {
      send_to_char ("So sorry Charlie!  You may not advance someone past your level.\n", ch);
      return;
      }
   if(newlevel > FORGER)
      {
      send_to_char ("60 is the highest possible level.\n", ch);
      return;
      }
   if(newlevel != 1)
      send_to_char ("You feel generous.\n", ch);

   act ("$n makes some strange gestures.\n"
        "A strange feeling comes upon you.   Like a giant hand, light comes down\n"
        "from above, grabbing your body, which begins to pulse with colored lights\n"
        "from inside.  Your head seems to be filled with demons from another plane\n"
        "as your body dissolves to the elements of time and space itself.\n"
        "Suddenly a silent explosion of light snaps you back to reality.  You feel\n"
        "improved!",
        FALSE, ch, 0, victim, TO_VICT);

   if(oldlevel == 0)
      {
      do_start (victim);
      return;
      }
   else
      {
      if(newlevel > 50)
         adv = 50 - oldlevel;

      if(oldlevel < MAXLVLMORTAL)
         gain_exp_regardless (victim, (int) (EXP_TABLE (victim, adv) - GET_EXP (victim)));

      /* new immortal advancement, without experience */
      while(GET_LEVEL (victim) < newlevel)
         {
         send_to_char ("You raise a level!\n", victim);
         GET_LEVEL (victim)++;
         if(GET_LEVEL (victim) == MINLVLIMMORTAL)
            {
            send_to_char ("A sense of timelessness overwhelms you.  You feel the myriad aches and pains\n"
                          "of mortal flesh drop away.  Power courses through your body and you seem to\n"
                          "actually be glowing!\n\nWelcome to immortality!\n", victim);
            SET_CBIT (victim->only.pc->pcact, PLR_PETITION);
            SET_CBIT (victim->only.pc->pcact, PLR_AGGIMMUNE);
            SET_CBIT (victim->only.pc->pcact, PLR_WIZLOG);
            SET_CBIT (victim->only.pc->pcact, PLR_STATUS);
            SET_CBIT (victim->only.pc->pcact, PLR_NOFOLLOW);
            SET_CBIT (victim->only.pc->pcact, PLR_VNUM);
            SET_CBIT (victim->only.pc->pcact, PLR_EMSLOG);
            REMOVE_CBIT (victim->only.pc->pcact, PLR_ANONYMOUS);
            victim->only.pc->prompt |= (PROMPT_VIS);
            }
         if(IS_CSET (GRANT_FLAGS (victim), 1))
            {
            sprintf (buf, "%s admin %d", GET_NAME (victim), GET_LEVEL (victim));
            do_grant (ch, buf, CMD_GRANT);
            logit (LOG_WIZ, "AUTOGRANT: %s", buf);
            }
         if(IS_CSET (GRANT_FLAGS (victim), 2))
            {
            sprintf (buf, "%s areas %d", GET_NAME (victim), GET_LEVEL (victim));
            do_grant (ch, buf, CMD_GRANT);
            logit (LOG_WIZ, "AUTOGRANT: %s", buf);
            }
         if(IS_CSET (GRANT_FLAGS (victim), 3))
            {
            sprintf (buf, "%s coder %d", GET_NAME (victim), GET_LEVEL (victim));
            do_grant (ch, buf, CMD_GRANT);
            logit (LOG_WIZ, "AUTOGRANT: %s", buf);
            }
         if(IS_CSET (GRANT_FLAGS (victim), 4))
            {
            sprintf (buf, "%s www %d", GET_NAME (victim), GET_LEVEL (victim));
            do_grant (ch, buf, CMD_GRANT);
            logit (LOG_WIZ, "AUTOGRANT: %s", buf);
            }
         }
      }

   wizlog (GET_LEVEL (ch), " %s has advanced %s to level %d",
           GET_NAME (ch), GET_NAME (victim), GET_LEVEL (victim));
   logit (LOG_WIZ, "%s advanced %s to level %d",
          GET_NAME (ch), GET_NAME (victim), GET_LEVEL (victim));
   do_restore (ch, name, -4);
}

#define REROLL_SYNTAX \
"Syntax:  reroll <name> [<modifier>]\n\
  Modifier is a number:\n\
    0 - miserable\n\
    1 - normal (default)\n\
    2 - good, still in 'normal' range\n\
    3 - great, can be above normal\n\
    4 - exceptional, well above average\n"

void do_reroll (P_char ch, char *argument, int cmd)
{
   P_char victim;
   int flag;
   char name[MAX_INPUT_LENGTH], modifier[MAX_INPUT_LENGTH], buf[MAX_STRING_LENGTH];

   if(IS_NPC (ch))
      return;

   argument_interpreter (argument, name, modifier);
   if(!*name)
      {
      send_to_char (REROLL_SYNTAX, ch);
      return;
      }
   else if(!(victim = get_char_in_game_vis (ch, name, TRUE)))
      {
      send_to_char ("No-one by that name in the world.\n", ch);
      return;
      }
   if(!*modifier || (*modifier == '1'))
      flag = 0;
   else if(*modifier == '0')
      flag = -1;
   else if(*modifier == '2')
      flag = 1;
   else if(*modifier == '3')
      flag = 2;
   else if(*modifier == '4')
      flag = 3;
   else
      {
      send_to_char (REROLL_SYNTAX, ch);
      return;
      }

   roll_basic_abilities (victim, flag);
   if(!IS_TRUSTED (ch))
      send_to_char ("Rerolled...\n", ch);
   else
      {
      sprintf (buf,
               "%s Rerolled:     Avg:(%d)\n  S:%3d  D:%3d  A:%3d  C:%3d\n  P:%3d  I:%3d  W:%3d Ch:%3d\n",
               GET_NAME (victim),
               (GET_C_STR (victim) + GET_C_DEX (victim) + GET_C_AGI (victim) + GET_C_CON (victim) +
                GET_C_POW (victim) + GET_C_INT (victim) + GET_C_WIS (victim) + GET_C_CHA (victim)) / 8,
               GET_C_STR (victim), GET_C_DEX (victim), GET_C_AGI (victim), GET_C_CON (victim),
               GET_C_POW (victim), GET_C_INT (victim), GET_C_WIS (victim), GET_C_CHA (victim));
      send_to_char (buf, ch);

      affect_total (victim);
      }
   if(IS_TRUSTED (ch))
      {
      wizlog (GET_LEVEL (ch), "%s has rerolled %s%s", GET_NAME (ch), GET_NAME (victim),
              (flag == -1) ? " miserably" : (flag == 0) ? "" : (flag == 1) ? " good" : (flag == 2) ? " great" : " godlike");
      logit (LOG_WIZ, "%s has rerolled %s%s", GET_NAME (ch), GET_NAME (victim),
             (flag == -1) ? " miserably" : (flag == 0) ? "" : (flag == 1) ? " good" : (flag == 2) ? " great" : " godlike");
      }
}
#undef REROLL_SYNTAX

void do_restore (P_char ch, char *argument, int cmd)
{
   P_char victim;
   int i = 0;
   char buf[MAX_STRING_LENGTH];

   if(IS_NPC (ch))
      return;

   one_argument (argument, buf);

   if(!*buf)
      send_to_char ("Who do you wish to restore?\n", ch);
   else if(!(victim = get_char_in_game_vis (ch, buf, TRUE)))
      send_to_char ("No-one by that name in the world.\n", ch);
   else
      {
      AddEvent (EVENT_BALANCE_AFFECTS, 1, TRUE, victim, 0);
#ifdef PCS_USE_MANA
      GET_MANA (victim) = GET_MAX_MANA (victim);
#endif
      GET_HIT (victim) = GET_MAX_HIT (victim);
      GET_MOVE (victim) = GET_MAX_MOVE (victim);

      /*
       * Restore the NPCs complement of spells available for casting. - SKB
       */
      if(IS_NPC (victim))
         {
         victim->only.npc->spells_in_circle[0] = 0;

         for(i = 1; i <= MAX_CIRCLE; i++)
            victim->only.npc->spells_in_circle[i] = spl_table[GET_LEVEL (victim)][i - 1];
         }
      if(GET_STAT (victim) < STAT_SLEEPING)
         SET_POS (victim, GET_POS (victim) + STAT_NORMAL);

      if(IS_PC (ch))
         {
         GET_COND (victim, FULL) = IS_TRUSTED (victim) ? -1 : 24;
         GET_COND (victim, THIRST) = IS_TRUSTED (victim) ? -1 : 24;
         GET_COND (victim, DRUNK) = 0;
         }
      if((cmd != -4) && !IS_TRUSTED (victim))
         {
         wizlog (GET_LEVEL (ch), "%s has restored %s", GET_NAME (ch),
                 GET_NAME (victim));
         logit (LOG_WIZ, "%s has restored %s", GET_NAME (ch),
                GET_NAME (victim));
         }
      if(IS_TRUSTED (victim))
         {
         for(i = 0; i < MAX_SKILLS; i++)
            {
            victim->only.pc->skills[i].learned = 100;
#if 0
            victim->only.pc->skills[i].total = 100;
#endif
            }
         for(i = 0; i < MAX_TONGUE; i++)
            GET_LANGUAGE (victim, i) = 100;
         }
      /* restore skill usage times -Neb */

      if(IS_PC(victim))
         {
         for(i = 0; i < MAX_TIMED_USAGES; i++)
            victim->only.pc->timed_usages[i].time_of_first_use = 0;
         }

      update_pos (victim);
      if(ch != victim)
         {
         send_to_char ("Done.\n", ch);
         act ("You have been fully restored by $N!", FALSE, victim, 0, ch, TO_CHAR);
         }
      else
         send_to_char ("Restored.\n", ch);
      }
}


void do_loadmob(P_char ch, char *argument, int cmd)
{
   char mob[MAX_STRING_LENGTH] = "", amnt[MAX_STRING_LENGTH] = "";
   int amount = 1, counter = 0, mobnum = 0;
   P_char loaded = NULL;



   argument = one_argument(argument, mob);
   if(!*mob || !argument)
      {
      send_to_char("Usage: loadmob <vnum> <amount>\n", ch);
      return;
      }

   mobnum = atoi(mob);
   argument = one_argument(argument, amnt);
   if(!*amnt)
      amount = 1;
   else
      amount = atoi(amnt);

   if(amount > 99 || amount < 1)
      {
      send_to_char("Please load mobs in the range of 1 to 99.\n", ch);
      return;
      }

   for(counter = 0; counter < (amount); counter++)
      if(!(loaded = loadmob(mobnum, ch)))
         break;


   if(loaded)
      {
      wizcmd(GET_LEVEL(ch), "%s loaded mob %s '%s' in [%d] %i times.",
             GET_NAME(ch), mob, C_NAME(loaded), world[ch->in_room].number, amount);
      logit(LOG_WIZLOAD, "%s loaded mob %s '%s' in [%d] %i times.",
            GET_NAME(ch), mob, C_NAME(loaded), world[ch->in_room].number, amount);
      }
}


void do_allowgroup(P_char ch, char *argument, int cmd)
{
   char character[MAX_STRING_LENGTH] = "";
   P_char vict;


   argument = one_argument(argument, character);


   if(!*character)
      {
      send_to_char ("Usage: allowgroup <player>\n", ch);
      return;
      }
   if(!(vict=get_char_in_game_vis(ch, character, TRUE)))
      {
      send_to_char ("That person doesn't exist!!!\n", ch);
      return;
      }
   if(IS_CSET(vict->only.pc->pcact, PLR_GROUPALLOW))
      {
      send_to_char ("Setting group allow to false!\n", ch);
      REMOVE_CBIT(vict->only.pc->pcact, PLR_GROUPALLOW);
      wizcmd (GET_LEVEL(ch), "%s removed allowgroup flag on %s.", GET_NAME(ch),
              GET_NAME(vict));
      }
   else
      {
      send_to_char ("Setting group allow to true!\n", ch);
      SET_CBIT(vict->only.pc->pcact, PLR_GROUPALLOW);
      wizcmd (GET_LEVEL(ch), "%s set allowgroup flag on %s.", GET_NAME(ch),
              GET_NAME(vict));
      }

}



void do_addprestige(P_char ch, char *argument, int cmd)
{

   char character[MAX_STRING_LENGTH] = "", amnt[MAX_STRING_LENGTH] = "", buff[MAX_STRING_LENGTH] = "";
   P_char vict;
   int ammount = 1;
   argument = one_argument(argument, character);


   if(!*character)
      {
      send_to_char ("Usage: addprestige <player> <ammount>\n", ch);
      return;
      }

   argument = one_argument(argument, amnt);
   if(!*amnt)
      ammount=1;
   else
      ammount = atoi(amnt);


   if(!(vict=get_char_in_game_vis(ch, character, TRUE)))
      send_to_char ("That person doesn't exist!!!\n", ch);

   else if(IS_NPC(vict))
      send_to_char ("You can't give a mobile prestige.\n", ch);

   else if(ammount > 500)
      send_to_char("You can't give anyone more than 500 prestige!\n", ch);

   else if(ammount < -500)
      send_to_char("You can't take away more than 500 prestige!\n", ch);

   else if((GET_PRESTIGE(vict)+ammount) < 0)
      send_to_char("There isn't that much prestige to take away!!!\n", ch);

   else
      {
      GET_PRESTIGE(vict) += ammount;
      sprintf(buff, "You just gave %s %s prestige!\n", character, amnt);
      send_to_char(buff, ch);
      sprintf(buff, "They have %i prestige now.\n", GET_PRESTIGE(vict));
      send_to_char(buff, ch);
      sprintf(buff, "You just recieved %s prestige!!\n", amnt);
      send_to_char(buff, vict);

      wizlog(GET_LEVEL(ch), "%s gave %s %s prestige.", GET_NAME(ch), GET_NAME(vict), amnt);
      logit(LOG_WIZ, "%s gave %s %s prestige.", GET_NAME(ch), GET_NAME(vict), amnt);
      }



}





void do_freeze (P_char ch, char *argument, int cmd)
{
   P_char vict;
   P_obj dummy;
   char buf[MAX_STRING_LENGTH];

   if(IS_NPC (ch))
      return;

   one_argument (argument, buf);

   if(!*buf)
      send_to_char ("Usage: freeze <player>\n", ch);
   else if(!generic_find (argument, FIND_CHAR_WORLD, ch, &vict, &dummy))
      send_to_char ("Couldn't find any such creature.\n", ch);
   else if(IS_NPC (vict))
      send_to_char ("You can't freeze a mobile.\n", ch);
   else if(GET_LEVEL (vict) >= GET_LEVEL (ch))
      act ("$E is too hot for you to freeze..", 0, ch, 0, vict, TO_CHAR);
   else if(IS_CSET (vict->only.pc->pcact, PLR_FROZEN))
      {
      send_to_char ("You have thawed out, and can move again freely.\n", vict);
      send_to_char ("Player has thawed out.\n", ch);
      REMOVE_CBIT (vict->only.pc->pcact, PLR_FROZEN);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s was just unfrozen by %s.", GET_NAME (vict), GET_NAME (ch));
         logit (LOG_WIZ, "%s was just unfrozen by %s.", GET_NAME (vict), GET_NAME (ch));
         }
      }
   else
      {
      send_to_char ("You suddenly feel very frozen, and can't move.\n", vict);
      send_to_char ("FROZEN set.\n", ch);
      SET_CBIT (vict->only.pc->pcact, PLR_FROZEN);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s was just frozen by %s.", GET_NAME (vict), GET_NAME (ch));
         logit (LOG_WIZ, "%s was just frozen by %s.", GET_NAME (vict), GET_NAME (ch));
         }
      }
}

void do_zreset(P_char ch, char *argument, int cmd)
{
   char arg[MAX_STRING_LENGTH];
   char buf[MAX_STRING_LENGTH];
   struct zone_data *zone_struct;
   int zone_number = 0;

   if(!ch)
      {
      logit(LOG_DEBUG, "Screw-up in do_zreset(), NULL ch.");
      return;
      }

   if(IS_NPC(ch))
      return;

   zone_number = world[ch->in_room].zone;
   zone_struct = &zone_table[zone_number];

   one_argument(argument, arg);

   if(!*arg)
      {
      send_to_char("Syntax:\n"
                   "> zreset ok (Reset zone)\n"
                   "> zreset never (Zone never resets)\n"
                   "> zreset always (Resets regardless of who is in it)\n"
                   "> zreset empty (Reset only when empty of players)\n"
                   "> zreset restock (Perform a major zone reset now!)\n", ch);
      }
   else if(!str_cmp(arg, "ok"))
      {
      sprintf(buf, "Zone: %s has been reset.\n", zone_struct->name);
      send_to_char(buf, ch);
      reset_zone(zone_number);
      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just reset the zone %s.", GET_NAME(ch), zone_struct->name);
         logit(LOG_WIZ, "%s just reset the zone %s.", GET_NAME(ch), zone_struct->name);
         }

      return;
      }
   else if(!str_cmp(arg, "restock"))
      { // NOTE: this does not call major_reset_zone() to see if it passes the allowable conditions
      if(purge_zone(zone_number)) // purge_zone() can fail for a variety of reasons, IE mobs fighting PCs
         {
         sprintf(buf, "Zone: %s has been completely reset.\n", zone_struct->name);
         send_to_char(buf, ch);
         zone_struct->time_last_boot = time(0);
         reset_zone(zone_number);
         wizlog(GET_LEVEL(ch), "%s just completely reset the zone %s.", GET_NAME(ch), zone_struct->name);
         // temp disable so as not to spam the log        logit(LOG_WIZ, "%s just completely reset the zone %s.", GET_NAME(ch), zone_struct->name);
         }
      else
         {
         sprintf(buf, "Zone: %s could not be reset in this manner at the current time.\n", zone_struct->name);
         send_to_char(buf, ch);
         }

      return;
      }
   else if(!str_cmp(arg, "purge")) // this is just for debugging purposes on testmud
      {
      if(purge_zone(zone_number)) // purge_zone() can fail for a variety of reasons, IE mobs fighting PCs
         {
         sprintf(buf, "Zone: %s has been completely purged.\n", zone_struct->name);
         send_to_char(buf, ch);
         }
      else
         {
         sprintf(buf, "Zone: %s could not be purged at the current time.\n", zone_struct->name);
         send_to_char(buf, ch);
         }

      return;
      }
   else if(!str_cmp(arg, "never"))
      {
      sprintf(buf, "Zone %s has been changed to: Never Resets.\n", zone_struct->name);
      send_to_char(buf, ch);
      zone_table[zone_number].reset_mode = 0;
      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just set zone %s to: Never Resets.", GET_NAME(ch), zone_struct->name);
         logit(LOG_WIZ, "%s just set zone %s to: Never Resets.", GET_NAME(ch), zone_struct->name);
         }

      return;
      }
   else if(!str_cmp(arg, "always"))
      {
      sprintf(buf, "Zone %s has been changed to: Resets Regardless.\n", zone_struct->name);
      send_to_char(buf, ch);
      zone_table[zone_number].reset_mode = 2;
      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just set zone %s to: Resets Regardless.", GET_NAME(ch), zone_struct->name);
         logit(LOG_WIZ, "%s just set zone %s to: Resets Regardless.", GET_NAME(ch), zone_struct->name);
         }

      return;
      }
   else if(!str_cmp(arg, "empty"))
      {
      sprintf(buf, "Zone %s has been changed to: Resets when Empty.\n", zone_struct->name);
      send_to_char(buf, ch);
      zone_table[zone_number].reset_mode = 1;
      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just set zone %s to: Resets when Empty.", GET_NAME(ch), zone_struct->name);
         logit(LOG_WIZ, "%s just set zone %s to: Resets when Empty.", GET_NAME(ch), zone_struct->name);
         }

      return;
      }
   else
      {
      send_to_char("Syntax:\n"
                   "> zreset ok (Reset zone)\n"
                   "> zreset never (Zone never resets)\n"
                   "> zreset always (Resets regardless of who is in it)\n"
                   "> zreset empty (Reset only when empty of players)\n"
                   "> zreset restock (Perform a major zone reset now!)\n", ch);
      }

   return;
}

void do_reinitphys (P_char ch, char *arg, int cmd)
{
   P_char vict;
   P_desc p;

   if(GET_LEVEL (ch) <= MAXLVLMORTAL)
      {      /* mortals can only set their own attr */
      send_to_char ("Your height and weight have been re-randomed!\n", ch);
      set_char_size (ch);
      return;
      }
   if(!*arg || !arg)
      {
      send_to_char ("usage:\n   reinitphys <targetname>\n", ch);
      return;
      }
   if(isname (arg, "all"))
      {
      for(p = descriptor_list; p; p = p->next)
         if(!p->connected)
            if(CAN_SEE (ch, p->character))
               if(strcmp (GET_NAME (ch), GET_NAME (p->character)))
                  do_reinitphys (ch, GET_NAME (p->character), CMD_REINITPHYS);
      send_to_char ("Ok, you re-random everyone's physical stats (except yourself).\n", ch);
      return;
      }
   vict = get_char_in_game_vis (ch, arg, TRUE);
   if(!vict)
      {
      send_to_char ("You see no char with name like that in the game!\n", ch);
      return;
      }
   act ("Ok, $N's weight and height rerolled.", TRUE, ch, 0, vict, TO_CHAR);
   act ("Your height and weight have been randomly rolled again by $n!", TRUE, ch, 0, vict, TO_VICT);
   set_char_size (vict);
}

void do_silence (P_char ch, char *argument, int cmd)
{
   P_char vict;
   P_obj dummy;
   char buf[MAX_STRING_LENGTH];

   if(IS_NPC (ch))
      return;

   one_argument (argument, buf);

   if(!*buf)
      send_to_char ("Usage: silence <player>\n", ch);
   else if(!generic_find (argument, FIND_CHAR_WORLD, ch, &vict, &dummy))
      send_to_char ("Couldn't find any such creature.\n", ch);
   else if(IS_NPC (vict))
      send_to_char ("Can't do that to a mobile.\n", ch);
   else if(GET_LEVEL (vict) > GET_LEVEL (ch))
      act ("$E might object to that.. better not.", 0, ch, 0, vict, TO_CHAR);
   else if(IS_CSET (vict->only.pc->pcact, PLR_SILENCE))
      {
      send_to_char ("You can shout, etc again!\n", vict);
      send_to_char ("SILENCE removed.\n", ch);
      REMOVE_CBIT (vict->only.pc->pcact, PLR_SILENCE);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s just removed the silence on %s.", GET_NAME (ch), GET_NAME (vict));
         logit (LOG_WIZ, "%s just removed the silence on %s.", GET_NAME (ch), GET_NAME (vict));
         }
      }
   else
      {
      send_to_char ("The gods take away your ability to shout!\n", vict);
      send_to_char ("SILENCE set.\n", ch);
      SET_CBIT (vict->only.pc->pcact, PLR_SILENCE);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s was just silenced by %s.", GET_NAME (vict), GET_NAME (ch));
         logit (LOG_WIZ, "%s was just silenced by %s.", GET_NAME (vict), GET_NAME (ch));
         }
      }
}

/*
   ** Make oneself visible only to players above certain levels.
 */
void do_vis (P_char ch, char *argument, int cmd)
{
   char buf[MAX_STRING_LENGTH];

   one_argument (argument, buf);

   if(!*buf)
      {       /* Inquire visibility */
      if(IS_TRUSTED (ch))
         sprintf (buf, "You are currently visible only to players with level > %d.\n", ch->only.pc->wiz_invis);
      if(IS_AFFECTED (ch, AFF_INVISIBLE))
         if(strlen (buf) > 0)
            strcat (buf, "You are also affected by Invisibility.\n");
         else
            strcpy (buf, "You are affected by Invisibility.\n");
      else if(strlen (buf) == 0)
         strcpy (buf, "You are not Invisible.\n");
      send_to_char (buf, ch);
      }
   else
      {        /* Set new visibility */
      int min_level;

      if(IS_AFFECTED(ch, AFF_WRAITHFORM))
         {
         BackToUsualForm (ch);
         return;
         }
      if(!IS_TRUSTED (ch))
         {
         appear (ch);
         return;
         }
      min_level = MAX (0, atoi (buf));
      if(min_level >= GET_LEVEL (ch))
         {
         send_to_char ("Sorry... but you cannot be invis to your peers or superiors.\n", ch);
         return;
         }
      sprintf (buf, "You are now visible only to PCs with level > %d.\n", min_level);

      ch->only.pc->wiz_invis = (ubyte) min_level;
      send_to_char (buf, ch);
      }
}

/* intended usage:  lflags player city flag
   player - any PC
   city   - any string (or partial string) in town_name_list
   flag   - any string in justice_flag_names
 */
void do_law_flags (P_char ch, char *argument, int cmd)
{
   P_char vict;
   char *args, buf[MAX_STRING_LENGTH];
   char c_flag[MAX_STRING_LENGTH], person[MAX_STRING_LENGTH], c_city[MAX_STRING_LENGTH];
   int i, city, flag;
   unsigned l;

   if(IS_NPC (ch))
      return;

   if((args = one_argument (argument, person)) == NULL)
      return;

   if(*person == '\0')
      {
      send_to_char (
                   "Eh?  try \"lflags player\" (to display a player's flags)\n"
                   "      or \"lflags player city flag\" (to set player's flag for city)\n\n"
                   "City names (may be partial):            Flag names (may be partial):\n\n", ch);
      for(i = 1; i <= LAST_HOME; i++)
         {
         sprintf (buf, "%-40s%-20s\n", town_name_list[i],
                  ((i - 1) > JUSTICE_IS_LAST_FLAG) ? "" : justice_flag_names[i - 1]);
         send_to_char (buf, ch);
         }
      send_to_char ("\n", ch);
      return;
      }
   /* find the player... */
   if(!(vict = get_char_in_game_vis (ch, person, FALSE)) || IS_NPC (vict))
      {
      send_to_char ("No one by that name here...\n", ch);
      return;
      }
   if((args = one_argument (args, c_city)) == NULL)
      return;
   if(*c_city == '\0')
      {
      /* okay.. just show them all the law flags for this player */
      sprintf (buf, "Current justice flags for %s:\n\n", vict->player.name);
      send_to_char (buf, ch);
      for(i = 1; i <= LAST_HOME; i++)
         {
         if(IS_TOWN_INVADER (vict, i))
            {
            sprintf(buf, "%-25s    &+Rinvader&N\n", town_name_list[i]);
            send_to_char(buf, ch);
            continue;
            }
         flag = PC_TOWN_JUSTICE_FLAGS(vict, i);
         if(flag == JUSTICE_IS_KNOWN)
            {
            sprintf (buf, "%-25s    %s\n", town_name_list[i],
                     justice_flag_names[flag]);
            send_to_char (buf, ch);
            continue;
            }
         else if(flag == JUSTICE_IS_OUTCAST)
            {
            sprintf(buf, "%-25s    &+L%s&N\n", town_name_list[i],
                    justice_flag_names[flag]);
            send_to_char(buf, ch);
            continue;
            }
         else
            {
            sprintf(buf, "%-25s    &+G%s&N\n", town_name_list[i],
                    justice_flag_names[flag]);
            send_to_char(buf, ch);
            }
         }
      send_to_char ("\n", ch);
      return;
      }
   /* validate the city... */
   l = strlen (c_city);
   for(city = 1; city <= LAST_HOME; city++)
      if(!strn_cmp (c_city, town_name_list[city], l))
         break;

   if(city > LAST_HOME)
      {
      send_to_char("Invalid city name.  Use \"lflags\" with no parameters for help.\n", ch);
      return;
      }
   if((args = one_argument (args, c_flag)) == NULL)
      return;
   if(*c_flag == '\0')
      {
      send_to_char ("And which flag do you want to set?\n", ch);
      return;
      }
   /* validate the flag */
   l = strlen (c_flag);
   for(flag = 0; flag <= JUSTICE_IS_LAST_FLAG; flag++)
      if(!strn_cmp (c_flag, justice_flag_names[flag], l))
         break;

   if(flag > JUSTICE_IS_LAST_FLAG)
      {
      send_to_char("Invalid city name.  Use \"lflags\" with no parameters for help.\n", ch);
      return;
      }

   if(IS_TOWN_INVADER (vict, city) && (flag != JUSTICE_IS_OUTCAST))
      {
      send_to_char ("They can only be an outcast from there!\n", ch);
      return;
      }

   PC_SET_TOWN_JUSTICE_FLAGS (vict, flag, city);

   /* fix their birth/home rooms */
   if(flag == JUSTICE_IS_OUTCAST)
      {
      int r_room;
      r_room = real_room (GET_BIRTHPLACE (vict));
      if((r_room != NOWHERE) && (city == zone_table[world[r_room].zone].hometown))
         {

         /* okay.. they are outcast in their birthplace.. FIX IT! */
         /* whee!  kludge time! */
         if(PC_TOWN_JUSTICE_FLAGS (vict, HOME_BLOODSTONE) != JUSTICE_IS_OUTCAST)
            GET_BIRTHPLACE (vict) = 7255;
         else
            GET_BIRTHPLACE (vict) = RACE_EVIL (vict) ? 0 : 0;
         }
      /* now check their home room */

      r_room = real_room (GET_HOME (vict));
      if((r_room != NOWHERE) && (city == zone_table[world[r_room].zone].hometown))
         GET_HOME (vict) = GET_BIRTHPLACE (vict);
      }
   sprintf (buf, "%s is now a %s in %s.\n", GET_NAME (vict), justice_flag_names[flag], town_name_list[city]);
   send_to_char (buf, ch);

   if(IS_TRUSTED(vict))
      {
      sprintf (buf, "%s just set your status in %s to \"%s\".\n",
               CAN_SEE (vict, ch) ? GET_NAME (ch) : "Someone", town_name_list[city], justice_flag_names[flag]);
      send_to_char (buf, vict);
      }

   /* log the hell out of it. */

   sprintf (buf, "%s made %s in %s by %s",
            GET_NAME (vict), justice_flag_names[flag], town_name_list[city], GET_NAME (ch));
   wizlog (GET_LEVEL (ch), buf);
   logit (LOG_FLAG, buf);
   logit (LOG_WIZ, buf);
}

/*
   ** Used to demote player to level 1 (and level 1 only)
 */

void do_demote (P_char ch, char *argument, int cmd)
{
   char person[MAX_STRING_LENGTH];
   char buf[MAX_STRING_LENGTH];
   P_char victim;
   int i;

   if(IS_NPC (ch))
      return;

   one_argument (argument, person);

   if(!*person)
      {
      send_to_char ("Syntax: demote person\n", ch);
      return;
      }
   if(!(victim = get_char_in_game_vis (ch, person, FALSE)))
      {
      send_to_char ("No one by that name here...\n", ch);
      return;
      }
   if(IS_NPC (victim))
      {
      send_to_char ("Monsters cannot be demoted.\n", ch);
      return;
      }
   if(GET_LEVEL (victim) >= GET_LEVEL (ch))
      {
      send_to_char ("Oh no you don't!\n", ch);
      return;
      }
   gain_exp_regardless (victim, (int) -(GET_EXP (victim)));

   /* Need to set the number of lessons to 0 */

#if 0
   victim->only.pc->spells_to_learn = 0;
#endif

   /* Unlearn all skill */

   for(i = 0; i < MAX_SKILLS; i++)
      {
      victim->only.pc->skills[i].learned = 0;
#if 0
      victim->only.pc->skills[i].total = 0;
#endif
      }
   NewbySkillSet (victim);

   /* Restore other attributes */
   victim->points.max_mana = 0;
   victim->points.max_move = 0;

   /* nuke granted commands too */
   for(i = 1; i <= MAX_GRANT_CMDS; i++)
      REMOVE_CBIT (GRANT_FLAGS (victim), i);

   /* Set hunger and thirst to 24 (instead of -1) */
   victim->specials.conditions[0] = 24;
   victim->specials.conditions[1] = 24;
   victim->specials.conditions[2] = 0;

   do_start (victim);

   /* LOG */

   wizlog (GET_LEVEL (ch), "%s has just demoted %s to level 1...Bummer\n", GET_NAME (ch), GET_NAME (victim));
   logit (LOG_WIZ, "%s has just demoted %s to level 1\n", GET_NAME (ch), GET_NAME (victim));

   /* Tell caster */

   sprintf (buf, "You have just demoted %s to level 1!  Ain't they hating..\n", GET_NAME (victim));
   send_to_char (buf, ch);

   /* Tell victim */

   send_to_char ("You have just been demoted to level one... oh well.\n", victim);
}

void do_poofIn (P_char ch, char *argument, int cmd)
{
   char arg[MAX_INPUT_LENGTH], buf[MAX_INPUT_LENGTH];
   int i;

   if(!IS_TRUSTED (ch))
      return;

   one_argument (argument, arg);

   if(!*arg)
      {
      if(ch->only.pc->poofIn != NULL)
         {
         free_string (ch->only.pc->poofIn);
         ch->only.pc->poofIn = NULL;
         }
      }
   else if(!str_cmp ("?", arg))
      {
      if(ch->only.pc->poofIn == NULL)
         {
         strcpy (buf, "$n appears with an ear-splitting bang.");
         }
      else
         {
         if(!strstr (ch->only.pc->poofIn, "%n"))
            {
            strcpy (buf, "$n ");
            strcat (buf, ch->only.pc->poofIn);
            }
         else
            {
            strcpy (buf, ch->only.pc->poofIn);
            /* bleah, code doubles $ to prevent entering 'act' strings */
            for(i = 0; i < strlen (buf); i++)
               if((*(buf + i) == '%') && (*(buf + i + 1) == 'n'))
                  *(buf + i) = '$';
            }
         }
      act (buf, TRUE, ch, 0, 0, TO_CHAR);
      }
   else
      {
      if(ch->only.pc->poofIn != NULL)
         {
         free_string (ch->only.pc->poofIn);
         ch->only.pc->poofIn = NULL;
         }
      if(*argument == ' ')
         argument++;
      if(strlen (argument) > 75)
         *(argument + 74) = '\0';
      ch->only.pc->poofIn = str_dup (argument);
      }            /* if */
}           /* do_poofIn */

void do_poofOut (P_char ch, char *argument, int cmd)
{
   char arg[MAX_INPUT_LENGTH], buf[MAX_INPUT_LENGTH];
   int i;

   if(!IS_TRUSTED (ch))
      return;

   one_argument (argument, arg);

   if(!*arg)
      {
      if(ch->only.pc->poofOut != NULL)
         {
         free_string (ch->only.pc->poofOut);
         ch->only.pc->poofOut = NULL;
         }
      }
   else if(!str_cmp ("?", arg))
      {
      if(ch->only.pc->poofOut == NULL)
         {
         strcpy (buf, "$n disappears in a puff of smoke.");
         }
      else
         {
         if(!strstr (ch->only.pc->poofOut, "%n"))
            {
            strcpy (buf, "$n ");
            strcat (buf, ch->only.pc->poofOut);
            }
         else
            {
            strcpy (buf, ch->only.pc->poofOut);
            /* bleah, code doubles $ to prevent entering 'act' strings */
            for(i = 0; i < strlen (buf); i++)
               if((*(buf + i) == '%') && (*(buf + i + 1) == 'n'))
                  *(buf + i) = '$';
            }
         }
      act (buf, TRUE, ch, 0, 0, TO_CHAR);
      }
   else
      {
      if(ch->only.pc->poofOut != NULL)
         {
         free_string (ch->only.pc->poofOut);
         ch->only.pc->poofOut = NULL;
         }
      if(*argument == ' ')
         argument++;
      if(strlen (argument) > 75)
         *(argument + 74) = '\0';
      ch->only.pc->poofOut = str_dup (argument);
      }            /* if */
}           /* do_poofOut */

/* temp new cmd so that I can work on it -Azuth */
extern int port;
void do_new_teleport(P_char ch, char *argument, int cmd);

void do_teleport(P_char ch, char *argument, int cmd)
{
   P_char victim, target_mob;
   char person[MAX_INPUT_LENGTH], room[MAX_INPUT_LENGTH];
   int target, old_room, loop;

   /* temp new cmd so that I can work on it -Azuth */
   /*  gonna keep this here a bit until it's for sure ok on main, call me cautious
      if(port != DFLT_MAIN_PORT || !str_cmp(GET_NAME(ch), "Azuth"))
         {*/
   do_new_teleport(ch, argument, cmd);
   return;
   /*      }*/

   if(!IS_TRUSTED(ch))
      return;

   half_chop(argument, person, room);

   if(!*person)
      {
      send_to_char("Who do you wish to teleport?\n", ch);
      return;
      }

   if(!*room)
      {
      send_to_char("Where do you wish to send this person?\n", ch);
      return;
      }

   if(!(victim = get_char_in_game_vis(ch, person, TRUE)))
      {
      send_to_char("No-one by that name around.\n", ch);
      return;
      }

   if((GET_LEVEL(victim) > GET_LEVEL(ch)) && (!IS_NPC(victim) || (GET_LEVEL(ch) < MAXLVL)))
      {
      send_to_char("You can't do that!\n", ch);
      return;
      }

   if(victim->in_room == NOWHERE)
      {
      send_to_char("Wait til they get into the game!\n", ch);
      return;
      }

   if(isdigit(*room))
      {
      target = atoi(&room[0]);
      for(loop = 0; loop <= top_of_world; loop++)
         { // Added sanity check to allow for null (empty) rooms. --MIAX 10/17/00
         if(!IS_CSET(world[loop].room_flags, RESERVED_OLC))
            {
            if(world[loop].number == target)
               {
               target = loop;
               break;
               }
            else if(loop == top_of_world)
               {
               send_to_char("No room exists with that number.\n", ch);
               return;
               }
            }
         }
      }
   else if(!strcmp(room, "return"))
      target = victim->specials.was_in_room;
   else if((target_mob = get_char_in_game_vis(ch, room, TRUE)))
      target = target_mob->in_room;
   else
      {
      send_to_char("No such target (person) can be found.\n", ch);
      return;
      }

   if(!can_enter_room(victim, target, FALSE) && GET_LEVEL(ch) < 59)
      {
      send_to_char("That person can't go there.\n", ch);
      return;
      }

   act("$n disappears in a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
   old_room = victim->in_room;
   char_from_room(victim);
   room_light(old_room, REAL);
   act("$n has teleported you!", FALSE, ch, 0, (char *) victim, TO_VICT);
   char_to_room(victim, target, -1);
   act("$n arrives from a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
   send_to_char("Teleport completed.\n", ch);
}

/* temp new cmd so that I can work on it -Azuth */
void do_new_teleport(P_char ch, char *argument, int cmd)
{
   P_char victim, target_mob;
   P_obj obj = NULL;
   char person[MAX_INPUT_LENGTH], room[MAX_INPUT_LENGTH];
   int target, old_room, loop, flag_obj_char = 0;

   if(!IS_TRUSTED(GET_PLYR(ch)))
      return;

   half_chop(argument, person, room);

   if(!*person)
      {
      send_to_char("Who or what do you wish to teleport?\n", ch);
      return;
      }

   if(!*room)
      {
      send_to_char("Where do you wish to send them or it?\n", ch);
      return;
      }

   victim = get_char_in_game_vis(ch, person, TRUE);
   if(victim && IS_PC(victim))
      flag_obj_char = 1; /* this is a char */
   else
      {
      victim = get_char_room_vis(ch, person);
      if(!victim)
         {
         if(!(obj = get_obj_in_list_vis(ch, person, world[ch->in_room].contents)))
            {
            send_to_char("Nothing matches that name.\n", ch);
            return;
            }
         else
            flag_obj_char = 2; /* this is an obj */
         }
      else
         flag_obj_char = 1; /* this is a char */
      }

   if(flag_obj_char == 1)
      {
      if(victim->in_room == NOWHERE)
         {
         send_to_char("Wait til they get into the game!\n", ch);
         return;
         }

      logit(LOG_WIZ, "%s teleport %s from [%d] to [%s]", GET_NAME(GET_PLYR(ch)), C_NAME(victim),
            world[victim->in_room].number, room);

      if((GET_LEVEL(victim) > GET_LEVEL(GET_PLYR(ch))) && !IS_NPC(victim))
         {
         send_to_char("You can't do that!\n", ch);
         return;
         }
      }

   if(isdigit(*room))
      {
      target = atoi(&room[0]);
      for(loop = 0; loop <= top_of_world; loop++)
         {
         if(world[loop].number == target)
            {
            target = loop;
            break;
            }
         else if(loop == top_of_world)
            {
            send_to_char("No room exists with that number.\n", ch);
            return;
            }
         }
      }
   else if(!strcmp(room, "return"))
      {
      if(flag_obj_char == 1)
         target = victim->specials.was_in_room;
      else
         {
         send_to_char("Sorry objects can't be returned this way.\n", ch);
         return;
         }
      }
   else if((target_mob = get_char_in_game_vis(ch, room, TRUE)))
      target = target_mob->in_room;
   else
      {
      send_to_char("No such target (person) can be found.\n", ch);
      return;
      }

   if(flag_obj_char == 1)
      {
      if(!can_enter_room(victim, target, FALSE) && GET_LEVEL(ch) < 59)
         {
         send_to_char("That person can't go there.\n", ch);
         return;
         }
      }

   if(flag_obj_char == 1)
      {
      act("$n disappears in a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
      old_room = victim->in_room;
      char_from_room(victim);
      room_light(old_room, REAL);
      act("$n has teleported you!", FALSE, ch, 0, (char *) victim, TO_VICT);
      char_to_room(victim, target, -1);
      act("$n arrives from a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
      send_to_char("Teleport completed.\n", ch);
      }
   else
      {
      act("$p disappears in a puff of smoke.", FALSE, ch, obj, 0, TO_ROOM);
      obj_from_room(obj);
      obj_to_room(obj, target);
      send_to_char("Teleport completed.\n", ch);
      }
}

#define BAN_HELP "\
ban xxx          ban all connections from any site containing the string 'xxx'\n\
                 Choose your strings carefully, 'ban edu' not only bans all\n\
                 education sites but ALL sites containing 'edu' anywhere in their\n\
                 name.\n\
ban #xxx         ban the creation of new characters from any site containing the\n\
                 string 'xxx'.\n\
ban ~xxx  ban the registration of any Email addresses containing xxx that\n\
                 players try to use when in the EMS character generation system.\n\
ban yyy@xxx      ban all users containing 'yyy' at all sites containing the\n\
                 string 'xxx'.\n\
ban yyy@#xxx     as above, but only ban the creation of new characters.\n\
ban <anon>@xxx   ban users without a username at any site containing the\n\
                 string 'xxx'.\n\
ban <anon>@#xxx  as above, but only new character creation is banned.\n\
ban xxx*zzz      ban any site containing the string 'xxx' followed by any\n\
                 number of other characters, followed by the string 'zzz'.\n\
                 IE: 'ban foo*oar' will ban foooar.edu, fooble.eoar.com, and\n\
                 foot.bath.oar.org, but not fooar.mil (because the two strings\n\
                 overlap, it's not a match).  Any number of '*' are legal, so you\n\
                 can get really creative (eg. a*b*c*d*e*f*g will ban any site\n\
                 having the first 7 letters of the alphabet in order, no matter\n\
                 how many characters are between the letters.)\n\
ban #xxx*zzz     as above, but only new characters are banned.\n\
ban ~xxx*zzz     as above, but only EMS registration of these are banned.\n\
ban yyy@xxx*zzz\n\
ban <anon>@xxx*zzz\n\
ban yyy@#xxx*zzz\n\
ban <anon>@#xxx*zzz\n\
                 For completeness, note that '*' is not legal in the username\n\
                 portion, only the sitename.  Also note, banning of a specific\n\
                 username alone, is not legal (mainly for reasons of cpu abuse),\n\
                 but you CAN 'ban annoyingtwit@.com' to catch him at any .com\n\
                 site.\n\n\
         NOTE:   after any of the above ban strings, you can (and encouraged to do\n\
                 so) add a short text string explaining the ban.  As follows:\n\
                 ban <ban string> <text>\n\
                 the only limit is the length of input (4 lines) but try to keep\n\
                 them short.  ban adds the name and level of the banner\n\
                 automatically, so you don't need to put those in the text.\n"

void do_ban (P_char ch, char *args, int cmd)
{
   char site[MAX_INPUT_LENGTH], user[MAX_INPUT_LENGTH], buf[MAX_STRING_LENGTH];
   int sites = 0, atpos, which;
   struct ban_t *tmp;

   if(!IS_TRUSTED (ch) || !ch->desc)
      return;

   /* eat leading spaces first */
   for(; isspace (*args); args++);

   if(*args == '?')
      {
      send_to_char (BAN_HELP, ch);
      return;
      }
   buf[0] = 0;

   if(!*args)
      {
      /* list the sites */
      if((tmp = ban_allowed) != NULL)
         {
         strcat (buf, "--------------------------------------\nAllowed this boot\n--------------------------------------\n");
         while(tmp)
            {
            sprintf (buf + strlen (buf), "     %12s %9s%c%c%s : %s\n",
                     tmp->banner,
                     *tmp->ban_user ? tmp->ban_user : " ", *tmp->ban_user ? '@' : ' ',
                     (tmp->ban_site[0] == '#') ? '#' : ' ',
                     (tmp->ban_site[0] == '#') ? (tmp->ban_site + 1) : tmp->ban_site,
                     *tmp->ban_str ? tmp->ban_str : "");
            tmp = tmp->next;
            }
         }
      if((tmp = ban_added) != NULL)
         {
         strcat (buf, "--------------------------------------\nNewly banned this boot\n--------------------------------------\n");
         while(tmp)
            {
            sprintf (buf + strlen (buf), "(%2d) %12s %9s%c%c%s : %s\n",
                     tmp->banner_lvl, tmp->banner,
                     *tmp->ban_user ? tmp->ban_user : " ", *tmp->ban_user ? '@' : ' ',
                     (tmp->ban_site[0] == '#') ? '#' : ' ',
                     (tmp->ban_site[0] == '#') ? (tmp->ban_site + 1) : tmp->ban_site,
                     *tmp->ban_str ? tmp->ban_str : "");
            tmp = tmp->next;
            }
         }
      strcat (buf, "--------------------------------------\nBanned sites\n--------------------------------------\n");
      if(bounce_null_sites)
         strcat (buf, "*** [Null sites being bounced] ***\n\n");
      if((tmp = ban_list) == NULL)
         {
         strcat (buf, "No sites currently banned.\n");
         send_to_char (buf, ch);
         return;
         }
      while(tmp)
         {
         sprintf (buf + strlen (buf), "(%2d) %12s %9s%c%c%s : %s\n",
                  tmp->banner_lvl, tmp->banner,
                  *tmp->ban_user ? tmp->ban_user : " ", *tmp->ban_user ? '@' : ' ',
                  (tmp->ban_site[0] == '#') ? '#' : ' ',
                  (tmp->ban_site[0] == '#') ? (tmp->ban_site + 1) : tmp->ban_site,
                  *tmp->ban_str ? tmp->ban_str : "");
         sites++;
         tmp = tmp->next;
         }
      if(sites == 1)
         {
         strcat (buf, "\nThere is 1 banned site.\n");
         }
      else
         {
         sprintf (buf + strlen (buf), "\nThere are %d banned sites.\n", sites);
         }
      page_string (ch->desc, buf, 1);
      return;
      }
   if(!str_cmp (args, "null"))
      {
      bounce_null_sites = 1;
      logit (LOG_WIZ, "%s bounces NULL sites", GET_NAME (ch));
      send_to_char ("Null sites will now be bounced.\n", ch);
      return;
      }
   /* ok, parse args into user and site and 'leftover' (which is the optional 'reason for ban').
      Valid formats:
      <site> [<reason>]
      <user>@<site> [<reason>]
    */

   for(atpos = 0, which = 0;;)
      {
      switch(*args)
         {
         case ' ':
            if(which == 0)
               {
               /* this is <site> <reason> */
               user[atpos] = 0;
               strcpy (site, user);
               atpos = 0;
               user[0] = 0;
               which = 2;
               args++;
               continue;
               }
            else if(which == 1)
               {
               /* this is <user>@<site> <reason> */
               site[atpos] = 0;
               atpos = 0;
               which = 2;
               args++;
               continue;
               }
            /* it's a space in the reason, just pass it through */
            break;

         case '@':
            if(which == 0)
               {
               which = 1;
               user[atpos] = 0;
               atpos = 0;
               args++;
               continue;
               }
            else if(which == 1)
               {
               /* '@'s are strictly illegal in sitenames, complain and bomb out. */
               send_to_char ("'@' is not valid WITHIN the sitename, aborting ban.\n", ch);
               return;
               }
            /* it's an '@' in the reason, just pass it through */
            break;

         case 0:
            if(which == 0)
               {
               if(!atpos)
                  {
                  /* very odd, this should never happen, moan pitifully and abort */
                  send_to_char ("Ban WHAT?  I don't think so.\n", ch);
                  return;
                  }
               /* ok, we have JUST <site>, no reason given. */
               user[atpos] = 0;
               strcpy (site, user);
               user[0] = 0;
               }
            else if(which == 1)
               {
               if(!atpos)
                  {
                  /* bleah, it's <user>@, not legal currently, bitch about it and abort */
                  send_to_char ("You must supply a site, username only is illegal.\n", ch);
                  return;
                  }
               /* we have <user>@<site>, no reason given. */
               }
            break;
         }

      if(which == 0)
         {
         user[atpos++] = *(args++);
         }
      else if(which == 1)
         {
         site[atpos++] = *(args++);
         }
      else
         {
         buf[atpos++] = *(args++);
         }
      if(!*(args - 1))
         break;
      }

   if(ban_str ("localhost", site) || ban_str ("127.0.0.1", site) || ban_str ("*********", site))
      {
      send_to_char ("'localhost' may not be banned.\n", ch);
      return;
      }
#ifdef MEM_DEBUG
   mem_use[MEM_BAN] += sizeof (struct ban_t);
#endif
   CREATE (tmp, struct ban_t, 1);
   tmp->banner = str_dup (GET_NAME (ch));
   tmp->ban_user = str_dup (user);
   tmp->ban_site = str_dup (site);
   tmp->ban_str = str_dup (buf);

   tmp->banner_lvl = GET_LEVEL (ch);

   if(ban_insert (&ban_list, tmp))
      {
      logit (LOG_WIZ, "%s bans %s%s%s", GET_NAME (ch),
             *tmp->ban_user ? tmp->ban_user : "", *tmp->ban_user ? "@" : "", tmp->ban_site);
      wizlog (GET_LEVEL (ch), "%s bans %s%s%s", GET_NAME (ch),
              *tmp->ban_user ? tmp->ban_user : "", *tmp->ban_user ? "@" : "", tmp->ban_site);
      send_to_char ("Ok.\n", ch);
      save_ban_file ();

      /* clone it for ban_added list */
#ifdef MEM_DEBUG
      mem_use[MEM_BAN] += sizeof (struct ban_t);
#endif
      tmp = NULL;
      CREATE (tmp, struct ban_t, 1);
      tmp->banner = str_dup (GET_NAME (ch));
      tmp->ban_user = str_dup (user);
      tmp->ban_site = str_dup (site);
      tmp->ban_str = str_dup (buf);

      tmp->banner_lvl = GET_LEVEL (ch);

      ban_insert (&ban_added, tmp);
      }
   else
      {
      send_to_char ("That user/site is already banned!\n", ch);
      }

}
#undef BAN_HELP

#define ALLOW_HELP "\
allow <ban string>\n\
                 remove a ban, you must type the ban string you wish to remove\n\
                 EXACTLY as it appears in the ban listing, or the command will\n\
                 fail.  Also, you cannot remove a ban placed by a someone who is\n\
                 higher level than you.\n"


void do_allow (P_char ch, char *args, int cmd)
{
   char site[MAX_INPUT_LENGTH], user[MAX_INPUT_LENGTH];
   int atpos, which;
   struct ban_t *curr, *prev;

   if(!IS_TRUSTED (ch) || !ch->desc)
      return;

   /* eat leading spaces first */
   for(; isspace (*args); args++);

   if(!*args)
      {
      send_to_char ("Remove which string from the ban list?\n", ch);
      return;
      }
   if(*args == '?')
      {
      send_to_char (ALLOW_HELP, ch);
      return;
      }
   if(!str_cmp (args, "null"))
      {
      bounce_null_sites = 0;
      send_to_char ("Null sites will now not be bounced.\n", ch);
      return;
      }
   if(ban_list == NULL)
      {
      send_to_char ("No sites are banned currently.\n", ch);
      return;
      }
   /* ok, parse args into user and site */

   for(atpos = 0, which = 0;;)
      {
      if(isspace (*args) && *args)
         {
         send_to_char ("Spaces and junk are not 'allowed'.\n", ch);
         return;
         }
      switch(*args)
         {
         case '@':
            if(which == 0)
               {
               which = 1;
               user[atpos] = 0;
               atpos = 0;
               args++;
               continue;
               }
            else
               {
               /* '@'s are strictly illegal in sitenames, complain and bomb out. */
               send_to_char ("'@' is not valid WITHIN the sitename, aborting allow.\n", ch);
               return;
               }

         case 0:
            if(which == 0)
               {
               if(!atpos)
                  {
                  /* very odd, this should never happen, moan pitifully and abort */
                  send_to_char ("Allow WHAT?  I don't think so.\n", ch);
                  return;
                  }
               /* ok, we have JUST <site>, no username. */
               user[atpos] = 0;
               strcpy (site, user);
               user[0] = 0;
               }
            else
               {
               if(!atpos)
                  {
                  /* bleah, it's <user>@, not legal currently, bitch about it and abort */
                  send_to_char ("You must supply a site, username only is illegal.\n", ch);
                  return;
                  }
               /* we have <user>@<site> */
               }
            break;
         }

      if(which == 0)
         {
         user[atpos++] = *(args++);
         }
      else
         {
         site[atpos++] = *(args++);
         }

      if(!*(args - 1))
         break;
      }

   for(curr = ban_list; curr; prev = curr, curr = curr->next)
      {
      if(!str_cmp (curr->ban_site, site) && (!*user || !str_cmp (curr->ban_user, user)))
         {

         /* replace banner name with the 'allower' name, and ban_lvl with their level */
         free_string (curr->banner);
         curr->banner = str_dup (GET_NAME (ch));
         curr->banner_lvl = GET_LEVEL (ch);

         /* remove it from ban_list */
         if(ban_list == curr)
            {
            ban_list = ban_list->next;
            }
         else
            {
            prev->next = curr->next;
            }

         /* and add it to 'allowed' bans list */
         ban_insert (&ban_allowed, curr);

         logit (LOG_WIZ, "%s allows %s%s%s", GET_NAME (ch),
                *curr->ban_user ? curr->ban_user : "", *curr->ban_user ? "@" : "", curr->ban_site);
         send_to_char ("Ok.\n", ch);
         save_ban_file ();
         return;
         }
      }

   send_to_char ("String not found in list!\n", ch);
}
#undef ALLOW_HELP


void do_secret (P_char ch, char *argument, int cmd)
{
   P_char dummy;
   P_obj obj = NULL;
   char buf[MAX_STRING_LENGTH];

   one_argument (argument, buf);
   if(generic_find (buf, FIND_OBJ_INV | FIND_OBJ_ROOM, ch, &dummy, &obj))
      {
      if(IS_SET (obj->extra_flags, ITEM_SECRET))
         {
         REMOVE_BIT (obj->extra_flags, ITEM_SECRET);
         send_to_char ("SECRET bit removed.\n", ch);
         }
      else
         {
         SET_BIT (obj->extra_flags, ITEM_SECRET);
         send_to_char ("SECRET bit set.\n", ch);
         }
      }
   else
      send_to_char ("Secretize what?\n", ch);
}

/* search the ban list for 'pattern' and output the list of matches. */

void do_lookup_bans (P_char ch, char *pattern)
{
   char *pos, o_buf[MAX_STRING_LENGTH];
   char title_str[MAX_INPUT_LENGTH], tbuf[MAX_INPUT_LENGTH], tbuf2[MAX_INPUT_LENGTH];
   int which = 4, found, title_shown, pat_len;
   struct ban_t *tmp;

   if(!*pattern)
      {
      send_to_char ("Remove which string from the ban list?\n", ch);
      return;
      }
   /* ok, this is somewhat trickier than just scanning a string, since we are scanning 3 strings, to make things
    * halfway sane, we output all three fields always (when one matches, of course), and highlight the matching
    * part.  */

   /* we scan all three ban lists (newly allowed, newly banned, and banned sites, and doctor up the output. */

   pat_len = strlen (pattern);
   o_buf[0] = 0;

   while(--which)
      {
      switch(which)
         {
         case 3:
            tmp = ban_allowed;
            title_shown = 0;
            strcpy (title_str, "--------------------------------------\nAllowed this boot\n--------------------------------------\n");
            break;
         case 2:
            tmp = ban_added;
            title_shown = 0;
            strcpy (title_str, "--------------------------------------\nNewly banned this boot\n--------------------------------------\n");
            break;
         case 1:
            tmp = ban_list;
            title_shown = 0;
            strcpy (title_str, "--------------------------------------\nBanned sites\n--------------------------------------\n");
            break;
         }

      while(tmp)
         {
         /* extreme cheating, but very efficient, we create a formatted string from our data, then search THAT, this
          * way we don't have to separately check each field. */

         sprintf (tbuf, "(%2d) %12s %9s%c%c%s : %s\n",
                  tmp->banner_lvl, tmp->banner,
                  *tmp->ban_user ? tmp->ban_user : " ", *tmp->ban_user ? '@' : ' ',
                  (tmp->ban_site[0] == '#') ? '#' : (tmp->ban_site[0] == '!') ? '!' : ' ',
                  ((tmp->ban_site[0] == '#') || (tmp->ban_site[0] == '!')) ? (tmp->ban_site + 1) : tmp->ban_site,
                  *tmp->ban_str ? tmp->ban_str : "");

         pos = tbuf;
         found = 0;
         while((pos = strstr (pos, pattern)))
            {
            found = 1;
            strcpy (tbuf2, tbuf);
            sprintf (tbuf2 + (pos - tbuf), "&+W%s&n%s", pattern, tbuf + ((pos - tbuf) + pat_len));
            strcpy (tbuf, tbuf2);
            pos += 5 + pat_len;
            }

         if(found)
            {
            if(!title_shown)
               {
               strcat (o_buf, title_str);
               title_shown = 1;
               }
            strcat (o_buf, tbuf);
            if(strlen (o_buf) + 150 > MAX_STRING_LENGTH)
               {
               strcat (o_buf, "And so on, and so forth...\n");
               break;
               }
            }
         tmp = tmp->next;
         }
      }

   if(o_buf[0])
      page_string (ch->desc, o_buf, 1);
   else
      send_to_char ("No matches.\n", ch);

   return;
}

/*
 * Look up an object based on a keyword, also lookup bans.
 *
 * Syntax : "lookup <room | mob | obj | bans> <search_string>"
 */

void do_lookup (P_char ch, char *argument, int cmd)
{
   FILE *fp;
   char o_buf[MAX_STRING_LENGTH];
   char arg[MAX_STRING_LENGTH], pattern[MAX_INPUT_LENGTH], buf[MAX_STRING_LENGTH],
   file[MAX_INPUT_LENGTH];
   char V_buf[MAX_STRING_LENGTH], K_buf[MAX_STRING_LENGTH], D_buf[MAX_STRING_LENGTH];
   int found, length, read_three = 0;

   if(IS_NPC (GET_PLYR(ch)) || !ch->desc)
      return;

   half_chop (argument, arg, buf);

   /* eat leading spaces first */
   for(length = 0; isspace (buf[length]); length++);

   strcpy (pattern, (buf + length));

   o_buf[0] = 0;

   if(!*arg || !*pattern)
      {
      send_to_char ("Syntax : lookup <room | zone | mob | obj | bans> <pattern>\n", ch);
      return;
      }
   /* ok, I changed this a tad, room and zon lookups are the same, searches the room name, object and mob
      lookups, however, search on 'name' (the keywords) and output Vnum and short desc.
      JAB */

   /* Search the files created via the gawk script (during boot),
    * for the desired pattern.  Output goes to a temp file, which
    * is then read in.
    */
   if(strncasecmp (arg, "mob", 1) == 0 || strncasecmp (arg, "char", 1) == 0)
      {
      strcpy (file, MOB_LOOKUP);
      read_three = 1;
      }
   else if(strncasecmp (arg, "obj", 1) == 0)
      {
      strcpy (file, OBJ_LOOKUP);
      read_three = 1;
      }
   else if(strncasecmp (arg, "room", 1) == 0)
      {
      strcpy (file, WLD_LOOKUP);
      }
   else if(strncasecmp (arg, "zone", 1) == 0)
      {
      strcpy (file, ZON_LOOKUP);
      }
   else if(strncasecmp (arg, "bans", 1) == 0)
      {
      do_lookup_bans (ch, pattern);   /* since the search will be very different, it's a separate function. */
      return;
      }
   else
      {
      send_to_char ("Syntax : lookup <room | zone | mob | obj> <pattern>\n", ch);
      return;
      }            /* if */

   if((fp = fopen (file, "r")) == NULL)
      {
      sprintf (buf, "Error opening %s", file);
      logit (LOG_FILE, buf);
      sprintf (buf, "Error opening %s...tell a forger.\n", file);
      send_to_char (buf, ch);
      return;
      }
   else
      {
      /*
       * Read in each line of the file.  See if the pattern
       * is in the line.  If so, pass it to the user.
       */
      found = 0;
      length = 0;
      strToLower (pattern);  /* lower case all values for comparison */
      for(;;)
         {
         if(fgets (V_buf, MAX_STRING_LENGTH - 1, fp) == NULL)
            break;
         if(fgets (K_buf, MAX_STRING_LENGTH - 1, fp) == NULL)
            break;
         if(read_three)
            if(fgets (D_buf, MAX_STRING_LENGTH - 1, fp) == NULL)
               break;
         strcpy (arg, K_buf);
         strToLower (K_buf);
         if((strstr (V_buf, pattern) != NULL) ||
            (strstr (K_buf, pattern) != NULL) ||
            (read_three && (strstr (D_buf, pattern) != NULL)))
            {
            found = 1;
            sprintf (buf, "%5d %s", atoi (V_buf + 1), (read_three) ? D_buf : arg);
            if((length + strlen (buf) + 40) > MAX_STRING_LENGTH)
               {
               strcat (o_buf, "...and the list goes on...\n");
               break;
               }
            else
               {
               length += strlen (buf);
               strcat (o_buf, buf);
               }
            }           /* if */
         }

      fclose (fp);

      if(!found)
         {
         sprintf (buf, "No matches found for pattern '%s'.\n", pattern);
         send_to_char (buf, ch);
         }
      else
         {         /* if */
         page_string (ch->desc, o_buf, 1);
         }
      }            /* if */

}           /* do_lookup */

/* inserts 'ban' into a sorted location in b_list .  Called by do_ban and read_ban_file.  Much easier than sorting
   the thing from scratch.  Returns 1 if 'ban' is inserted, 0 if it's discarded as a duplicate.  JAB */

int ban_insert (struct ban_t **b_list, struct ban_t *ban)
{
   char *site_p, *ban_p;
   int which;
   struct ban_t *ban_last, *ban_this;

   if(!b_list || !ban || !ban->ban_site)
      dump_core ();

   if(!*b_list)
      {
      *b_list = ban;
      ban->next = NULL;
      return 1;
      }
   else
      {
      /* we ignore the 'new char ban' character so the sites will match up */
      ban_p = (ban->ban_site[0] != '#') ? ban->ban_site : (ban->ban_site + 1);

      ban_last = ban_this = *b_list;
      while(ban_this)
         {
         /* we ignore the 'new char ban' character so the sites will match up */
         if(ban_this && ban_this->ban_site)
            {
            site_p = (ban_this->ban_site[0] != '#') ? ban_this->ban_site : (ban_this->ban_site + 1);
            }
         else
            dump_core ();     /* NULL site bans are currently illegal */

         /* this is faster in almost all cases, than just calling str_cmp */
         if(LOWER (*site_p) == LOWER (*ban_p))
            which = str_cmp (site_p, (const char *) ban_p);
         else if(*site_p < *ban_p)
            which = -1;
         else
            which = 1;

         switch(which)
            {

            case -1:       /* site_p < ban_p, shuffle pointers and try again. */
               ban_last = ban_this;
               ban_this = ban_this->next;
               break;

            case 1:        /* site_p > ban_p, insert our site before ban_this */
               goto do_it;
               break;

            case 0:        /* site_p = ban_p, do a ban_user sort to put it in the right place.  Dupes get punted. */
               if(!ban->ban_user || !*ban->ban_user)
                  {  /* no username, it goes first */
                  if(!ban_this->ban_user || !*ban_this->ban_user) /* it's a dupe, kill it */
                     return 0;
                  goto do_it;
                  }
               else
                  {
                  while(ban_this)
                     {
                     /* we ignore the 'new char ban' character so the sites will match up */
                     if(ban_this && ban_this->ban_site)
                        {
                        site_p = (ban_this->ban_site[0] != '#') ? ban_this->ban_site : (ban_this->ban_site + 1);
                        }
                     else
                        dump_core ();  /* NULL site bans are currently illegal */

                     if(str_cmp (ban_p, site_p))
                        goto do_it;

                     switch(str_cmp (ban_this->ban_user, (const char *) ban->ban_user))
                        {
                        case -1:      /* ban_this->ban_user < ban->ban_user, shuffle pointers and try again. */
                           ban_last = ban_this;
                           ban_this = ban_this->next;
                           break;

                        case 1:    /* ban_this->ban_user > ban->ban_user, insert our site before ban_this */
                           goto do_it;
                           break;

                        case 0:
                           return 0;      /* it's a dupe */
                        }
                     }
                  /* we are tail-end charlie */
                  goto do_it;
                  }
            }
         }
      }

   do_it:
   if(ban_this == ban_last)
      { /* we are the new head of ban_list */
      *b_list = ban;
      ban->next = ban_this;
      }
   else
      {
      ban_last->next = ban;
      ban->next = ban_this;
      }

   return 1;
}


void read_ban_file (void)
{
   FILE *f;
   char buf1[MAX_INPUT_LENGTH], buf2[MAX_INPUT_LENGTH], buf3[MAX_INPUT_LENGTH],
   buf4[MAX_INPUT_LENGTH];
   int tmp;
   struct ban_t *ban;

   f = fopen (BAN_FILE, "r");
   if(!f)
      {
      logit (LOG_FILE, "Could not open %s to read ban info.\n", BAN_FILE);
      return;
      }
   buf2[0] = 0;
   buf3[0] = 0;
   buf4[0] = 0;

   while(fscanf (f, "%s\n%d\n", buf1, &tmp) != EOF)
      {
      CREATE (ban, struct ban_t, 1);

      ban->banner = str_dup (buf1);
      ban->banner_lvl = tmp;

      /* fscanf won't read '\000' properly, so I'm using fgets, but fgets has the annoying property of
         including the '\n' in the string, so we have to nuke it. */
      fgets (buf2, MAX_INPUT_LENGTH, f);
      if(strstr (buf2, "\n"))
         buf2[strlen (buf2) - 1] = 0;
      ban->ban_user = str_dup (buf2);

      fgets (buf3, MAX_INPUT_LENGTH, f);
      if(strstr (buf3, "\n"))
         buf3[strlen (buf3) - 1] = 0;
      ban->ban_site = str_dup (buf3);

      fgets (buf4, MAX_INPUT_LENGTH, f);
      if(strstr (buf4, "\n"))
         buf4[strlen (buf4) - 1] = 0;
      ban->ban_str = str_dup (buf4);

      (void) ban_insert (&ban_list, ban);      /* adds ban in sorted order to ban_list */

      buf2[0] = 0;
      buf3[0] = 0;
      buf4[0] = 0;
      }

   fclose (f);
}

void save_ban_file (void)
{
   FILE *f;
   struct ban_t *i;

   f = fopen (BAN_FILE, "w");
   if(!f)
      {
      logit (LOG_FILE, "Could not open %s to save ban info.\n", BAN_FILE);
      return;
      }
   for(i = ban_list; i; i = i->next)
      {
      fprintf (f, "%s\n%d\n", i->banner, i->banner_lvl);
      fwrite (i->ban_user, (size_t) 1, (size_t) MAX (1, (int) strlen (i->ban_user)), f);
      fprintf (f, "\n%s\n", i->ban_site);
      fwrite (i->ban_str, (size_t) 1, (size_t) MAX (1, (int) strlen (i->ban_str)), f);
      fprintf (f, "\n");
      }

   fclose (f);
}

void do_lag (P_char ch, char *argument, int cmd)
{
   P_char victim;
   char buf[MAX_STRING_LENGTH], arg[MAX_STRING_LENGTH];
   int how_long = 0;

   if(IS_NPC (ch))
      {
      send_to_char ("Blearg.\n", ch);
      return;
      }
   half_chop (argument, arg, buf);

   if(!*arg)
      {
      send_to_char ("Mercilessly lag who? For how long?\n", ch);
      return;
      }
   if(!(GET_VICTIM_ROOM (victim, ch, arg)))
      {
      send_to_char ("No such person around.\n", ch);
      return;
      }
   if(is_number (buf))
      {
      how_long = atoi (buf);
      if(how_long < 1)
         {
         send_to_char ("Don't we all wish we could reduce lag? :(\n", ch);
         return;
         }
      if(how_long >= 4000)
         {
         send_to_char ("You might as well freeze them then.\n", ch);
         return;
         }
      if(ch == victim)
         {
         send_to_char ("You try to lag yourself, but you're too lagged...\n", ch);
         return;
         }
      if(GET_LEVEL (ch) <= GET_LEVEL (victim))
         {
         send_to_char ("You can't. 'Nuff said.\n", ch);
         return;
         }
      sprintf (buf, "You lag $N for %d seconds.", how_long);
      act (buf, FALSE, ch, 0, victim, TO_CHAR);
      CharWait (victim, WAIT_SEC * how_long);
      sprintf (buf, "<LAG>: %s lagged %s for %d seconds.", GET_NAME (ch), GET_NAME (victim), how_long);
      logit (LOG_WIZ, buf);
      return;
      }
   else
      {
      send_to_char ("That's not a number, goof!\n", ch);
      return;
      }
}

#define GRANT_SYNTAX \
"Syntax:  grant [<name>] [<command>|admin|areas|coder|www] [#|all]\n\n\
   grant  -- list syntax and all grantable commands.\n\
   grant <name>  -- list all commands already granted to <name>.\n\
   grant <name> <command>  -- grant <command> to <name>.\n\
   grant <name> admin|areas|coder|www|quest # -- give <name> all level #\n\
         admin/areas/coder commands.\n\
   grant <name> admin|areas|coder|www|quest all -- give <name> all admin/areas/coder/www\n\
         commands, up to <name>'s current level, and set the\n\
         autogrant flag for <name> that will automatically grant\n\
         new admin/areas/coder/www commands as <name> is advanced.\n\
         All three flags are set on Forgers.\n\n"

#define REVOKE_SYNTAX \
"Syntax:  revoke [<name>] [<command>|all|#|admin|areas|coder|www]\n\n\
   revoke  -- list syntax and all revokable commands.\n\
   revoke <name> <command>  -- revoke <command> from <name>.\n\
   revoke <name> all  -- revoke all granted commands from <name>.\n\
   revoke <name> #  -- revoke from <name> all level # granted commands.\n\
   revoke <name> admin|areas|coder|www|quest  -- revoke the autogrant flag from <name>\n\n"

int gr_idiotproof (P_char ch, P_char victim, char *name, int mode)
{
   char buf[MAX_INPUT_LENGTH];

   if(IS_NPC (ch))
      {
      send_to_char ("Wait till you're higher level...\n", ch);
      return(TRUE);
      }
   if(!*name)
      {
      if(mode == CMD_GRANT)
         send_to_char (GRANT_SYNTAX, ch);
      else
         send_to_char (REVOKE_SYNTAX, ch);

      return(TRUE);
      }
   if(!victim)
      {
      send_to_char ("Your victim is not among us.\n", ch);
      return(TRUE);
      }
   if(IS_NPC (victim))
      {
      sprintf (buf, "Hehe, when do mobs need such a command %sed?.\n",
               mode == CMD_GRANT ? "grant" : "revok");
      send_to_char (buf, ch);
      return(TRUE);
      }
   if(victim->desc && victim->desc->original)
      {
      strcpy (buf, "$N is not $Mself today. Wait until $E comes back!");
      act (buf, TRUE, ch, 0, victim, TO_CHAR);
      return(TRUE);
      }
   /* if ((GET_LEVEL(ch) <= GET_LEVEL(victim)) && (GET_LEVEL(ch) != FORGER)) {
      send_to_char("Perish the thought!  You aren't powerful enough to affect them!\n", ch);
      return TRUE;
      } */
   return(FALSE);
}

void do_grant (P_char ch, char *argument, int cmd)
{
   char name[MAX_INPUT_LENGTH], cmdstr1[MAX_INPUT_LENGTH], cmdstr2[MAX_INPUT_LENGTH];
   int cmdint, loopvar = 0, bytepos = -1, lvl = -1, found, no;
   P_char victim;
   char buf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];

   buf[0] = 0;
   argument = one_argument (argument, name);
   argument = one_argument (argument, cmdstr1);
   one_argument (argument, cmdstr2);

   victim = get_char_in_game_vis (ch, name, FALSE);

   if(gr_idiotproof (ch, victim, name, CMD_GRANT))
      {
      strcpy (buf, "List of grantable commands:\n");
      for(loopvar = 5; *grantable_bits[loopvar] != '\n'; loopvar++)
         {
         sprintf (buf + strlen (buf), "%14s", grantable_bits[loopvar]);
         if(!((loopvar - 2) % 6))
            strcat (buf, "\n");
         }
      strcat (buf, "\n");
      page_string (ch->desc, buf, 1);
      return;
      }
   if(!*cmdstr1)
      {
      if(GET_LEVEL (ch) < GET_LEVEL (victim))
         {
         strcpy (buf, "You are not holy enough to see that.");
         }
      else
         {
         if(IS_CSET (GRANT_FLAGS (victim), 1) ||
            IS_CSET (GRANT_FLAGS (victim), 2) ||
            IS_CSET (GRANT_FLAGS (victim), 3) ||
            IS_CSET (GRANT_FLAGS (victim), 4) ||
            IS_CSET (GRANT_FLAGS (victim), 5))
            sprintf (buf, "Autogrant flags:  %s%s%s%s%s\n\n",
                     (IS_CSET (GRANT_FLAGS (victim), 1)) ? "Admin " : "",
                     (IS_CSET (GRANT_FLAGS (victim), 2)) ? "Areas " : "",
                     (IS_CSET (GRANT_FLAGS (victim), 3)) ? "Coder " : "",
                     (IS_CSET (GRANT_FLAGS (victim), 4)) ? "WWW " : "",
                     (IS_CSET (GRANT_FLAGS (victim), 5)) ? "Quest " : "");
         strcat (buf, "Following commands granted:\n");

         for(no = 0, loopvar = 6; *grantable_bits[loopvar] != '\n'; loopvar++)
            {
            if(!IS_CSET (GRANT_FLAGS (victim), loopvar))
               continue;
            no++;
            sprintf (buf + strlen (buf), "%14s", grantable_bits[loopvar - 1]);
            if(!(no % 6))
               strcat (buf, "\n");
            }
         strcat (buf, "\n");
         page_string (ch->desc, buf, 1);
         return;
         }
      }
   else
      {
      for(loopvar = 0; *grantable_bits[loopvar] != '\n'; loopvar++)
         if(is_abbrev (cmdstr1, grantable_bits[loopvar]))
            {
            bytepos = loopvar;
            break;
            }
      if(bytepos < 0)
         {
         send_to_char ("That command cannot be granted! Sorry...\n", ch);
         return;
         }
      if(bytepos > 5)
         {     /* means it's a single command */
         if(*cmdstr2)
            {
            send_to_char ("The third argument only applies with admin|areas|coder|www.\n", ch);
            return;
            }
         cmdint = grant_cmds[bytepos][0];

         if(cmdint > 0)
            {
            if((ch == victim) && (GET_LEVEL (ch) != FORGER))
               {
               send_to_char ("Granting yourself something?  How generous of you!\n\n\n\n\n\n\n\n\n...NOT!\n", ch);
               return;
               }
            if(cmd_info[cmdint].minimum_level <= (int) GET_LEVEL (ch))
               {
               if(!IS_CSET (GRANT_FLAGS (victim), bytepos + 1))
                  SET_CBIT (GRANT_FLAGS (victim), bytepos + 1);
               else
                  {
                  if(ch != victim)
                     strcpy (buf, "They already have that command.\n.");
                  else
                     strcpy (buf, "You already have that command.\n");
                  send_to_char (buf, ch);
                  return;
                  }
               if(ch != victim)
                  {
                  sprintf (buf, "You grant $M the command '%s'.", command[cmdint - 1]);
                  act (buf, FALSE, ch, 0, victim, TO_CHAR);
                  sprintf (buf, "$n grants you the command '%s'.", command[cmdint - 1]);
                  act (buf, FALSE, ch, 0, victim, TO_VICT);
                  }
               else
                  {
                  sprintf (buf, "You grant yourself the command '%s'.", command[cmdint - 1]);
                  act (buf, FALSE, ch, 0, 0, TO_CHAR);
                  }
               do_save_silent (victim, 1);
               logit (LOG_WIZ, "<GRANT>: %s grants %s the command '%s'.",
                      GET_NAME (ch), GET_NAME (victim), command[cmdint - 1]);
               wizlog (GET_LEVEL (ch), "%s grants %s the command '%s'.",
                       GET_NAME (ch), GET_NAME (victim), command[cmdint - 1]);
               return;
               }
            }
         sprintf (buf, "What kind of command is that?!?!?\n");
         send_to_char (buf, ch);
         }
      else
         {         /* gave arg of admin|areas|coder */
         if(!*cmdstr2)
            {
            send_to_char ("The third argument is required with admin|areas|coder.\n", ch);
            return;
            }
         if(is_number (cmdstr2))
            {
            lvl = atoi (cmdstr2);
            if((lvl < 51) || (lvl > GET_LEVEL (ch)))
               {
               send_to_char ("# must be between 51 and 60, (and <= to your own level).\n", ch);
               return;
               }
            }
         else if(str_cmp ("all", cmdstr2))
            {
            send_to_char ("Third arg needs to be a level number or 'all'.\n", ch);
            return;
            }
         else
            lvl = 61;      /* flag value */

         if(lvl == 61)
            {
            if(!IS_CSET (GRANT_FLAGS (victim), bytepos + 1))
               {
               sprintf (buf, "Setting %s autogrant flag.\n", grantable_bits[bytepos]);
               SET_CBIT (GRANT_FLAGS (victim), bytepos + 1);
               }
            }
         /* Vaprak WAD changing loopvar from 3 to 4 to get www out of granted commands */
         for(found = 0, loopvar = 5; *grantable_bits[loopvar] != '\n'; loopvar++)
            if((((lvl != 61) && (grant_cmds[loopvar - 1][bytepos + 1] == lvl)) ||
                ((lvl == 61) && (grant_cmds[loopvar - 1][bytepos + 1] <= GET_LEVEL (victim)))) &&
               !IS_CSET (GRANT_FLAGS (victim), loopvar))
               {
               if(!found)
                  {
                  found = 1;
                  strcat (buf, "Granting the following commands:\n");
                  }
               SET_CBIT (GRANT_FLAGS (victim), loopvar);
               sprintf (buf + strlen (buf), "%s, ", grantable_bits[loopvar - 1]);
               }
         if(!*buf)
            {
            send_to_char ("Nothing new granted.\n", ch);
            return;
            }
         else
            {
            if(buf[strlen (buf) - 2] == ',')
               {
               buf[strlen (buf) - 2] = 0;
               strcat (buf, "\n");
               }
            do_save_silent (victim, 1);
            logit (LOG_WIZ, "<GRANT>: %s grants %s:\n%s", GET_NAME (ch), GET_NAME (victim), buf);
            wizlog (GET_LEVEL (ch), "%s grants %s:\n%s", GET_NAME (ch), GET_NAME (victim), buf);
            sprintf (buf2, "$n grants you greater powers!\n%s", buf);
            act (buf2, FALSE, ch, 0, victim, TO_VICT);
            send_to_char (buf, ch);
            }
         }
      }
   return;
}

void do_revoke (P_char ch, char *argument, int cmd)
{
   char name[MAX_INPUT_LENGTH], cmdstr1[MAX_INPUT_LENGTH];
   int cmdint, loopvar = 0, bytepos = -1, lvl = -1, found;
   P_char victim;
   char buf[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];

   buf[0] = 0;
   argument = one_argument (argument, name);
   one_argument (argument, cmdstr1);

   victim = get_char_in_game_vis (ch, name, FALSE);

   if(gr_idiotproof (ch, victim, name, CMD_REVOKE))
      {
      strcpy (buf, "List of revokable commands:\n");
      for(loopvar = 4; *grantable_bits[loopvar] != '\n'; loopvar++)
         {
         sprintf (buf + strlen (buf), "%13s", grantable_bits[loopvar]);
         if(!((loopvar - 2) % 6))
            strcat (buf, "\n");
         }
      strcat (buf, "\n");
      page_string (ch->desc, buf, 1);
      return;
      }
   if(!*cmdstr1)
      {
      send_to_char (REVOKE_SYNTAX, ch);
      return;
      }
   if(GET_LEVEL (ch) < GET_LEVEL (victim))
      {
      strcpy (buf, "You are not holy enough to see that.");
      return;
      }
   if(!str_cmp (cmdstr1, "all"))
      bytepos = -2;
   else if(is_number (cmdstr1))
      {
      bytepos = -(atoi (cmdstr1));
      if(GET_LEVEL (ch) < -bytepos)
         {
         send_to_char ("Sorry, you are too wimpy to do that.\n", ch);
         return;
         }
      else if(bytepos > -51)
         {
         send_to_char ("# must be in the range (51 - (your level)).\n", ch);
         return;
         }
      }
   else
      {
      for(loopvar = 0; *grantable_bits[loopvar] != '\n'; loopvar++)
         if(is_abbrev (cmdstr1, grantable_bits[loopvar]))
            {
            bytepos = loopvar;
            break;
            }
      if(bytepos < 0)
         {
         send_to_char ("Illegal argument.\n", ch);
         send_to_char (REVOKE_SYNTAX, ch);
         return;
         }
      }

   if(bytepos > 2)
      {    /* means it's a single command */

      cmdint = grant_cmds[bytepos][0];

      if(cmdint > 0)
         {
         if(ch == victim)
            send_to_char ("Revoking your own powers?  Well, ok, I guess!\n", ch);

         if(cmd_info[cmdint].minimum_level <= (int) GET_LEVEL (ch))
            {
            if(IS_CSET (GRANT_FLAGS (victim), bytepos + 1))
               REMOVE_CBIT (GRANT_FLAGS (victim), bytepos + 1);
            else
               {
               if(ch != victim)
                  strcpy (buf, "They don't have that command.\n.");
               else
                  strcpy (buf, "You don't have that command.\n");
               send_to_char (buf, ch);
               return;
               }

            if(ch != victim)
               {
               sprintf (buf, "You revoke $S '%s' command.", command[cmdint - 1]);
               act (buf, FALSE, ch, 0, victim, TO_CHAR);
               sprintf (buf, "$n revokes your '%s' command!", command[cmdint - 1]);
               act (buf, FALSE, ch, 0, victim, TO_VICT);
               }
            else
               {
               sprintf (buf, "You revoke your '%s' command.", command[cmdint - 1]);
               act (buf, FALSE, ch, 0, 0, TO_CHAR);
               }
            do_save_silent (victim, 1);
            logit (LOG_WIZ, "<REVOKE>: %s revokes %s's '%s' command.",
                   GET_NAME (ch), GET_NAME (victim), command[cmdint - 1]);
            wizlog (GET_LEVEL (ch), "%s revokes %s's '%s' command.",
                    GET_NAME (ch), GET_NAME (victim), command[cmdint - 1]);
            }
         }
      else
         {
         sprintf (buf, "What kind of command is that?!?!?\n");
         send_to_char (buf, ch);
         }
      return;
      }
   else
      {        /* gave arg of admin|areas|coder|www|all|# */
      switch(bytepos)
         {
         case 1:
         case 2:
         case 3:       /* revoking the autogrant flags */
         case 4:

            if(IS_CSET (GRANT_FLAGS (victim), bytepos))
               {
               sprintf (buf, "$n revokes your %s autogrant flag.", grantable_bits[bytepos]);
               REMOVE_CBIT (GRANT_FLAGS (victim), bytepos);
               logit (LOG_WIZ, "<REVOKE>: %s revokes %s's '%s' autogrant flag.",
                      GET_NAME (ch), GET_NAME (victim), grantable_bits[bytepos - 1]);
               wizlog (GET_LEVEL (ch), "%s revokes %s's '%s' autogrant flag.",
                       GET_NAME (ch), GET_NAME (victim), grantable_bits[bytepos - 1]);
               }
            else
               {
               send_to_char ("They don't have that autogrant flag.\n", ch);
               return;
               }
            break;
         case -2:         /* revoking everything */

            CLEAR_CBITS (GRANT_FLAGS (victim), GRANT_BYTES);
            strcpy (buf, "$n revokes ALL your granted commands!");
            logit (LOG_WIZ, "<REVOKE>: %s revokes ALL of %s's grants.", GET_NAME (ch), GET_NAME (victim));
            wizlog (GET_LEVEL (ch), "%s revokes ALL of %s's grants.", GET_NAME (ch), GET_NAME (victim));
            break;

         default:         /* revoking a whole level's worth */

            lvl = -bytepos;
            for(found = 0, loopvar = 6; *grantable_bits[loopvar] != '\n'; loopvar++)
               if((cmd_info[grant_cmds[loopvar][0]].minimum_level == lvl) &&
                  IS_CSET (GRANT_FLAGS (victim), loopvar))
                  {
                  if(!found)
                     {
                     found = 1;
                     strcat (buf, "Revoking the following commands:\n");
                     }
                  REMOVE_CBIT (GRANT_FLAGS (victim), loopvar);
                  sprintf (buf + strlen (buf), "%s, ", grantable_bits[loopvar]);
                  }
            if(*buf)
               {
               if(buf[strlen (buf) - 2] == ',')
                  {
                  buf[strlen (buf) - 2] = 0;
                  strcat (buf, "\n");
                  }
               logit (LOG_WIZ, "<REVOKE>: %s revokes from %s:\n%s", GET_NAME (ch), GET_NAME (victim), buf);
               wizlog (GET_LEVEL (ch), "%s revokes from %s:\n%s", GET_NAME (ch), GET_NAME (victim), buf);
               }
            break;
         }

      if(!*buf)
         {
         send_to_char ("Nothing revoked.\n", ch);
         return;
         }
      else
         {
         do_save_silent (victim, 1);
         sprintf (buf2, "$n revokes some of our powers!\n%s", buf);
         act (buf2, FALSE, ch, 0, victim, TO_VICT);
         send_to_char (buf, ch);
         }
      }
}
#undef GRANT_SYNTAX
#undef REVOKE_SYNTAX

void do_ptell (P_char ch, char *arg, int cmd)
{
   P_char vict, vict_t;
   char name[MAX_INPUT_LENGTH], msg[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   struct descriptor_data *d = NULL;
   struct descriptor_data *td = NULL;
   bool socket = FALSE, no_name = FALSE;
   int socket_num = 0;
   char sock_name[25];

   if(!(SanityCheck (ch, "do_ptell")))
      {
      logit (LOG_DEBUG, "do_ptell failed SanityCheck");
      return;
      }
   if(IS_NPC (ch))
      {
      send_to_char ("You try. . . . but you fail miserably.\n", ch);
      return;
      }
   half_chop (arg, name, msg);

   if(!*name || !*msg)
      {
      send_to_char ("To whom are you responding? And what are you telling them?\n", ch);
      return;
      }
   // Ptell to socket #
   if((socket_num = atoi(name)))
      {
      //socket_num = atoi(name);
      for(td = descriptor_list; td; td = td->next)
         {
         //if (!td->connected)
         //continue;
         if(td->descriptor == socket_num)
            {
            d = td;
            break;
            }
         }

      //debuglog(51, DS_SHEVARASH, "socket ptell: socket: %d name: %s match: %d d-conn: %d", socket_num, name, (d ? 1 : 0), (d->connected ? 1 : 0));

      if(!d || !(vict = d->character))
         {
         send_to_char("That socket can't hear you.\n", ch);
         return;
         }

      socket = TRUE;

      if(!(vict->player.name))
         no_name = TRUE;
      sprintf(sock_name, "Socket #%d", socket_num);
      }

   if(!socket && !(vict = get_char_vis (ch, name, ch->in_room)))
      {
      send_to_char ("Nobody by that name seems available.\n", ch);
      return;
      }
   if(IS_TRUSTED (vict) && (GET_WIZINVIS (vict) >= GET_LEVEL (ch)))
      {
      send_to_char ("Nobody by that name seems available.\n", ch);
      return;
      }
   if(ch == vict)
      {
      send_to_char ("You try to tell yourself something. Everybody cares. Really.\n", ch);
      return;
      }
   if(!no_name && IS_NPC (vict) && !vict->desc)
      {
      send_to_char ("What's the point?\n", ch);
      return;
      }
   if(!(vict->desc) && (!no_name && !(IS_CSET (vict->only.pc->pcact, PLR_MORPH))))
      {
      send_to_char ("That person can't hear you.\n", ch);
      return;
      }
   if(ch->desc)
      {
      if(IS_CSET (ch->only.pc->pcact, PLR_ECHO))
         {
         sprintf (Gbuf1, "&+rYou ptell %s '&+R%s&n&+r'\n", no_name ? sock_name : GET_NAME (vict), msg);
         send_to_char (Gbuf1, ch);
         }
      else
         send_to_char ("Ok.\n", ch);
      }
   sprintf (Gbuf1, "&+r%s responds to your petition with '&+R%s&n&+r'\n", ch->player.name, msg);

   /* Switch to morphed mob for ptell.. */
   if(!no_name && (IS_CSET (vict->only.pc->pcact, PLR_MORPH)) && vict->only.pc->switched)
      vict_t = vict->only.pc->switched;
   else
      vict_t = vict;

   send_to_char (Gbuf1, vict_t);

   for(d = descriptor_list; d; d = d->next)
      {
      if((STATE (d) == CON_PLYNG) && IS_TRUSTED (d->character) &&
         (IS_CSET (d->character->only.pc->pcact, PLR_PETITION) || IS_CSET (d->character->only.pc->pcact, PLR_PETITION2))
         && (d->character != vict) && (d->character != ch))
         {
         sprintf (Gbuf1, "&+rPetition: %s responds to %s with '&+R%s&n&+r'\n",
                  GET_NAME (ch), (no_name ? sock_name : GET_NAME (vict)), msg);
         send_to_char (Gbuf1, d->character);
         if(!no_name && IS_CSET (vict->only.pc->pcact, PLR_SILENCE))
            {
            sprintf (Gbuf2, "&+r%s is SILENCED! And cannot respond.&n\n", GET_NAME(vict));
            send_to_char (Gbuf2, d->character);
            }
         }
      }
   if(!no_name && IS_CSET (vict->only.pc->pcact, PLR_SILENCE))
      {
      sprintf (Gbuf2, "&+r%s is SILENCED! And cannot respond.&n\n", GET_NAME(vict));
      send_to_char (Gbuf2, ch);
      }
   return;
}

void do_finger (P_char ch, char *arg, int cmd)
{
   P_char t_ch;
   char Gbuf1[MAX_STRING_LENGTH], outcast[MAX_INPUT_LENGTH];
   unsigned int laston, timegone;

   if(!*arg || !arg)
      {
      send_to_char ("Usage:\n  finger playername\n", ch);
      return;
      }
   t_ch = GetNewChar (NEW_PC);
   t_ch->only.pc->aggressive = -1;

   skip_spaces(&arg);
   if((restoreCharOnly (t_ch, arg) < 0) || !t_ch)
      {
      send_to_char ("Pfile does not exist or is invalid.\n", ch);
      if(pfile_exists ("Players/Declined", arg))
         send_to_char ("That name has been declined.\n", ch);
      RemoveFromCharList (t_ch);
      free_char (t_ch);
      return;
      }
   if(GET_LEVEL (t_ch) > GET_LEVEL (ch))
      {
      send_to_char ("Sorry, you cannot finger those higher level than you.\n", ch);
      RemoveFromCharList (t_ch);
      free_char (t_ch);
      return;
      }
   sprintf (Gbuf1, "&+cName:&n %s %s &n&+cLevel:&n %d &+cClass:&n %s &n&+cRace:&n %s\n",
            GET_NAME (t_ch), GET_TITLE (t_ch) ? GET_TITLE (t_ch) : "", GET_LEVEL (t_ch),
            class_types[(int) GET_CLASS (t_ch)], race_types[(int) GET_RACE (t_ch)]);
   laston = t_ch->player.time.saved;
   timegone = (time (0) - laston) / SECS_PER_MUD_HOUR;
   send_to_char (Gbuf1, ch);

   /* Alignment/Outcast Status --CRM */
   IS_CSET(t_ch->only.pc->pcact, PLR_OUTCAST) ? strcpy(outcast, "&+rYes&N") : strcpy(outcast, "&+gNo&N");
   sprintf(Gbuf1, "&+cAlign:&N %d &N&+cOutcast: %s\n",  GET_ALIGNMENT(t_ch), outcast);
   send_to_char(Gbuf1, ch);

   sprintf (Gbuf1, "&+cLast saved:&n %s", asctime (localtime ((long *) &laston)));
   Gbuf1[strlen (Gbuf1) - 1] = 0;
   send_to_char (Gbuf1, ch);
   if(timegone > 10)
      {
      strcpy (Gbuf1, "  &n&+c(MIA:&n ");
      if(timegone > 1440)
         sprintf (Gbuf1 + strlen (Gbuf1), "%d day%s, ",
                  (int) (timegone / 1440), ((timegone / 1440) > 1) ? "s" : "");
      if((timegone % 1440) > 60)
         sprintf (Gbuf1 + strlen (Gbuf1), "%d hour%s, ",
                  (int) (timegone % 1440) / 60, (((timegone % 1440) / 60) > 1) ? "s" : "");
      if(timegone % 60)
         sprintf (Gbuf1 + strlen (Gbuf1), "%d minute%s, ",
                  (int) (timegone % 60), ((timegone % 60) > 1) ? "s" : "");
      Gbuf1[strlen (Gbuf1) - 2] = 0;
      strcat (Gbuf1, "&n)");
      }
   else
      *Gbuf1 = '\0';
   send_to_char (Gbuf1, ch);
   if(real_room (GET_HOME (t_ch)) == NOWHERE)
      strcpy (Gbuf1, "\n&+RLast saved in a now non-existent room.\n");
   else
      sprintf (Gbuf1, "\n&+cLast saved in [&n%d&+c]&n %s\n",
               world[real_room (GET_HOME (t_ch))].number, world[real_room (GET_HOME (t_ch))].name);
   send_to_char (Gbuf1, ch);
   sprintf (Gbuf1, "&+cLast login from:&n %s\n", GET_LAST_LOGIN (t_ch));
   send_to_char (Gbuf1, ch);

   if(pfile_exists ("Players/Declined", arg))
      send_to_char ("That name has been declined.\n", ch);

   RemoveFromCharList (t_ch);
   free_char (t_ch);
}

void do_undecline(P_char ch, char *arg, int cmd)
{
   char name[MAX_INPUT_LENGTH];
   FILE *f;
   char buf[256], *buff, Gbuf1[MAX_STRING_LENGTH];

   if(!ch || !IS_TRUSTED(ch))
      return;

   if(!arg)
      {
      send_to_char("Undecline what name?\n", ch);
      return;
      }

   one_argument(arg, name);

   if(*name == '\0')
      {
      send_to_char("Undecline what name?\n", ch);
      return;
      }

   strcpy(buf, name);
   buff = buf;
   for(; *buff; buff++);
   *buff = LOWER(*buff);

   sprintf(Gbuf1, "%s/%c/%s", "Players/Declined", buf[0], buf);

   if((f = fopen(Gbuf1, "w")) != NULL)
      {
      fclose(f);
      unlink(Gbuf1);
      send_to_char("Name removed from decline list.\n", ch);
      wizlog (GET_LEVEL (ch), "%s has removed %s from the decline list.", GET_NAME (ch), name);
      logit (LOG_WIZ, "%s has removed %s from decline.", GET_NAME (ch), name);
      }
   else
      {
      send_to_char("Name not in decline list.\n", ch);
      }

}

void do_deny (P_char ch, char *arg, int cmd)
{
   char name[MAX_INPUT_LENGTH];

   if(!ch || !IS_TRUSTED(ch))
      return;

   if(!arg)
      {
      send_to_char("Put what name into the decline list?\n", ch);
      return;
      }

   one_argument(arg, name);

   if(*name == '\0')
      {
      send_to_char("Put what name into the decline list?\n", ch);
      return;
      }

   deny_name(name);
   wizlog (GET_LEVEL(ch), "%s has added %s into the decline list.", GET_NAME(ch), name);
   logit (LOG_WIZ, "%s has added %s to the decline.", GET_NAME(ch), name);
}

void do_decline (P_char ch, char *arg, int cmd)
{
   char Gbuf[MAX_STRING_LENGTH], Gbuf2[MAX_STRING_LENGTH];
   char f_a[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH], Hold[MAX_STRING_LENGTH];
   char cmp_str[MAX_STRING_LENGTH];
   P_desc d, i;
   bool declineName = FALSE, nameHelp = FALSE;

   strcpy(Hold, arg);

   skip_spaces(&arg);

   if(!*arg || !arg)
      {
      send_to_char ("Usage: decline <charname> [reason]\n", ch);
      return;
      }

   arg = one_argument (arg, f_a);
   strcpy(cmp_str, arg);
   strToLower(cmp_str);

   for(d = descriptor_list; d; d = d->next)
      {
      if(STATE (d) == CON_ACCEPTWAIT && d->character && !str_cmp (GET_NAME (d->character), f_a))
         {
         if(!arg || !*arg)
            {
            declineName = TRUE;
            strcpy(arg, "<Standard Decline>");
            SEND_TO_Q ("\n\n&+CYour new character application has been declined.  Please pick a new name\n"
                       "&+Cappropriate for your race that follows these guidelines:\n\n"
                       "&+cNo names or words, misspelled or in any language, or contractions.  no silly names, no\n"
                       "&+cwords or names backwards, no compound words, no famous names, no fictitious names, no\n"
                       "&+cnames from TSR or FR etc.  No Oriental type names.  Please make an ORIGINAL fantasy name.\n", d);
            }
         else
            {

            /* Catch the Standard Declines -- CRM */

            if(!strcmp(cmp_str, " std") || !strcmp(cmp_str, " standard"))
               {
               declineName = TRUE;
               strcpy(arg, "<Standard Decline>");
               SEND_TO_Q ("\n\n&+CYour new character application has been declined.  Please pick a new name\n"
                          "&+Cappropriate for your race that follows these guidelines:\n\n"
                          "&+cNo names or words, misspelled or in any language, or contractions.  no silly names, no\n"
                          "&+cwords or names backwards, no compound words, no famous names, no fictitious names, no\n"
                          "&+cnames from TSR or FR etc.  No Oriental type names.  Please make an ORIGINAL fantasy name.\n", d);
               }
            else if(!strcmp(cmp_str, " tooclose") || !strcmp(cmp_str, " close"))
               {
               declineName = TRUE;
               strcpy(arg, "<Too Close to Another Name>");
               SEND_TO_Q ("\n\n&+CYour new character application has been declined because the name you picked\n"
                          "&+Cis too similar to another character's name.  Please pick a new name.\n", d);
               }
            else if(!strcmp(cmp_str, " disclaimer") || !strcmp(cmp_str, " disc"))
               {
               declineName = TRUE;
               strcpy(arg, "<Disclaimer>");
               SEND_TO_Q ("\n\n&+CYour new character application has been repeatedly declined.  Please take\n"
                          "&+Ca moment to read over the name disclaimer you agreed to and then choose a\n"
                          "&+Cnew name which conforms to those conventions.\n", d);
               }
            else if(!strcmp(cmp_str, " race"))
               {
               declineName = FALSE;
               nameHelp = TRUE;
               strcpy(arg, "<Race Decline>");
               SEND_TO_Q ("\n\n&+CYour new character application has been declined because the name you picked\n"
                          "&+Cis inappropriate for your character's race.  Please take a moment to read the\n"
                          "&+Cfollowing guidelines for your race, and try again.\n\n", d);
               }
            else if(!strcmp(cmp_str, " nameban"))
               {
               SET_CBIT(d->character->only.pc->pcact, PLR_NAMEBAN);
               declineName = TRUE;
               nameHelp = FALSE;
               strcpy(arg, "<Name Banned>");
               SEND_TO_Q("\n\n&+RYour new character application has been repeatedly declined.  We understand\n"
                         "&+Rthat it can be difficult to create an original fantasy name, but we do not allow\n"
                         "&+Rinappropriate names.  Please select one of the pre-generated names for your character.\n", d);
               }
            else
               {
               declineName = TRUE;
               nameHelp = TRUE;
               SEND_TO_Q ("\n\nYour new character application has been declined.\nReason supplied was:", d);
               SEND_TO_Q (arg, d);
               SEND_TO_Q ("\n\n", d);
               }
            }
         logit (LOG_NEWCHAR, "%s declined new char %s [%s]: %s.",
                GET_NAME (ch), GET_NAME (d->character), full_address (d, 60, 0), (arg ? arg : "NO REASON GIVEN"));
         sprintf (Gbuf1, "&+c*** STATUS: %s declined new player %s. (%s)\n",
                  GET_NAME (ch), GET_NAME (d->character), arg);
         sprintf (Gbuf2, "&+c*** STATUS: Someone declined new player %s. (%s)\n", GET_NAME (d->character), arg);
         STATE (d) = CON_NEW_NAME;
         Gbuf[0] = 0;
         switch(GET_RACE (d->character))
            {
            case RACE_HUMAN:
               strcpy (Gbuf, "NAMES HUMAN");
               break;
            case RACE_BARBARIAN:
               strcpy (Gbuf, "NAMES BARBARIAN");
               break;
            case RACE_DROW:
               strcpy (Gbuf, "NAMES DROW");
               break;
            case RACE_GREY:
               strcpy (Gbuf, "NAMES ELF");
               break;
            case RACE_MOUNTAIN:
               strcpy (Gbuf, "NAMES DWARF");
               break;
            case RACE_DUERGAR:
               strcpy (Gbuf, "NAMES DUERGAR");
               break;
            case RACE_HALFLING:
               strcpy (Gbuf, "NAMES HALFLING");
               break;
            case RACE_GNOME:
               strcpy (Gbuf, "NAMES GNOME");
               break;
            case RACE_OGRE:
               strcpy (Gbuf, "NAMES OGRE");
               break;
            case RACE_TROLL:
               strcpy (Gbuf, "NAMES TROLL");
               break;
            case RACE_HALFELF:
               strcpy (Gbuf, "NAMES HALF-ELF");
               break;
            case RACE_ILLITHID:
               strcpy (Gbuf, "NAMES ILLITHID");
               break;
            case RACE_YUANTI:
               strcpy (Gbuf, "NAMES YUAN-TI");
               break;
            default:
               break;
            }

         if(nameHelp)
            do_help (d->character, Gbuf, -4);

         d = nameEngine(d);
         Gbuf[0] = 0;
         SEND_TO_Q ("\n\nPlease enter another, more suitable fantasy name:", d);
         for(i = descriptor_list; i; i = i->next)
            if(!i->connected && i->character && IS_CSET (i->character->only.pc->pcact, PLR_PETITION) &&
               IS_TRUSTED (i->character))
               {
               if(!CAN_SEE (i->character, ch))
                  send_to_char (Gbuf2, i->character);
               else
                  send_to_char (Gbuf1, i->character);

               if(declineName)
                  deny_name (GET_NAME (d->character));
               }
         return;
         }
      }
   send_to_char ("User not found in Name acceptance queue, trying EMS database..\n", ch);

   sprintf(Gbuf1, " decline %s", Hold);
   do_ems(ch, Gbuf1, CMD_EMS);
}

extern int accept_mode;

void do_accept (P_char ch, char *arg, int cmd)
{
   int i, Count;
   P_desc d, j;
   char Gbuf2[MAX_STRING_LENGTH], Gbuf1[MAX_STRING_LENGTH];
   char Hold[MAX_STRING_LENGTH], StatHold[64];
   const char *accept_modes[] =
   {
      "off",
      "on",
      "\n"
   };

   if(!*arg || !arg)
      {
      /* list characters needing approval: */
      i = 0;
      sprintf (Gbuf1, "&+cAC: Post-creation approval system is now %s.\n", accept_modes[accept_mode]);
      send_to_char (Gbuf1, ch);
      if(accept_mode)
         {
         send_to_char ("List of character NAME's needing approval:\n", ch);
         for(d = descriptor_list; d; d = d->next)
            {
            if(STATE (d) == CON_ACCEPTWAIT)
               {
               sprintf (Gbuf1, "%s (%s %s %s) [%s]\n", GET_NAME (d->character),
                        (GET_SEX (d->character) == SEX_MALE) ? "Male" : "Female",
                        race_types[(int) GET_RACE (d->character)],
                        class_types[(int) GET_CLASS (d->character)],
                        full_address (d, 0, 1));
               send_to_char (Gbuf1, ch);
               i++;
               }
            }
         if(!i)
            send_to_char ("None.\n\n", ch);
         i = 0;

         send_to_char ("List of EMS accounts needing approval:\n", ch);
         for(Count = 1; Count <= Email_Registration_Database_Top; Count++)
            {
            if(EMSD[Count].Status == EMS_PENDING_ACCEPT)
               {
               i++;
               if(GET_LEVEL(ch) > 55)
                  sprintf(Gbuf1,
                          "&+y%d: Name: &+c%s&+y, Pin: &+c%d&+y, Email: &+c%s&+y, Status: &+c%s&+y\n",
                          Count, EMSD[Count].Name, EMSD[Count].PIN,
                          EMSD[Count].Email, GET_EMS_STATUS(StatHold, EMSD[Count].Status));
               else
                  sprintf(Gbuf1, "&+c%d: Name: &+c%s&+y, Status: &+c%s&+y\n",
                          Count, EMSD[Count].Name, GET_EMS_STATUS(StatHold, EMSD[Count].Status));
               send_to_char(Gbuf1, ch);
               }
            }
         sprintf(Gbuf1, "&+cTotal EMS accounts pending acceptance: &+C%d\n", i);
         send_to_char(Gbuf1, ch);
         }
      send_to_char ("Usage: accept <charname|on|off>\n", ch);
      return;
      }

   strcpy(Hold, arg);
   skip_spaces(&arg);
   switch(search_block (arg, strlen (arg), accept_modes, FALSE))
      {
      case 0:

         if(GET_LEVEL (ch) < FORGER)
            {
            send_to_char ("Sorry, that option is forger only.\n", ch);
            return;
            }
         accept_mode = 0;

         sprintf (Gbuf1, "&+cAC: %s set newchar application system %s.\n",
                  GET_NAME (ch), accept_modes[accept_mode]);
         sprintf (Gbuf2, "&+CAC: Someone set newchar application system %s.\n", accept_modes[accept_mode]);
         logit (LOG_WIZ, Gbuf1);
         for(j = descriptor_list; j; j = j->next)
            if(!j->connected && j->character && IS_CSET (j->character->only.pc->pcact, PLR_PETITION) &&
               IS_TRUSTED (j->character))
               {
               if(!CAN_SEE (j->character, ch))
                  send_to_char (Gbuf2, j->character);
               else
                  send_to_char (Gbuf1, j->character);
               }
         break;
      case 1:

         if(GET_LEVEL (ch) < FORGER)
            {
            send_to_char ("Sorry, that option is forger only.\n", ch);
            return;
            }
         accept_mode = 1;

         sprintf (Gbuf1, "&+cAC: %s set newchar application system %s.\n",
                  GET_NAME (ch), accept_modes[accept_mode]);
         logit (LOG_WIZ, Gbuf1);
         sprintf (Gbuf2, "&+cAC: Someone set newchar application system %s.\n", accept_modes[accept_mode]);
         for(j = descriptor_list; j; j = j->next)
            if(!j->connected && j->character && IS_CSET (j->character->only.pc->pcact, PLR_PETITION) &&
               IS_TRUSTED (j->character))
               {
               if(!CAN_SEE (j->character, ch))
                  send_to_char (Gbuf2, j->character);
               else
                  send_to_char (Gbuf1, j->character);
               }
         break;
      case -1:
         Count = 1;
         for(d = descriptor_list; d; d = d->next)
            {
            if(STATE (d) == CON_ACCEPTWAIT && d->character && !str_cmp (GET_NAME (d->character), arg))
               {
               logit (LOG_NEWCHAR, "%s accepted new char %s from %s.",
                      GET_NAME (ch), GET_NAME (d->character), full_address (d, 0, 0));
               sprintf (Gbuf1, "&+c*** STATUS: %s accepted new player %s from %s.\n",
                        GET_NAME (ch), GET_NAME (d->character), full_address (d, 0, 1));
               sprintf (Gbuf2, "&+c*** STATUS: Someone accepted new player %s from %s.\n",
                        GET_NAME (d->character), full_address (d, 0, 1));

               Count++;
               for(j = descriptor_list; j; j = j->next)
                  {
                  if(!j->connected && j->character &&
                     IS_CSET (j->character->only.pc->pcact, PLR_PETITION) &&
                     IS_TRUSTED (j->character))
                     {
                     if(!CAN_SEE (j->character, ch))
                        send_to_char (Gbuf2, j->character);
                     else
                        send_to_char (Gbuf1, j->character);
                     }
                  }

               SEND_TO_Q ("\nYour application for character has been accepted. Welcome into the ranks of\n"
                          "the players of Outcast!\n\n\n*** PRESS RETURN:\n", d);
               if(IS_ENABLED(CODE_EMS))
                  STATE (d) = CON_QRETURN;
               else
                  STATE (d) = CON_WELCOME;
               return;
               }
            }
         if(Count == 0)
            send_to_char ("User not found in Name acceptance queue, trying EMS database..\n", ch);

         sprintf(Gbuf1, " accept %s", Hold);
         do_ems(ch, Gbuf1, CMD_EMS);

         break;
      }
}


/* clone stuff - Valkur */

P_obj clone_obj (P_obj obj)
{
   P_obj ocopy;
   struct extra_descr_data *c_desc, *o_desc;

   ocopy = read_object (obj->R_num, REAL);

   /* copy */
   if(obj->name)
      {
      if(IS_SET (obj->str_mask, STRUNG_KEYS))
         {
         ocopy->name = str_dup (obj->name);
         SET_BIT (ocopy->str_mask, STRUNG_KEYS);
         }
      }
   if(obj->short_description)
      {
      if(IS_SET (obj->str_mask, STRUNG_DESC2))
         {
         ocopy->short_description = str_dup (obj->short_description);
         SET_BIT (ocopy->str_mask, STRUNG_DESC2);
         }
      }
   if(obj->description)
      {
      if(IS_SET (obj->str_mask, STRUNG_DESC1))
         {
         ocopy->description = str_dup (obj->description);
         SET_BIT (ocopy->str_mask, STRUNG_DESC1);
         }
      }
   /* awww, let's REALLY clone things. JAB */

   ocopy->value[0] = obj->value[0];
   ocopy->value[1] = obj->value[1];
   ocopy->value[2] = obj->value[2];
   ocopy->value[3] = obj->value[3];
   ocopy->value[4] = obj->value[4];
   ocopy->value[5] = obj->value[5];
   ocopy->value[6] = obj->value[6];
   ocopy->value[7] = obj->value[7];

   ocopy->wear_flags = obj->wear_flags;
   ocopy->extra_flags = obj->extra_flags;

   ocopy->cost = obj->cost;

   COPY_CBITS (ocopy->sets_affs, obj->sets_affs, AFF_BYTES);

   ocopy->affected[0].location = obj->affected[0].location;
   ocopy->affected[0].modifier = obj->affected[0].modifier;
   ocopy->affected[1].location = obj->affected[1].location;
   ocopy->affected[1].modifier = obj->affected[1].modifier;

   o_desc = obj->ex_description;
   while(o_desc)
      {
#ifdef MEM_DEBUG
      mem_use[MEM_E_DSCR] += sizeof (struct extra_descr_data);
#endif
      CREATE (c_desc, struct extra_descr_data, 1);

      c_desc->keyword = str_dup (o_desc->keyword);

      if(o_desc->description)
         {
         c_desc->description = str_dup (o_desc->description);
         }
      else
         {
         wizlog(51, "clone_obj(): detected a gimpy object with extra description keywords but no matching descriptions.");
         wizlog(51, "             obj vnum [%d]. Bug an AREA person to fix this object", obj_index[obj->R_num].virtual);
         o_desc->description = NULL;
         }

      c_desc->next = ocopy->ex_description;
      ocopy->ex_description = c_desc;
      o_desc = o_desc->next;
      }

   return ocopy;
}

void clone_container_obj (P_obj to, P_obj obj)
{
   P_obj tmp, ocopy;

   for(tmp = obj->contains; tmp; tmp = tmp->next_content)
      {
      ocopy = clone_obj (tmp);
      if(tmp->contains)
         clone_container_obj (ocopy, tmp);
      obj_to_obj (ocopy, to);
      }
}

void do_clone(P_char ch, char *argument, int cmd)
{
   P_char mob = NULL, mcopy = NULL;
   P_obj obj = NULL, ocopy = NULL;
   char type[MAX_STRING_LENGTH] = "", name[MAX_STRING_LENGTH] = "", buf[MAX_STRING_LENGTH] = "";
   int i = 0, j = 0, count = 0, where = 0;

   if(IS_NPC(ch))
      {
      send_to_char("Uh, no you can't clone something as a mob.\n", ch);
      return;
      }

   argument = one_argument(argument, type);
   if(!*type)
      {
      send_to_char("Usage: clone <mob|obj> <name> <count>\n", ch);
      return;
      }

   argument = one_argument(argument, name);
   if(!*name)
      {
      send_to_char("Usage: clone <mob|obj> <name> <count>\n", ch);
      return;
      }

   argument = one_argument(argument, buf);
   if(!*buf)
      count = 1;
   else
      count = atoi(buf);

   if(!count)
      {
      send_to_char("No count specified!  Assuming 1.\n", ch);
      count = 1;
      }

   /* keep them from doing too many on accident ;) */
   if(count > 100)
      {
      send_to_char("Max count set to 100 so Pook can't do it 2000 times. ;)\n", ch);
      return;
      }

   if(is_abbrev(type, "mobile") || is_abbrev(type, "character"))
      {
      if(!(mob = get_char_room_vis(ch, name)))
         {
         send_to_char("Can't find any such mobile!\n", ch);
         return;
         }

      if(IS_PC(mob))
         {
         send_to_char("Cloning a PC?!?!? Buahahahaha!!!!\n", ch);
         sprintf(buf, "%s just tried to clone YOU...*laugh*\n", GET_NAME(ch));
         send_to_char(buf, mob);
         return;
         }

      if(mob->nr < 0)
         {
         send_to_char("You can't clone that mob!!\n", ch);
         return;
         }

      for(i = 0; i < count; i++)
         {
         if(!(mcopy = read_mobile(mob->nr, REAL)))
            break;

         /* copy */
         if(mob->player.name)
            {
            if(IS_SET(mob->only.npc->str_mask, STRUNG_KEYS))
               {
               mcopy->player.name = str_dup(mob->player.name);
               SET_BIT(mcopy->only.npc->str_mask, STRUNG_KEYS);
               }
            }

         if(mob->player.short_descr)
            {
            if(IS_SET(mob->only.npc->str_mask, STRUNG_DESC2))
               {
               mcopy->player.short_descr = str_dup(mob->player.short_descr);
               SET_BIT(mcopy->only.npc->str_mask, STRUNG_DESC2);
               }
            }

         if(mob->player.long_descr)
            {
            if(IS_SET(mob->only.npc->str_mask, STRUNG_DESC1))
               {
               mcopy->player.long_descr = str_dup(mob->player.long_descr);
               SET_BIT(mcopy->only.npc->str_mask, STRUNG_DESC1);
               }
            }
         if(mob->player.description)
            {
            if(IS_SET(mob->only.npc->str_mask, STRUNG_DESC3))
               {
               mcopy->player.description = str_dup(mob->player.description);
               SET_BIT(mcopy->only.npc->str_mask, STRUNG_DESC3);
               }
            }

         /* clone EQ equiped */
         if(mob->equipment)
            {
            for(j = 0; j < MAX_WEAR; j++)
               {
               if(mob->equipment[j])
                  {
                  /* clone mob->equipment[j] */
                  ocopy = clone_obj(mob->equipment[j]);
                  if(mob->equipment[j]->contains)
                     clone_container_obj(ocopy, mob->equipment[j]);

                  equip_char(mcopy, ocopy, j, 0);
                  }
               }
            }

         /* clone EQ carried */
         if(mob->carrying)
            {
            for(obj = mob->carrying; obj; obj = obj->next_content)
               {
               ocopy = clone_obj(obj);
               if(obj->contains)
                  clone_container_obj(ocopy, obj);

               /* move obj to cloned mobs carrying */
               obj_to_char(ocopy, mcopy);
               }
            }

         /* put */
         char_to_room(mcopy, ch->in_room, -1);
         GET_BIRTHPLACE(mcopy) = world[ch->in_room].number;
         GET_HOME(mcopy) = GET_BIRTHPLACE(mcopy);

         if(IS_NPC_GUARD(mob) && !IS_CSET(mob->only.npc->npcact, ACT_SENTINEL))
            justice_register_guard(mcopy);

         act("$n has just made a clone of $N!", FALSE, ch, 0, mob, TO_ROOM);
         act("You make a clone of $N.", FALSE, ch, 0, mob, TO_CHAR);
         } /* end mob clone for */

      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just cloned %s %d times [&+C%d&N]", GET_NAME(ch),
                mob->player.short_descr, count, world[ch->in_room].number);
         logit(LOG_WIZ, "%s cloned %s %d times [&+C%d&N]", GET_NAME(ch),
               mob->player.short_descr, count, world[ch->in_room].number);
         }
      }
   else if(is_abbrev(type, "object"))
      {
      if((obj = get_obj_in_list_vis(ch, name, ch->carrying)))
         where = 1;
      else if((obj = get_obj_in_list_vis(ch, name, world[ch->in_room].contents)))
         where = 2;
      else
         {
         send_to_char("Can't find any such object!!\n", ch);
         return;
         }

      if(obj->R_num < 0)
         {
         send_to_char("You can't clone that object!!\n", ch);
         return;
         }

      for(i = 0; i < count; i++)
         {
         ocopy = clone_obj(obj);
         if(obj->contains)
            clone_container_obj(ocopy, obj);

         /* put */
         act("$n has just made a clone of $p!", FALSE, ch, obj, 0, TO_ROOM);
         act("You make a clone of $p.", FALSE, ch, obj, 0, TO_CHAR);
         if(where == 1)
            obj_to_char(ocopy, ch);
         else
            obj_to_room(ocopy, ch->in_room);
         }

      if(GET_LEVEL(ch) > MAXLVLMORTAL)
         {
         wizlog(GET_LEVEL(ch), "%s just cloned %s %d times [&+C%d&N]", GET_NAME(ch),
                obj->short_description ? obj->short_description : "No description", count, world[ch->in_room].number);
         logit(LOG_WIZ, "%s cloned %s %d times [&+C%d&N]", GET_NAME(ch),
               obj->short_description ? obj->short_description : "No description", count, world[ch->in_room].number);
         }
      }
   else
      {
      send_to_char("Usage: clone <mob|obj> <name> <count>\n", ch);
      return;
      }

   return;
}

/* end clone stuff - Valkur */

void do_knock (P_char ch, char *arg, int cmd)
{

   P_char victim;

   if(!ch || !arg || IS_NPC (ch) || !IS_TRUSTED (ch))
      return;

   if(!*arg)
      {
      send_to_char ("At whose door do you wish to knock?\n", ch);
      return;
      }
   if(!(victim = get_char_in_game_vis (ch, arg, FALSE)) || IS_NPC (victim))
      {
      send_to_char ("Sorry, no one around that fits that description.\n", ch);
      return;
      }
   if(ch == victim)
      {
      send_to_char ("You don't have to ask yourself to come in, silly!\n", ch);
      return;
      }
   if(!IS_TRUSTED (victim))
      {
      act ("$N's not a god, why knock?", FALSE, ch, 0, victim, TO_CHAR);
      return;
      }
   act ("You knock at $N's door...", FALSE, ch, 0, victim, TO_CHAR);
   act ("&+R*KNOCK KNOCK*&n  $n is knocking at your door.  Can $e come in?", FALSE, ch, 0, victim, TO_VICT);

   return;
}

#define INROOM_HELP "Eh?  try: \"inroom <string>\"\n\
  <string> should contain 1 occurrence of ONE of the following:\n\
    %p - all players in the room\n\
    %m - all mobs in the room\n\
    %a - both mobs and players in the room\n\
    %o - all objects in the room\n\
    %e - everything in the room\n\
  <string> will then be executed once for each pc and/or npc\n\
  in the room, replacing the % token with the char name.\n"

void do_inroom(P_char ch, char *args, int cmd)
{
   P_char vict, vict_next;
   P_obj obj, obj_next;
   char buf1[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH], *p, *q;
   int target;

   strcpy(buf1, args);     /* don't wanna change args directly */
   debuglog(51, DS_AZUTH, "inroom: args [%s]", args);

   if((p = strstr(buf1, "%")))
      {
      if((q = strstr(p+1, "%")))
         {
         /* error.. too many tokens provided */
         send_to_char(INROOM_HELP, ch);
         return;
         }
      }

   if((p = strstr(buf1, " tra"))) /* have to use tra to allow trip :( */
      {
      send_to_char("transfer command not allowed with inroom, try teleport instead.\n", ch);
      return;
      }

   if((p = strstr(buf1, " %p")))
      target = 1;
   else if((p = strstr(buf1, " %m")))
      target = 2;
   else if((p = strstr(buf1, " %a")))
      target = 3;
   else if((p = strstr(buf1, " %o")))
      target = 4;
   else if((p = strstr(buf1, " %e")))
      target = 5;
   else
      {
      /* error.. no valid token provided */
      send_to_char(INROOM_HELP, ch);
      return;
      }

   /* okay... now we have p pointing to the space before the % token.
      move it forward two places, and replace the letter with an "s"
      for use in sprintf */

   p += 2;
   *p = 's';

   /* okay.. now buf1 is setup as an argument for sprintf... */
   if(target != 4)
      {
      for(vict = world[ch->in_room].people; vict; vict = vict_next)
         {
         /* this is important due to removing from a linked list */
         vict_next = vict->next_in_room;

         if(ch == vict || !CAN_SEE(ch, vict))
            continue;
         if((IS_NPC(vict)) && (target == 1))
            continue;
         if((IS_PC(vict)) && (target == 2))
            continue;

         sprintf(buf2, buf1, FirstWord(GET_NAME(vict)));
         debuglog(51, DS_AZUTH, "inroom: people loop [%s]", buf2);
         command_interpreter(ch, buf2);
         }
      }

   if(target == 4 || target == 5)
      {
      for(obj = world[ch->in_room].contents; obj; obj = obj_next)
         {
         /* this is important due to removing from a linked list */
         obj_next = obj->next_content;

         if(IS_NOSHOW(obj))
            continue;

         sprintf(buf2, buf1, FirstWord(obj->name));
         debuglog(51, DS_AZUTH, "inroom: contents loop [%s]", buf2);
         command_interpreter(ch, buf2);
         }
      }
}

#define WHICH_SYNTAX "Syntax:\n   which room <zone flag>\n   which zone <zone flag>\n   which char|mob <mobact flag>\n   which obj|item <wear or extra flag>\n"

/* this is 'where' based on flags, so we can find all the 'peace' rooms, or 'no-ground' rooms, etc.
   It borrows code from both do_stat, and the setbit support functions. */

void do_which_room(P_char ch, char *arg);
void do_which_zone(P_char ch, char *arg);
void do_which_char(P_char ch, char *arg);
void do_which_obj(P_char ch, char *arg);

void do_which(P_char ch, char *args, int cmd)
{
   char arg1[MAX_INPUT_LENGTH] = "", arg2[MAX_INPUT_LENGTH] = "";

   args = two_arguments(args, arg1, arg2);

   if(!*arg1 || !*arg2)
      {
      send_to_char(WHICH_SYNTAX, ch);
      return;
      }

   switch(*arg1)
      {
      case 'r':
         do_which_room(ch, arg2);
         break;

      case 'z':
         do_which_zone(ch, arg2);
         break;

      case 'c':
      case 'm':
         do_which_char(ch, arg2);
         break;

      case 'o':
      case 'i':
         do_which_obj(ch, arg2);
         break;

      default:
         send_to_char(WHICH_SYNTAX, ch);
      }

   return;
}

void do_which_room(P_char ch, char *arg)
{
   char o_buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "";
   int i = 0, j = 0, room_nr = 0, o_len = 0;
   int printed = 0;

   for(i = 0; str_cmp(room_bits[i], arg) && (room_bits[i][0] != '\n'); i++)
      ;

   if(room_bits[i][0] == '\n')
      {
      printed = 0;
      send_to_char("Unknown flag, valid options are:\n", ch);
      for(j = 0; room_bits[j][0] != '\n'; j++)
         {
         if(room_bits[j][0] == '&')
            continue;

         sprintf(buf1 + strlen(buf1), "%-20s", room_bits[j]);
         printed++;
         if(!(printed % 4))
            strcat(buf1, "\n");
         }

      strcat(buf1, "\n");
      send_to_char(buf1, ch);
      return;
      }

   for(room_nr = 0; room_nr < top_of_world; room_nr++)
      {
      if(IS_CSET(world[room_nr].room_flags, RESERVED_OLC) && i != RESERVED_OLC)
         continue; // skip empty rooms, unless that's the ones they lookin for

      if(IS_CSET(world[room_nr].room_flags, i))
         {
         o_len += sprintf(buf1, "&+Y[&n%5d&+Y](&n%5d&+Y)&n %s\n", world[room_nr].number, room_nr, world[room_nr].name);
         if(o_len + 30 >= MAX_STRING_LENGTH)
            {
            strcat(o_buf, "And so on, and so forth...\n");
            break;
            }
         else
            strcat(o_buf, buf1);
         }
      }

   if(!*o_buf)
      send_to_char("No matches.\n", ch);
   else
      page_string(ch->desc, o_buf, 1);
}

void do_which_zone(P_char ch, char *arg)
{
   char o_buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "";
   int i = 0, j = 0, zone_nr = 0, o_len = 0;
   int printed = 0;

   for(i = 0; str_cmp(zone_bits[i], arg) && (zone_bits[i][0] != '\n'); i++)
      ;

   if(zone_bits[i][0] == '\n')
      {
      printed = 0;
      send_to_char("Unknown flag, valid options are:\n", ch);
      for(j = 0; zone_bits[j][0] != '\n'; j++)
         {
         if(zone_bits[j][0] == '&')
            continue;

         sprintf(buf1 + strlen(buf1), "%-20s", zone_bits[j]);
         printed++;
         if(!(printed % 4))
            strcat(buf1, "\n");
         }

      strcat(buf1, "\n");
      send_to_char(buf1, ch);
      return;
      }

   for(zone_nr = 0; zone_nr <= top_of_zone_table; zone_nr++)
      {
      if(zone_table[zone_nr].flags & (1 << i))
         {
         o_len += sprintf(buf1, "&+Y[&n%3d&+Y]&n %s\n", zone_nr, zone_table[zone_nr].name);
         if(o_len + 30 >= MAX_STRING_LENGTH)
            {
            strcat(o_buf, "And so on, and so forth...\n");
            break;
            }
         else
            strcat(o_buf, buf1);
         }
      }

   if(!*o_buf)
      send_to_char("No matches.\n", ch);
   else
      page_string(ch->desc, o_buf, 1);
}

void do_which_char(P_char ch, char *arg)
{
   P_char t_ch = NULL;
   char o_buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "";
   int i = 0, j = 0, o_len = 0, which = 0;
   int printed = 0;

   which = 0;
   for(i = 0; str_cmp(action_bits[i], arg) && (action_bits[i][0] != '\n'); i++)
      ;

   if(action_bits[i][0] == '\n')
      {
      which = 1;
      for(i = 0; str_cmp(player_bits[i], arg) && (player_bits[i][0] != '\n'); i++)
         ;

      if(player_bits[i][0] == '\n')
         {
         printed = 0;
         send_to_char("Unknown flag, valid options are:\n", ch);
         for(j = 0; action_bits[j][0] != '\n'; j++)
            {
            if(action_bits[j][0] == '&')
               continue;

            sprintf(buf1 + strlen(buf1), "%-20s", action_bits[j]);
            printed++;
            if(!(printed % 4))
               strcat(buf1, "\n");
            }

         for(j = 0; player_bits[j][0] != '\n'; j++)
            {
            if(player_bits[j][0] == '&')
               continue;

            sprintf(buf1 + strlen(buf1), "%-20s", player_bits[j]);
            printed++;
            if(!(printed % 4))
               strcat(buf1, "\n");
            }

         strcat(buf1, "\n");
         send_to_char(buf1, ch);
         return;
         }
      }

   if(which)
      {
      for(t_ch = PC_list; t_ch; t_ch = t_ch->next)
         {
         if(t_ch->desc && t_ch->desc->connected)
            continue;    /* not in game */

         if(!CAN_SEE(ch, t_ch))
            continue;

         if(IS_CSET(t_ch->only.pc->pcact, i))
            {
            o_len += sprintf(buf1, "%-30s &+Y[&n%5d&+Y]&n %s\n",
                             t_ch->player.name, world[t_ch->in_room].number, world[t_ch->in_room].name);
            if(o_len + 30 >= MAX_STRING_LENGTH)
               {
               strcat(o_buf, "And so on, and so forth...\n");
               break;
               }
            else
               strcat(o_buf, buf1);
            }
         }
      }
   else
      {
      for(t_ch = NPC_list; t_ch; t_ch = t_ch->next)
         {
         if(!CAN_SEE(ch, t_ch))
            continue;

         if(IS_CSET(t_ch->only.npc->npcact, i))
            {
            o_len += sprintf(buf1, "%-*s &+Y[&n%5d&+Y]&n %s\n", ansi_comp(t_ch->player.short_descr, 30), t_ch->player.short_descr,
                             world[t_ch->in_room].number, world[t_ch->in_room].name);
            if(o_len + 30 >= MAX_STRING_LENGTH)
               {
               strcat(o_buf, "And so on, and so forth...\n");
               break;
               }
            else
               strcat(o_buf, buf1);
            }
         }
      }

   if(!*o_buf)
      send_to_char("No matches.\n", ch);
   else
      page_string(ch->desc, o_buf, 1);
}

void do_which_obj(P_char ch, char *arg)
{
   P_obj t_obj = NULL, tobj = NULL;
   char o_buf[MAX_STRING_LENGTH] = "", buf1[MAX_STRING_LENGTH] = "";
   int i = 0, j = 0, o_len = 0, which = 0, found = 0;
   int printed = 0;

   which = 0;
   for(i = 0; (wear_bits[i][0] != '\n') && str_cmp(wear_bits[i], arg); i++)
      ;

   if(wear_bits[i][0] == '\n')
      {
      which = 1;
      for(i = 0; (extra_bits[i][0] != '\n') && str_cmp(extra_bits[i], arg); i++)
         ;

      if(extra_bits[i][0] == '\n')
         {
         which = 2;
         for(i = 0; (affected_bits[i][0] != '\n') && str_cmp(affected_bits[i], arg); i++)
            ;

         if(affected_bits[i][0] == '\n')
            {
            which = 3;
            for(i = 0; (apply_types[i][0] != '\n') && str_cmp(apply_types[i], arg); i++)
               ;

            if(apply_types[i][0] == '\n')
               {
               which = 4;
               for(i = 0; (anti_bits[i][0] != '\n') && str_cmp(anti_bits[i], arg); i++)
                  ;

               if(anti_bits[i][0] == '\n')
                  {
                  which = 5;
                  for(i = 0; (item_types[i][0] != '\n') && str_cmp(item_types[i], arg); i++)
                     ;

                  if(item_types[i][0] == '\n')
                     {
                     printed = 0;
                     send_to_char("Unknown flag, valid options are:\n", ch);
                     for(j = 0; wear_bits[j][0] != '\n'; j++)
                        {
                        if(wear_bits[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", wear_bits[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     for(j = 0; extra_bits[j][0] != '\n'; j++)
                        {
                        if(extra_bits[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", extra_bits[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     for(j = 0; affected_bits[j][0] != '\n'; j++)
                        {
                        if(affected_bits[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", affected_bits[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     for(j = 0; apply_types[j][0] != '\n'; j++)
                        {
                        if(apply_types[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", apply_types[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     for(j = 0; anti_bits[j][0] != '\n'; j++)
                        {
                        if(anti_bits[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", anti_bits[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     for(j = 0; item_types[j][0] != '\n'; j++)
                        {
                        if(item_types[j][0] == '&')
                           continue;

                        sprintf(buf1 + strlen(buf1), "%-20s", item_types[j]);
                        printed++;
                        if(!(printed % 4))
                           strcat(buf1, "\n");
                        }

                     strcat(buf1, "\n");
                     page_string(ch->desc, buf1, 1);
                     return;
                     }
                  }
               }
            }
         }
      }

   for(t_obj = object_list; t_obj; t_obj = t_obj->next)
      {
      /* heh, this is ugly, but we don't want people finding wizinvis gods via their objects. JAB */
      for(tobj = t_obj; OBJ_INSIDE(tobj); tobj = tobj->loc.inside)
         ;

      if((OBJ_WORN(tobj) && WIZ_INVIS(ch, tobj->loc.wearing)) ||
         (OBJ_CARRIED(tobj) && WIZ_INVIS(ch, tobj->loc.carrying)))
         continue;

      found = FALSE;
      if(which == 0)
         {
         if(t_obj->wear_flags & (1 << i))
            found = TRUE;
         }
      else if(which == 1)
         {
         if(t_obj->extra_flags & (1 << i))
            found = TRUE;
         }
      else if(which == 2)
         {
         if(IS_CSET(t_obj->sets_affs, i))
            found = TRUE;
         }
      else if(which == 3)
         {
         for(j = 0; j < 2; j++)
            {
            if(t_obj->affected[j].location == i)
               found = TRUE;
            }
         }
      else if(which == 4)
         {
         if(IS_SET(t_obj->anti_flags, (1 << i)))
            found = TRUE;
         }
      else
         {
         if(GET_ITEM_TYPE(t_obj) == i)
            found = TRUE;
         }

      if(found)
         {
         if(!t_obj->short_description)
            o_len += sprintf(buf1, "[%5d] %-*s %s\n", obj_index[t_obj->R_num].virtual,
                             30, "No description", where_obj(t_obj, FALSE));
         else
            o_len += sprintf(buf1, "[%5d] %-*s %s\n", obj_index[t_obj->R_num].virtual,
                             ansi_comp(t_obj->short_description, 30), t_obj->short_description, where_obj(t_obj, FALSE));

         if(o_len + 30 >= MAX_STRING_LENGTH)
            {
            strcat(o_buf, "And so on, and so forth...\n");
            break;
            }
         else
            strcat(o_buf, buf1);
         }
      }

   if(!*o_buf)
      send_to_char("No matches.\n", ch);
   else
      page_string(ch->desc, o_buf, 1);
}

#undef WHICH_SYNTAX

#define REVOKETITLE_SYNTAX "Syntax:\n revoketitle <char name>\n"

/* Guild-God Command.

   This command allows guild god(s) to revoke from any player the
   ability to 'title.'  It is made a separate command to restrict
   gods from revoking just any command--since revoke is reserved for
   gods level 59 and up..

   Some of this code was borrowed from do_revoke() [naturally..]
 */

void do_revoketitle (P_char ch, char *args, int cmd)
{
   P_char victim;
   char buf[MAX_STRING_LENGTH];
   char name[MAX_INPUT_LENGTH];
   int loopvar = 0, bytepos = -1;

   buf[0] = 0;

   if(!args || !*args)
      {
      send_to_char (REVOKETITLE_SYNTAX, ch);
      return;
      }
   (void) one_argument (args, name);
   victim = get_char_in_game_vis (ch, name, FALSE);

   if(!victim)
      {
      send_to_char ("Who?\n", ch);
      return;
      }
   if(GET_LEVEL (ch) < GET_LEVEL (victim))
      {
      send_to_char ("Not so fast wise-guy.\n", ch);
      return;
      }
   if(ch == victim)
      send_to_char ("Revoking your own powers?  Well, ok, I guess!\n", ch);

   /* just in case the title command moves, or is taken out (?!?) */

   for(loopvar = 0; *grantable_bits[loopvar] != '\n'; loopvar++)
      if(is_abbrev ("title", grantable_bits[loopvar]))
         {
         bytepos = loopvar;
         break;
         }
   if(bytepos < 0)
      {
      send_to_char ("This 'title' command does not exist anymore!\n", ch);
      return;
      }
   /* its assumed whoever had this power was GRANTED the command.
      this is a minimum_level 59 command, noone by a handful have
      access to this command. */

   if(IS_CSET (GRANT_FLAGS (victim), bytepos))
      REMOVE_CBIT (GRANT_FLAGS (victim), bytepos);
   else
      {
      if(ch != victim)
         strcpy (buf, "They don't have the 'title' command.\n");
      else
         strcpy (buf, "You don't have the 'title' command.\n");
      send_to_char (buf, ch);
      return;
      }

   if(ch != victim)
      {
      act ("You revoke $S 'title' command.", FALSE, ch, 0, victim, TO_CHAR);
      act ("$n revokes your 'title' command!", FALSE, ch, 0, victim, TO_VICT);
      }
   else
      act ("You revoke your 'title' command.", FALSE, ch, 0, 0, TO_CHAR);

   do_save_silent (victim, 1);

   logit (LOG_WIZ, "<REVOKE>: %s revokes %s's 'title' command.", GET_NAME (ch), GET_NAME (victim));
   wizlog (GET_LEVEL (ch), "%s revokes %s's 'title' command.", GET_NAME (ch), GET_NAME (victim));
}

#undef SYNTAX_REVOKETITLE

/* Guild-God command.

   This command acts as a filter, calling do_setbit() after it
   rebuilds the command to set the hometown.
 */

#define SYNTAX_SETHOME "Syntax:\n   sethome <char> <home flags> <room>\n"

void do_sethome (P_char ch, char *args, int cmd)
{
   char *p, name[MAX_INPUT_LENGTH], hflg[MAX_INPUT_LENGTH];
   char val[MAX_INPUT_LENGTH], buf[MAX_STRING_LENGTH];
   int i, hflg_index = -1;
   static char homeflags[3][16] =
   {"hometown", "birthplace", "\n"};
   static char setbit_flags[2][16] =
   {"home", "orighome"};

   args = one_argument (args, name);
   args = one_argument (args, hflg);
   (void) one_argument (args, val);

   if(!name[0])
      {
      send_to_char (SYNTAX_SETHOME, ch);
      return;
      }
   if(hflg[0])
      {
      for(p = hflg; *p; p++)
         *p = tolower (*p);

      for(i = 0; homeflags[i][0] != '\n'; i++)
         {
         if(is_abbrev (hflg, homeflags[i]))
            {
            hflg_index = i;
            break;
            }
         }
      }
   if(!hflg[0] || hflg_index == -1)
      {
      send_to_char (SYNTAX_SETHOME, ch);
      send_to_char ("\nValid home flags are:\n", ch);
      for(i = 0; homeflags[i][0] != '\n'; i++)
         {
         sprintf (buf, "\t%s\n", homeflags[i]);
         send_to_char (buf, ch);
         }
      return;
      }
   /* here we have at least 2 arguments, the 2nd being a valid 'home flag' */

   sprintf (buf, "char %s %s %s", name, setbit_flags[hflg_index], val);
   do_setbit (ch, buf, CMD_SETHOME);
}

#if 0          /* don't do anything with this yet (neb) */
void finish_notes (P_desc, int, char *);

void
do_notes (P_char ch, char *arg, int cmd)
{
   char Gbuf1[MAX_STRING_LENGTH];
   char name[MAX_INPUT_LENGTH];
   FILE *f1;
   int ct;
   struct tm *lt;
   char *tmstr, *buf;

   if(!*arg || !arg)
      {
      send_to_char ("Usage:\n  notes playername\n", ch);
      return;
      }
   if(!finger_foo)
      {
#ifdef MEM_DEBUG
      mem_use[MEM_PC] += (sizeof (struct char_data) + sizeof (struct pc_only_data));
#endif
      CREATE (finger_foo, struct char_data, 1);
      CREATE (finger_foo->only.pc, struct pc_only_data, 1);
      }
   skip_spaces(&arg);
   if(restoreCharOnly (finger_foo, arg) < 0 || !finger_foo)
      {
      if(finger_foo && finger_foo->events)
         ClearCharEvents (finger_foo);
      send_to_char ("Invalid player name.\n", ch);
      return;
      }
   if(GET_LEVEL (finger_foo) > GET_LEVEL (ch))
      {
      send_to_char ("If you have something to say about them, say it to their face!\n", ch);
      if(finger_foo && finger_foo->events)
         ClearCharEvents (finger_foo);
      return;
      }
   if(finger_foo && finger_foo->events)
      ClearCharEvents (finger_foo);

   /* okay... its a valid player name... */

   ct = time (0);
   lt = localtime (&ct);
   tmstr = asctime (lt);
   *(tmstr + strlen (tmstr) - 6) = '\0';

   /* read any existing notes.. */

   strcpy (name, arg);

   for(buf = name; *buf; buf++)
      *buf = LOWER (*buf);
   sprintf (Gbuf1, "Players/Notes/%c/%s", name[0], name);

   buf = str_dup (Gbuf1);

   if((f1 = fopen (Gbuf1, "r")) != NULL)
      {
      Gbuf1[fread (Gbuf1, 1, MAX_STRING_LENGTH - 90, f1)] = '\0';
      fclose (f1);
      strcat (Gbuf1, "\n");
      }
   else
      Gbuf1[0] = '\0';

   sprintf (Gbuf1 + strlen (Gbuf1), "&+W>>%s: ", tmstr + 3);

   if(get_char (name))
      {
      P_char foo = get_char (name);
      sprintf (Gbuf1 + strlen (Gbuf1), "%s", full_address (foo, 0, 1));
      }
   strcat (Gbuf1, "&n");
   /* now.. put Gbuf1 into the editor! */

   start_editor (ch->desc, Gbuf1, MAX_STRING_LENGTH - 2, finish_notes, (int) buf);
   return;
}

void
finish_notes (P_desc d, int mark, char *text)
{
   char *buf;
   FILE *f1;

   buf = (char *) mark;

   if(text)
      {
      if(!(*text))
         {
         unlink (buf);
         SEND_TO_Q ("\nNotes file deleted\n", d);
         }
      else if((f1 = fopen (buf, "w")) != NULL)
         {
         fwrite (text, 1, strlen (text), f1);
         fclose (f1);
         SEND_TO_Q ("\nNotes saved.\n", d);
         }
      else
         {
         SEND_TO_Q ("\nUnable to create notes file!  PANIC!\n", d);
         }
      }
   free (buf);
   free (text);
   return;
}
#endif /* if 0 */

void do_setpk (P_char ch, char *args, int cmd)
{
   send_to_char ("This command is on hold pending a review for bugs\n", ch);
   return;

#if 0
   char character[MAX_STRING_LENGTH] = "", onoff[MAX_STRING_LENGTH] = ""; //, buff[MAX_STRING_LENGTH] = "";
   P_char vict;
   int onOff = 0;

   if(!ch || !args || IS_NPC (GET_PLYR(ch)) || !IS_TRUSTED (ch))
      return;

   args = one_argument(args, character);

   if(!*character)
      {
      send_to_char ("Usage: setpk  <player> <#>\n1 is ON, 0 is OFF\n", ch);
      return;
      }

   if(!(vict=get_char_in_game_vis(ch, character, TRUE)))
      {
      send_to_char ("That person doesn't exist!!!\n", ch);
      return;
      }

   if(GET_LEVEL(ch) < GET_LEVEL(vict))
      {
      send_to_char ("Trying to pkill your superiors?  Naughty you!\n", ch);
      act ("$n &+Wtried to set you to &+LPkill.&N  &+rKill them all&N\n", FALSE, ch, 0, vict, TO_VICT);
      return;
      }

   args = one_argument(args, onoff);

   if(!*onoff)
      {
      send_to_char("You have to specify 1 or 0, for On/Off, to set PK\n", ch);
      return;
      }
   else
      {

      onOff = atoi(onoff);

      switch(onOff)
         {
         case 0:
            vict->only.pc->quest_portal = 0;
            vict->only.pc->acheron_portal = 0;
            break;

         case 1:
            vict->only.pc->quest_portal = GET_BIRTHPLACE(vict);
            vict->only.pc->acheron_portal = GET_BIRTHPLACE(vict);
            break;


         default:
            send_to_char("You have to specify 1 or 0, for On/Off, to set PK\n", ch);
            return;

         }
      }

   wizlog (GET_LEVEL (GET_PLYR(ch)), "%s set %s pkill flags %s", GET_NAME (GET_PLYR(ch)), GET_NAME(GET_PLYR(vict)), onoff);
   return;
#endif
}

/* this command allows a god to bestow all sorts of protections/enhancements on a character with one command.
 * it basically just parses the args, then calls the spells with those args, all at once.  It's real good for
 * quest preparation or testing things, the group won't have to 'spell up'.  JAB */

void do_prep (P_char ch, char *args, int cmd)
{
   P_char victim;

   if(!ch || !args || IS_NPC (GET_PLYR(ch)) || !IS_TRUSTED (ch))
      return;

   if(!*args)
      {
      send_to_char ("Prep who?\n", ch);
      return;
      }
   if(!(victim = get_char_room_vis (ch, args)))
      {
      send_to_char ("Sorry, no one here fits that description.\n", ch);
      return;
      }
   wizlog (GET_LEVEL (GET_PLYR(ch)), "%s preps %s in %d", GET_NAME (GET_PLYR(ch)), GET_NAME (victim), world[ch->in_room].number);

   /* ok, have the victim, now we just spam him/her with spells. */

   spell_armor (GET_LEVEL (ch), ch, victim, NULL);
   spell_barkskin (GET_LEVEL (ch), ch, victim, NULL);
   spell_bless (GET_LEVEL (ch), ch, victim, NULL);
   spell_true_sight (GET_LEVEL (ch), ch, victim, NULL);
   spell_dexterity (GET_LEVEL (ch), ch, victim, NULL);
   spell_dexterity (GET_LEVEL (ch), ch, victim, NULL);
   spell_dexterity (GET_LEVEL (ch), ch, victim, NULL);
   spell_farsee (GET_LEVEL (ch), ch, victim, NULL);
   spell_fly (GET_LEVEL (ch), ch, victim, NULL);
   spell_globe (GET_LEVEL (ch), ch, victim, NULL);
   spell_haste (GET_LEVEL (ch), ch, victim, NULL);
   spell_infravision (GET_LEVEL (ch), ch, victim, NULL);
   spell_invisibility (GET_LEVEL (ch), ch, victim, NULL);
   spell_prot_from_undead (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_acid (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_cold (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_evil (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_fire (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_gas (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_good (GET_LEVEL (ch), ch, victim, NULL);
   spell_protection_from_lightning (GET_LEVEL (ch), ch, victim, NULL);
   spell_sense_life (GET_LEVEL (ch), ch, victim, NULL);
   spell_dragonscales (GET_LEVEL (ch), ch, victim, NULL);
   spell_displacement (GET_LEVEL (ch), ch, victim, NULL);
   spell_strength (GET_LEVEL (ch), ch, victim, NULL);
   spell_strength (GET_LEVEL (ch), ch, victim, NULL);
   spell_strength (GET_LEVEL (ch), ch, victim, NULL);
   spell_vitality (GET_LEVEL (ch), ch, victim, NULL);
   spell_waterbreath (GET_LEVEL (ch), ch, victim, NULL);
   spell_mind_blank (GET_LEVEL (ch), ch, victim, NULL);
   spell_blur (GET_LEVEL (ch), ch, victim, NULL);
   spell_mirror_image (GET_LEVEL (ch), victim, victim, NULL);
   spell_elemental_earth (GET_LEVEL (ch), victim, victim, NULL);
   spell_elemental_ward (GET_LEVEL (ch), ch, victim, NULL);

   do_restore (ch, args, cmd);

}

void displayCodeControlSyntax (P_char ch)
{
   char Gbuf1[MAX_STRING_LENGTH];

   sprintf (Gbuf1, "Code Command Syntax:\n\n"
            "Code [CODE_FEATURE] [1 | 0]\n"
            "     Both fields are optional, second field is either 1 or 0\n"
            "     to enable or disable given feature.\n\n"
            "Code Status:\n\n");

   createCodeControlList (Gbuf1);

   send_to_char (Gbuf1, ch);
   return;
}

void createCodeControlList (char *buf)
{
   int i;

   sprintf (buf + strlen (buf), "&+LCode Control Status:\n\n");

   for(i = 0; cctags[i].name; i++)
      sprintf (buf + strlen (buf), "&+L%-20s: %s\n", cctags[i].name,
               IS_CSET (sets_code_control, cctags[i].type) ? "ON" : "OFF");

   strcat (buf, "\n");

   return;
}

void do_code (P_char ch, char *args, int cmd)
{
   int i, tagNum = 0, onOff = -1;
   char name[MAX_STRING_LENGTH], onoff[MAX_STRING_LENGTH];

   name[0] = 0;
   onoff[0] = 0;

   if(!ch || !IS_TRUSTED (ch))
      return;

   if(!*args)
      {
      displayCodeControlSyntax (ch);
      return;
      }
   /* get the tag name and on or off specifier */
   half_chop (args, name, onoff);

   if(!*name)
      {
      createCodeControlList (name);
      send_to_char (name, ch);
      return;
      }
   /* search for the tag in cctags[] in order to find proper cbit index */
   for(i = 0; cctags[i].name != NULL; i++)
      {
      if(!strcmp (cctags[i].name, name))
         {
         tagNum = cctags[i].type;
         break;
         }
      }

   /* found any? */
   if(!tagNum)
      {
      send_to_char ("Doesn't look like a valid code feature name. Try again Bubba.\n", ch);
      return;
      }
   if(*onoff)
      {
      if(!isdigit (*onoff))
         {
         send_to_char ("The second parameter should be either 1 to enable or 0 to disable.\n", ch);
         return;
         }
      onOff = atoi (onoff);
      }
   else
      {
      onOff = -1;
      }

   switch(onOff)
      {
      case -1:
         sprintf (name, "&+LCode feature [%s - cbit %d] is %s\n", cctags[i].name, cctags[i].type,
                  IS_CSET (sets_code_control, cctags[i].type) ? "&+GON" : "&+ROFF");
         send_to_char (name, ch);
         break;

      case 0:
         REMOVE_CBIT (sets_code_control, tagNum);
         if(saveCodeControlBits () != TRUE)
            {
            sprintf (name, "&+R&-LPANIC!&N Failed to save the code control data!\n");
            send_to_char (name, ch);
            return;
            }
         sprintf (name, "&+LCode feature [%s - cbit %d] disabled successfully.\n", cctags[i].name, cctags[i].type);
         wizlog(51, name);
         send_to_char (name, ch);
         break;

      case 1:
         SET_CBIT (sets_code_control, tagNum);
         if(saveCodeControlBits () != TRUE)
            {
            sprintf (name, "&+R&-LPANIC!&N Failed to save the code control data!\n");
            send_to_char (name, ch);
            return;
            }
         sprintf (name, "&+LCode feature [%s - cbit %d] enabled successfully.\n", cctags[i].name, cctags[i].type);
         wizlog(51, name);
         send_to_char (name, ch);
         break;

      default:
         send_to_char ("&+LInvalid enable flag. Use 1 to enable and 0 to disable.\n", ch);
         break;
      }
}

/* All-in-one outcast command.  Needs to be updated when new goodrace homes */
/* are added, or if OC penalties change, obviously.             -- 1/99 CRM */
void do_outcast (P_char ch, char *args, int cmd)
{
   P_char victim;
   int old_room;
   char buf[MAX_INPUT_LENGTH];

   if(!ch || IS_NPC(ch) || !IS_TRUSTED(ch))
      return;

   if(!args)
      {
      send_to_char ("Outcast who? (Usage: outcast <victim>)\n", ch);
      return;
      }

   one_argument (args, buf);

   if(!(victim = get_char_in_game_vis(ch, buf, FALSE)))
      {
      send_to_char ("No-one by that name around.\n", ch);
      return;
      }

   if(victim->in_room == NOWHERE)
      {
      send_to_char ("Wait til they get into the game!\n", ch);
      return;
      }

   if(IS_NPC(victim))
      {
      send_to_char("You can't outcast NPC's, doofus.\n", ch);
      return;
      }

   if(IS_TRUSTED(victim))
      {
      send_to_char("Bored?  Outcasting of immortals is pointless.\n", ch);
      return;
      }

   if(RACE_EVIL(victim))
      {
      send_to_char("You can't outcast evil races!\n", ch);
      return;
      }

   /* Set law flags first */
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_WATERDEEP);
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_GRIFFONS);
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_ASHREMITE);
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_LEUTHILSPAR);
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_LUIREN);
   PC_SET_TOWN_JUSTICE_FLAGS(victim, JUSTICE_IS_OUTCAST, HOME_MITHRIL_HALL);

   /* Set OC flag */
   SET_CBIT(victim->only.pc->pcact, PLR_OUTCAST);

   /* Now set home/birth room - defaults to Viper main room */
   if(guild_locations[HOME_VIPERSTONGUE][GET_CLASS(victim)] != -1)
      {
      GET_BIRTHPLACE(victim) = guild_locations[HOME_VIPERSTONGUE][GET_CLASS(victim)];
      GET_HOME(victim) = GET_BIRTHPLACE(victim);
      }
   else
      GET_BIRTHPLACE(victim) = GET_HOME(victim) = 20953;

   /* You is evil!  Bad outcast!  BAD! */
   GET_ALIGNMENT(victim) = -1000;

   wizlog(51, "%s outcasts %s.", GET_NAME(ch), GET_NAME(victim));
   logit(LOG_WIZ, "%s outcasted by %s.", GET_NAME(victim), GET_NAME(ch));
   send_to_char("Outcasting successful.\n", ch);

   act ("$n disappears in a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);
   old_room = victim->in_room;
   char_from_room(victim);
   room_light(old_room, REAL);
   char_to_room (victim, real_room(GET_BIRTHPLACE(victim)), -1);
   act ("$n arrives from a puff of smoke.", FALSE, victim, 0, 0, TO_ROOM);

   send_to_char("\n&=LRYou have been outcasted!!!\n", victim);
   send_to_char("This is your new guildhall.  Welcome to Viperstongue Outpost...scum.\n", victim);
}

void do_sethelper(P_char ch, char *argument, int cmd)
{
   P_char vict;
   P_obj dummy;
   char buf[MAX_STRING_LENGTH];

   if(!ch || IS_NPC (ch))
      return;

   one_argument (argument, buf);

   if(!*buf)
      send_to_char ("Usage: sethelper <player>\n", ch);
   else if(!generic_find (argument, FIND_CHAR_WORLD, ch, &vict, &dummy))
      send_to_char ("Couldn't find any such creature.\n", ch);
   else if(IS_NPC (vict))
      send_to_char ("Can't do that to a mobile.\n", ch);
   else if(GET_LEVEL (vict) > GET_LEVEL (ch))
      act ("$E might object to that.. better not.", 0, ch, 0, vict, TO_CHAR);
   else if(IS_CSET (vict->only.pc->pcact, PLR_HELPER))
      {
      send_to_char ("&+CYour newbie helper statues has been revoked!&N\n", vict);
      send_to_char ("&+cHelper status revoked.&N\n", ch);
      REMOVE_CBIT (vict->only.pc->pcact, PLR_HELPER);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s just revoked %s's helper status.", GET_NAME (ch), GET_NAME (vict));
         logit (LOG_WIZ, "%s just revoked %s's helper status.", GET_NAME (ch), GET_NAME (vict));
         }
      }
   else
      {
      send_to_char ("&+cYou are now a &+CNewbie Helper&N&+c!&N\n", vict);
      send_to_char ("&+cHelper status set.\n", ch);
      SET_CBIT (vict->only.pc->pcact, PLR_HELPER);
      if(GET_LEVEL (ch) > 50)
         {
         wizlog (GET_LEVEL (ch), "%s made helper by %s.", GET_NAME (vict), GET_NAME (ch));
         logit (LOG_WIZ, "%s made helper by %s.", GET_NAME (vict), GET_NAME (ch));
         }
      }
}

const char *PROC_FORMAT =
"\n"
"&+LProc Format:\n\n&N"
"  add  charname functioname   - attaches a proc to a character\n"
"  rm   charname functioname   - removes a proc from a character\n"
"  list charname               - lists procs attached to a character\n"
"  list                        - lists all available procs\n"
"\n";


void do_proc(P_char ch, char *arg, int cmd)
{
   char buf[MAX_STRING_LENGTH];
   char Gbuf1[MAX_STRING_LENGTH];
   char Gbuf2[MAX_STRING_LENGTH];
   struct func_registration_data *frd = NULL;
   P_char vict;

   if(!ch)
      return;

   if(!*arg)
      {
      send_to_char(PROC_FORMAT, ch);
      return;
      }

   /* get the command */
   half_chop(arg, buf, Gbuf1);

   if(!*buf)
      {
      send_to_char(PROC_FORMAT, ch);
      return;
      }

   if(!strcmp(buf, "add"))
      {

      /* get the character name */
      half_chop(Gbuf1, buf, Gbuf2);
      if(!*buf)
         {
         send_to_char("Invalid or Missing character name.\n", ch);
         return;
         }
      if(!(vict = get_char_in_game_vis (ch, buf, FALSE)))
         {
         send_to_char ("No-one by that name around.\n", ch);
         return;
         }

      /* get the function name */
      half_chop(Gbuf2, buf, Gbuf1);
      if(!*buf)
         {
         send_to_char("You need to specify a function name.\n", ch);
         return;
         }

      frd = findProcFunction(buf);
      if(!frd)
         {
         send_to_char("Function not found.\n", ch);
         return;
         }

      AddProcPC(vict, frd->ptr, buf);
      send_to_char("Proc attached successfully.\n", ch);
      wizlog(GET_LEVEL(ch), "%s attached the %s proc to %s.", GET_NAME(ch), buf, GET_NAME(vict));
      logit(LOG_WIZ, "%s attached the %s proc to %s.", GET_NAME(ch), buf, GET_NAME(vict));
      return;
      }

   if(!strcmp(buf, "rm"))
      {

      /* get the character name */
      half_chop(Gbuf1, buf, Gbuf2);
      if(!*buf)
         {
         send_to_char("Invalid or Missing character name.\n", ch);
         return;
         }
      if(!(vict = get_char_in_game_vis (ch, buf, FALSE)))
         {
         send_to_char ("No-one by that name around.\n", ch);
         return;
         }

      /* get the function name */
      half_chop(Gbuf2, buf, Gbuf1);
      if(!*buf)
         {
         send_to_char("You need to specify a function name.\n", ch);
         return;
         }

      frd = findProcFunction(buf);
      if(!frd)
         {
         send_to_char("Function not found.\n", ch);
         return;
         }

      RemoveProcPC(vict, frd->ptr, buf);
      send_to_char("Proc detached successfully.\n", ch);
      wizlog(GET_LEVEL(ch), "%s detached the %s proc from %s.", GET_NAME(ch), buf, GET_NAME(vict));
      logit(LOG_WIZ, "%s detached the %s proc from %s.", GET_NAME(ch), buf, GET_NAME(vict));
      return;
      }

   if(!strcmp(buf, "list"))
      {

      /* see if they want to list all available procs*/
      if(!*Gbuf1)
         {
         listProcFunctions(Gbuf1);
         sprintf(Gbuf2, "\n&+LRegistered Proc Functions:\n\n%s\n", Gbuf1);
         send_to_char(Gbuf2, ch);
         return;
         }

      half_chop(Gbuf1, buf, Gbuf2);

      if(!(vict = get_char_in_game_vis (ch, buf, FALSE)))
         {
         send_to_char ("No-one by that name around.\n", ch);
         return;
         }

      ListProcs(vict, ch);
      return;
      }
}
/* Function to list the witness record of player and mob
   or the town crime record */
#ifdef NEWJUSTICE

   #define LWITNESS_SYNTAX "Syntax:\n   lwitness char|mob <name>\n   lwitness town <town # or name>\n   lwitness town <town # or name> <name> (remove records for <name>.)\n"

void do_list_witness(P_char ch, char *argument, int cmd)
{
   wtns_rec *rec = NULL;
   crm_rec *crec = NULL;
   char buf[MAX_STRING_LENGTH], arg1[MAX_STRING_LENGTH];
   char arg2[MAX_STRING_LENGTH], arg3[MAX_STRING_LENGTH];
   char arg4[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH];
   P_char target = NULL;
   int town = 0;
   unsigned l;

   half_chop(argument, arg1, arg2);
   half_chop(arg2, arg3, arg4);

   if(!*arg1)
      {
      send_to_char(LWITNESS_SYNTAX, ch);
      return;
      }
   if((*arg1 == 't') || (*arg1 == 'T'))
      {
      if(is_number(arg3))
         {
         town = atoi(arg3);
         if((town < 1) || (town > LAST_HOME))
            {
            send_to_char("Invalid town number!\n", ch);
            return;
            }
         }
      else
         {
         /* validate the city...  */
         l = strlen(arg3);
         for(town = 1; town <= LAST_HOME; town++)
            if(!strn_cmp(arg3, town_name_list[town], l))
               break;
         if(town > LAST_HOME)
            {
            send_to_char("Invalid town name!\n", ch);
            return;
            }
         }
      if(!*arg4)
         {
         if(town > 0)
            {
            if(!hometowns[town - 1].crime_list)
               {
               sprintf(buf, "Crime record for %s is empty.\n", town_name_list[town]);
               send_to_char(buf, ch);
               return;
               }
            sprintf(buf, "Crimes report for &+M%s&N:\n", town_name_list[town]);

            while((crec = crime_find(hometowns[town - 1].crime_list,
                                     NULL, NULL, 0, NOWHERE, J_STATUS_NONE, crec)))
               {

               switch(crec->status)
                  {
                  case J_STATUS_WANTED:
                     sprintf(buf, "%s&+R%s&N wanted for %s; reward %d platinums.\n",
                             buf, crec->attacker, crime_list[crec->crime], crec->money);
                     break;
                  case J_STATUS_DEBT:
                     sprintf(buf, "%s&+R%s&N owes %d platinum to the town.\n",
                             buf, crec->attacker, crec->money);
                     break;
                  case J_STATUS_PARDON:
                     sprintf(buf, "%s&+R%s&N pardoned %s for %s.\n",
                             buf, crec->victim, crec->attacker, crime_list[crec->crime]);
                     break;
                  case J_STATUS_CRIME:
                     sprintf(buf, "%s&+R%s&N committed %s against %s.\n",
                             buf, crec->attacker, crime_list[crec->crime], crec->victim);
                     break;
                  case J_STATUS_IN_JAIL:
                     sprintf(buf, "%s&+R%s&N is waiting in jail to be judged.\n",
                             buf, crec->attacker);
                     break;
                  case J_STATUS_JAIL_TIME:
                     sprintf(buf, "%s&+R%s&N is in jail for %d days.\n",
                             buf, crec->attacker, crec->money);
                     break;
                  case J_STATUS_DELETED:
                     break;
                  case J_STATUS_NONE:
                     sprintf(buf, "%s&+R%s&N committed %s against %s, status &+c%s&n "
                             "(%d)(%d).\n",
                             buf, crec->attacker, crime_list[crec->crime], crec->victim,
                             justice_status[crec->status], crec->money, (int) crec->time);
                     break;
                  }
               }
            page_string(ch->desc, buf, 1);

            return;
            }
         else
            {
            send_to_char("Invalid town!\n", ch);
            return;
            }
         }
      else if(arg4[0] == '@')
         {
         sprintf(buf, "Special crimes report for &+M%s&N:\n", town_name_list[town]);
         while((crec = crime_find(hometowns[town - 1].crime_list,
                                  NULL, NULL, 0, NOWHERE, J_STATUS_NONE, crec)))
            {
            sprintf(buf + strlen(buf), "&+R%s&N committed %s against %s, status &+c%s&n "
                    "(%d)(%d).\n",
                    crec->attacker, crime_list[crec->crime], crec->victim,
                    justice_status[crec->status], crec->money, (int) crec->time);
            }
         page_string(ch->desc, buf, 1);
         return;
         }
      /*    else if (!(target = get_char_in_game_vis(ch, arg4, FALSE)) || IS_NPC(target)) {
              send_to_char("No one by that name here...\n", ch);
              return;
          }  */
      else
         {
         while((crec = crime_find(hometowns[town - 1].crime_list,
                                  arg4, NULL, CRIME_NONE, NOWHERE, J_STATUS_NONE, NULL)))
            {
            crime_remove(town, crec);
            }
         /*      if (IS_PC(target)) {  */
         wizlog(GET_LEVEL(ch), "%s removed crime records for %s in %s",
                GET_NAME(ch), arg4, town_name_list[town]);
         logit(LOG_WIZ, "%s removed crime records for %s in %s", GET_NAME(ch),
               arg4, town_name_list[town]);
         /*      }  */
         sprintf(buf, "Records removed for &+M%s&N.\n", arg4);
         send_to_char(buf, ch);
         }
      }
   else if((*arg1 == 'c') || (*arg1 == 'C') || (*arg1 == 'm') || (*arg1 == 'M'))
      {
      if(!(target = get_char_vis(ch, arg3, TRUE)))
         {
         send_to_char("No-one by that name around.\n", ch);
         return;
         }
      sprintf(buf2, "Witness record of &+M%s&N:\n", C_NAME(target));

      if(!target->specials.witnessed)
         {
         sprintf(buf2, "%sWitness record is empty.\n", buf2);
         }
      else
         {
         while((rec = witness_find(target->specials.witnessed,
                                   NULL, NULL, 0, NOWHERE, rec)))
            {
            sprintf(buf2 + strlen(buf2), "&+R%s&N committed %s against %s at %s.\n",
                    rec->attacker, crime_list[rec->crime], rec->victim,
                    world[rec->room].name);
            }
         }
      if(IS_PC(target))
         {
         sprintf(buf2, "%sNumber of times judged : &+R%d&N.\n", buf2, GET_TIMES_JUDGED(target));

         sprintf(buf2, "%sJustice info for %s.\n", buf2, GET_NAME(target));

         for(town = 1; town <= LAST_HOME; town++)
            {
            if(!hometowns[town-1].crime_list)
               continue;
            if((crec = crime_find(hometowns[town-1].crime_list, GET_NAME(target),
                                  NULL, 0, NOWHERE, J_STATUS_NONE, crec)))
               {
               sprintf(buf2, "%s=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n", buf2);
               sprintf(buf2, "%sCrime(s) for %s\n", buf2, town_name_list[town]);
               }
            crec = NULL;
            while((crec = crime_find(hometowns[town-1].crime_list, GET_NAME(target),
                                     NULL, 0, NOWHERE, J_STATUS_NONE, crec)))
               {
               switch(crec->status)
                  {
                  case J_STATUS_WANTED:
                     sprintf(buf2, "%s&+R%s&N wanted for %s; reward %d platinums.\n",
                             buf2, crec->attacker, crime_list[crec->crime], crec->money);
                     break;
                  case J_STATUS_DEBT:
                     sprintf(buf2, "%s&+R%s&N owes %d platinum to the town.\n",
                             buf2, crec->attacker, crec->money);
                     break;
                  case J_STATUS_PARDON:
                     sprintf(buf2, "%s&+R%s&N pardoned %s for %s.\n",
                             buf2, crec->victim, crec->attacker, crime_list[crec->crime]);
                     break;
                  case J_STATUS_CRIME:
                     sprintf(buf2, "%s&+R%s&N committed %s against %s.\n",
                             buf2, crec->attacker, crime_list[crec->crime], crec->victim);
                     break;
                  case J_STATUS_IN_JAIL:
                     sprintf(buf2, "%s&+R%s&N is waiting in jail to be judged.\n",
                             buf2, crec->attacker);
                     break;
                  case J_STATUS_JAIL_TIME:
                     sprintf(buf2, "%s&+R%s&N is in jail for %d days.\n",
                             buf2, crec->attacker, crec->money);
                     break;
                  case J_STATUS_DELETED:
                     break;
                  case J_STATUS_NONE:
                     sprintf(buf2, "%s&+R%s&N committed %s against %s, status &+c%s&n "
                             "(%d)(%d).\n",
                             buf2, crec->attacker, crime_list[crec->crime], crec->victim,
                             justice_status[crec->status], crec->money, (int) crec->time);
                     break;
                  }
               }
            }
         sprintf(buf2, "%s=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n", buf2);
         }
      page_string(ch->desc, buf2, 1);
      return;
      }
   else
      {
      send_to_char(LWITNESS_SYNTAX, ch);
      return;
      }
}
#endif


/* Generic function hook up ..               -- Altherog Jun 1999          */

struct functionSearchBlockStruct
   {
   int   funcNumber;
   void  (*funcPtr) (P_char, char *);
   };

#define FUNC_PK_BALANCE         1
#define FUNC_EVENTLOAD          2
#define FUNC_EARTHQUAKE         3
#define FUNC_DEBUG_MODE         4
#define FUNC_DISEASE            5
#define FUNC_MMAIL              6
#define FUNC_PCLIST             7
#define FUNC_SQLDUMP            8
#define FUNC_BANDWIDTH          9
#define FUNC_PCCASH             10
#define FUNC_FOLLOWTREE         11
#define FUNC_BITBUCKET          12

const char *function_keywords[] =
{
   "pkbalance",
   "eventload",
   "earthquake",
   "systemdebug",
   "disease",
   "mmail",
   "pclist",
   "sqldump",
   "bandwidth",
   "showpccash",
   "followtree",
   "bitbucket",
   "\n"
};

void do_nothing(P_char ch, char *args)
{
}

const struct functionSearchBlockStruct definedFunctions[] =
{
   { FUNC_PK_BALANCE,     adjustPKBalance},
   { FUNC_EVENTLOAD,      showEventLoad},
   { FUNC_EARTHQUAKE,     startEarthquakeWrapper},
   { FUNC_DEBUG_MODE,     toggleSystemDebugMode},
   { FUNC_DISEASE,        manageDiseases},
   { FUNC_MMAIL,          mmailAdmin},
   { FUNC_PCLIST,         pclistdebug},
   { FUNC_SQLDUMP,        do_nothing},
   { FUNC_BANDWIDTH,      bw_showStats},
   { FUNC_PCCASH,         showPCcash},
   { FUNC_FOLLOWTREE,     showFollowTree},
   { FUNC_BITBUCKET,      bitbucket},
   { -1, NULL}
};


void do_function(P_char ch, char *argument, int command)
{
   int function_index;
   char cmd[MAX_INPUT_LENGTH], arg[MAX_INPUT_LENGTH];;

   half_chop(argument, cmd, arg);
   function_index = search_block(cmd, strlen(cmd), function_keywords, FALSE);

   if(function_index == -1)
      {
      function_index = 0;
      sprintf(cmd, "&+LAvailable Function Commands:\n&+B----------------------------\n");
      while(*function_keywords[function_index] != '\n')
         {
         sprintf(cmd + strlen(cmd), "   %s\n", function_keywords[function_index]);
         function_index++;
         }

      page_string(ch->desc, cmd, 1);
      return;
      }

   /* have it in a switch in case a command needs some additional preprocessing
    * before calling the function */
   switch(definedFunctions[function_index].funcNumber)
      {
      case FUNC_EVENTLOAD:
      case FUNC_PK_BALANCE:
      case FUNC_EARTHQUAKE:
      case FUNC_DEBUG_MODE:
      case FUNC_DISEASE:
      case FUNC_MMAIL:
      case FUNC_PCLIST:
      case FUNC_SQLDUMP:
      case FUNC_BANDWIDTH:
      case FUNC_PCCASH:
      case FUNC_FOLLOWTREE:
      case FUNC_BITBUCKET:
         (*definedFunctions[function_index].funcPtr) (ch, arg);
         break;

      default:
         function_index = 0;
         sprintf(cmd, "&+LAvailable Function Commands:\n&+B----------------------------\n");
         while(*function_keywords[function_index] != '\n')
            {
            sprintf(cmd + strlen(cmd), "   %s\n", function_keywords[function_index]);
            function_index++;
            }

         page_string(ch->desc, cmd, 1);
         break;
      };

   return;
}

// Shows all online player's cash
// no paging for now, can add later via an argument
// or just force it and use arg for something like sorting later
// -Azuth
void showPCcash(P_char ch, char *arg)
{
   int      ptotal = 0, gtotal = 0, stotal = 0, ctotal = 0;
   int      pgtotal = 0, ggtotal = 0, sgtotal = 0, cgtotal = 0;
   int      net = 0, gnet = 0;
   int      pc_count = 0;
   char     Gbuf1[MAX_STRING_LENGTH] = "";
   struct   moneyStruct *money = NULL;
   P_char   tch = NULL, lch = NULL;

   send_to_char("\n&+RPlayer Name      &n  &+WPlatinum&n  &+Y    Gold&n  &n  Silver&n  &+y  Copper&n  &+WNet Worth&n\n", ch);
   send_to_char("&+W=================  ========  ========  ========  ========  =========&n\n", ch);
   for(lch = PC_list; lch; lch = lch->next)
      {
      if(lch->desc && lch->desc->connected)
         continue;  /* not in game */

      tch = GET_PLYR(lch);

      if(IS_TRUSTED(tch)) // don't count god's cash
         continue;

      pc_count++;
      money = getCarriedMoneyStruct(tch);

      // Total up players bank, bag, and inv cash
      ptotal = (GET_BALANCE_PLATINUM(tch) + money->platinum + GET_PLATINUM(tch));
      gtotal = (GET_BALANCE_GOLD(tch) + money->gold + GET_GOLD(tch));
      stotal = (GET_BALANCE_SILVER(tch) + money->silver + GET_SILVER(tch));
      ctotal = (GET_BALANCE_COPPER(tch) + money->copper + GET_COPPER(tch));
      net = ptotal + gtotal/10 + stotal/100 + ctotal/1000;

      // Gather Grand Totals
      pgtotal += ptotal;
      ggtotal += gtotal;
      sgtotal += stotal;
      cgtotal += ctotal;
      gnet += net;

      sprintf(Gbuf1, "&+R%-17.17s&n  &+W%8d&n  &+Y%8d&n  &n%8d&n  &+y%8d&n  &+W%9d&n\n", GET_NAME(tch),
              ptotal, gtotal, stotal, ctotal, net);
      send_to_char(Gbuf1, ch);
      }

   send_to_char("&+W=================  ========  ========  ========  ========  =========&n\n", ch);
   sprintf(Gbuf1, "&+RGrand Totals     &n  &+W%8d&n  &+Y%8d&n  &n%8d&n  &+y%8d&n  &+W%9d&n\n",
           pgtotal, ggtotal, sgtotal, cgtotal, gnet);
   send_to_char(Gbuf1, ch);
   sprintf(Gbuf1, "&+RPer Player       &n  &+W%8.1lf&n  &+Y%8.1lf&n  &n%8.1lf&n  &+y%8.1lf&n  &+W%9.1lf&n\n",
           (double)pgtotal/(double)pc_count, (double)ggtotal/(double)pc_count, (double)sgtotal/(double)pc_count,
           (double)cgtotal/(double)pc_count, (double)gnet/(double)pc_count);
   send_to_char(Gbuf1, ch);
}

void pclistdebug(P_char ch, char *args)
{
   P_char   tch = NULL;
   int   count;
   char buf[MAX_STRING_LENGTH] = "", buf2[128] = "";
   char arg1[MAX_STRING_LENGTH] = "";
   //   char arg2[MAX_STRING_LENGTH] = "";

   args = one_argument(args, arg1);
   //   debuglog(51, DS_AZUTH, "pclistdebug: args[%s] arg1[%s] arg2[%s]", args, arg1, arg2);

   if(!str_cmp(arg1, "enable"))
      {
      pclist_debug = 1;
      send_to_char("PC_List debugging &+Genabled&n\n", ch);
      }
   else if(!str_cmp(arg1, "disable"))
      {
      pclist_debug = 0;
      send_to_char("PC_List debugging &+Rdisabled&n\n", ch);
      }
   else if(!str_cmp(arg1, "dump"))
      {
      count = 0;
      sprintf(buf, "Full PC_List Dump - PC_count = %3d\n", PC_count);
      sprintf(buf + strlen(buf), "-----------------------------------\n");
      for(tch = PC_list; tch; tch = tch->next)
         {
         if(tch->desc)
            sprinttype(tch->desc->connected, connected_types, buf2);
         else
            sprintf(buf2, "NO_DESCRIP");

         sprintf(buf + strlen(buf), "&+W%17.17s&n  &+c%11.11s&n &+Y%d&n\n", C_NAME(tch) ? C_NAME(tch) : "NULL", buf2, count);
         count++;
         if(count > 250)
            {
            sprintf(buf + strlen(buf), "count exceeded 250, aborting list\n");
            break;
            }

         if(strlen(buf) > (MAX_STRING_LENGTH - 1024))
            {
            sprintf(buf + strlen(buf), "string size approaching limit, aborting list\n");
            break;
            }
         }

      sprintf(buf + strlen(buf), "\n");
      send_to_char(buf, ch);
      }
   else
      {
      sprintf(buf, "pclist debugging options\n");
      sprintf(buf + strlen(buf), "------------------------\n");
      sprintf(buf + strlen(buf), "enable  - enables debugging mode. (debug channel 26)\n");
      sprintf(buf + strlen(buf), "disable - disables debugging mode.\n");
      sprintf(buf + strlen(buf), "dump    - dumps full list of names in PC_List.\n\n");
      send_to_char(buf, ch);
      }
}

void do_deaggro(P_char ch, char *argument, int cmd)
{
   char buf1[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH], buf3[MAX_STRING_LENGTH];
   P_char vic = NULL, mob = NULL;

   half_chop(argument, buf1, buf2);

   if(!*buf1 || !*buf2)
      {
      send_to_char("Usage: deaggro <player> <mobile>\n", ch);
      return;
      }

   if(((vic = get_char_vis(ch, buf1, TRUE))) && (!IS_NPC(vic)))
      {
      if(((mob = get_char_vis(ch, buf2, TRUE))) && (IS_NPC(mob)))
         {
         if(IS_CSET(mob->only.npc->npcact, ACT_MEMORY))
            mem_remFromMemory(mob->only.npc->memory, GET_NAME(vic));
         else
            {
            send_to_char("Mobile does not have the memory flag set!\n", ch);
            return;
            }
         }
      else
         {
         send_to_char("Sorry, Player Not Found!\n", ch);
         return;
         }
      }
   else
      {
      send_to_char("Sorry, Mobile Not Found!\n", ch);
      return;
      }

   sprintf(buf3, "%s has been removed from %s's aggressive list.\n", C_NAME(vic), C_NAME(mob));
   wizcmd (GET_LEVEL(ch), "%s made %s no longer aggressive to %s in [%d]",
           GET_NAME(ch), C_NAME(mob), C_NAME(vic), world[mob->in_room].number);
   logit (LOG_WIZ, "%s made %s no longer aggressive to %s in [%d]",
          GET_NAME(ch), C_NAME(mob), C_NAME(vic), world[mob->in_room].number);
   send_to_char(buf3, ch);
}

void do_reaggro(P_char ch, char *argument, int cmd)
{
   char buf1[MAX_STRING_LENGTH], buf2[MAX_STRING_LENGTH], buf3[MAX_STRING_LENGTH];
   P_char vic = NULL, mob = NULL;

   half_chop(argument, buf1, buf2);

   if(!*buf1 || !*buf2)
      {
      send_to_char("Usage: reaggro <player> <mobile>\n", ch);
      return;
      }

   if(((vic = get_char_vis(ch, buf1, TRUE))) && (!IS_NPC(vic)))
      {
      if(((mob = get_char_vis(ch, buf2, TRUE))) && (IS_NPC(mob)))
         {
         if(IS_CSET(mob->only.npc->npcact, ACT_MEMORY))
            mem_addToMemory(mob->only.npc->memory, GET_NAME(vic));
         else
            {
            send_to_char("Mobile does not have the memory flag set!\n", ch);
            return;
            }
         }
      else
         {
         send_to_char("Sorry, Player Not Found!\n", ch);
         return;
         }
      }
   else
      {
      send_to_char("Sorry, Mobile Not Found!\n", ch);
      return;
      }

   sprintf(buf3, "%s has been added to %s's aggressive list.\n", C_NAME(vic), C_NAME(mob));
   wizcmd (GET_LEVEL(ch), "%s made %s aggressive to %s in [%d]",
           GET_NAME(ch), C_NAME(mob), C_NAME(vic), world[mob->in_room].number);
   logit (LOG_WIZ, "%s made %s aggressive to %s in [%d]",
          GET_NAME(ch), C_NAME(mob), C_NAME(vic), world[mob->in_room].number);
   send_to_char(buf3, ch);
}

void do_createmob(P_char ch, char *argument, int cmd)
{
   P_char mob = NULL;
   int level = 0, classed = 0, sex = 0, race = 0, align = 0, i = 0;
   uint flags = 0, affects = 0, affects2 = 0;
   char arg1[MAX_INPUT_LENGTH], arg2[MAX_INPUT_LENGTH], arg3[MAX_INPUT_LENGTH];
   char arg4[MAX_INPUT_LENGTH], arg5[MAX_INPUT_LENGTH], arg6[MAX_INPUT_LENGTH];
   char arg7[MAX_INPUT_LENGTH], arg8[MAX_INPUT_LENGTH];

   // Sanity check
   if(!(ch))
      {
      logit(LOG_EXIT, "Invalid parameters in do_createmob(), dumping core...");
      dump_core();
      }

   if(!*argument)
      {
      send_to_char("Createmob syntax: createmob <level> <race> <class> <sex> <align> <flags> <affects> <affects2>\n", ch);
      return;
      }

   if(!IS_TRUSTED(GET_PLYR(ch)))
      {
      send_to_char("Duh, go away, peon.", ch);
      return;
      }

   // Chop up the arguments..yay

   half_chop(argument, arg1, arg8);
   half_chop(arg8, arg2, arg8);
   half_chop(arg8, arg3, arg8);
   half_chop(arg8, arg4, arg8);
   half_chop(arg8, arg5, arg8);
   half_chop(arg8, arg6, arg8);
   half_chop(arg8, arg7, arg8);


   // Checks the args..
   if(!*arg1 || !*arg2 || !*arg3 || !*arg4 || !*arg5 || !*arg6 || !*arg7 || !*arg8)
      {
      send_to_char("Createmob syntax: createmob <level> <race> <class> <sex> <align> <flags> <affects> <affects2>\n", ch);
      return;
      }

   // Setup our parameters..
   level = BOUNDED(1, (atoi(arg1)), 59);

   race = search_block(arg2, sizeof(arg2), race_types, FALSE);
   if((race < 1) || (race > LAST_RACE))
      {
      send_to_char("Sorry, couldn't find that race!\n", ch);
      return;
      }
   classed = search_block(arg3, sizeof(arg3), class_types, FALSE);
   if((classed < 0) || (classed > LAST_CLASS))
      {
      send_to_char("Invalid class specified.\n", ch);
      return;
      }

   if(LOWER(*arg4) == 'm')
      sex = 1;
   else if(LOWER(*arg4) == 'f')
      sex = 2;
   else
      sex = 0;

   if(LOWER(*arg5) == 'g')
      align = 1000;
   else if(LOWER(*arg5) == 'e')
      align = -1000;
   else
      align = 0;

   flags = MAX(0, (atoi(arg6)));
   affects = MAX(0, (atoi(arg7)));
   affects2 = MAX(0, (atoi(arg8)));


   if(!flags)
      flags = 2;


   // Got our parameters set up, now load the blank mob and apply them
   mob = read_mobile(910, VIRTUAL);

   GET_LEVEL(mob) = level;
   GET_RACE(mob) = race;
   GET_SEX(mob) = sex;
   GET_CLASS(mob) = classed;
   GET_ALIGNMENT(mob) = align;
   GET_BIRTHPLACE(mob) = world[ch->in_room].number;
   GET_HOME(mob) = GET_BIRTHPLACE(mob);

   for(i = 0; i < 32; i++)
      {
      if(IS_SET(flags, 1U << i))
         SET_CBIT(mob->only.npc->npcact, i + 1);
      }

   for(i = 0; i < 32; i++)
      {
      if(IS_SET(affects, 1U << i))
         SET_CBIT(mob->specials.affects, i + 1);
      }

   for(i = 0; i < 32; i++)
      {
      if(IS_SET(affects2, 1U << i))
         {
         SET_CBIT(mob->specials.affects, i + 33);
         }
      }


   // Convert it
   convert_mobile(mob, GET_LEVEL(mob));

   // Spam
   act ("&+L$n makes a strange &N&+carcane&+L gesture with one hand.", TRUE, ch, 0, 0, TO_ROOM);
   act ("&+L$n has created $N!", FALSE, ch, 0, mob, TO_ROOM);
   act ("&+LYou have created $N!", FALSE, ch, 0, mob, TO_CHAR);

   // Load it at ch, and we're done
   char_to_room(mob, ch->in_room, 0);

   wizcmd(GET_LEVEL(GET_PLYR(ch)), "%s created a %s %s (&+R%d&N) in [%d]", GET_NAME(GET_PLYR(ch)), arg2, arg3, level, world[ch->in_room].number);

}


const char *rptoggle_names[] = {
   "Neutral",
   "Bad",
   "Poor",
   "Good",
   "Excellent",
   "\n"
};

void do_rptoggle(P_char ch, char *argument, int cmd)
{
   P_char victim = NULL;
   int rplevel = 0;
   char arg1[MAX_INPUT_LENGTH], arg2[MAX_INPUT_LENGTH], buf[MAX_STRING_LENGTH];

   // Sanity Check
   if(!ch)
      {
      logit(LOG_EXIT, "Bogus paramaters in do_rptoggle(), dumping core..");
      dump_core();
      }

   if(!*argument)
      {
      send_to_char("RPToggle Syntax: rptoggle <player> <rp-level>\n", ch);
      return;
      }

   // Chop chop
   half_chop(argument, arg1, arg2);

   if(!*arg1 || !*arg2)
      {
      send_to_char("RPToggle Syntax: rptoggle <player> <rp-level>\n", ch);
      return;
      }

   // Get our victim..

   victim = get_char_vis(ch, arg1, TRUE);

   if(victim == NULL || IS_NPC(victim))
      {
      send_to_char("Player not found.\n", ch);
      return;
      }

   // Get our rplevel..
   rplevel = search_block(arg2, sizeof(arg2), rptoggle_names, FALSE);
   if(rplevel < 0)
      {
      send_to_char("Invalid RP Level specified.  Valid levels are: Bad, Poor, Neutral, Good, Excellent.\n", ch);
      return;
      }

   // Unset any currently set flags and reset em...yay, ugly.  but easy.
   REMOVE_CBIT(victim->only.pc->pcact, PLR_B_RP);
   REMOVE_CBIT(victim->only.pc->pcact, PLR_P_RP);
   REMOVE_CBIT(victim->only.pc->pcact, PLR_G_RP);
   REMOVE_CBIT(victim->only.pc->pcact, PLR_E_RP);

   switch(rplevel)
      {
      case 1:
         SET_CBIT(victim->only.pc->pcact, PLR_B_RP);
         break;
      case 2:
         SET_CBIT(victim->only.pc->pcact, PLR_P_RP);
         break;
      case 3:
         SET_CBIT(victim->only.pc->pcact, PLR_G_RP);
         break;
      case 4:
         SET_CBIT(victim->only.pc->pcact, PLR_E_RP);
         break;
      }

   sprintf(buf, "%s's RP Level changed to: '&+L%s&N'.\n", GET_NAME(victim), rptoggle_names[rplevel]);
   send_to_char(buf, ch);

   sprintf(buf, "%s changed %s's RP Level to: '&+L%s'\n", GET_NAME(ch), GET_NAME(victim), rptoggle_names[rplevel]);
   wizlog(GET_LEVEL(ch), buf);

   // Fin
   return;
}

void showFollowTree(P_char ch, char *arg)
{
   P_char   tch = NULL;
   char     Gbuf1[MAX_STRING_LENGTH] = "";
   char     arg1[MAX_STRING_LENGTH] = "";

   arg = one_argument(arg, arg1);
   if(!str_cmp(arg1, "pc"))
      {
      send_to_char("\n&+WPlayer Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = PC_list; tch; tch = tch->next)
         {
         if(IS_PC(tch) && tch->desc && tch->desc->connected)
            continue;  /* player not in game */

         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(!str_cmp(arg1, "npc"))
      {
      send_to_char("\n&+WNPC Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = NPC_list; tch; tch = tch->next)
         {
         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(!str_cmp(arg1, "pcroom"))
      {
      send_to_char("\n&+WPlayer Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = world[ch->in_room].people; tch; tch = tch->next_in_room)
         {
         if(IS_PC(tch) && tch->desc && tch->desc->connected)
            continue;  /* player not in game */

         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(!str_cmp(arg1, "npcroom"))
      {
      send_to_char("\n&+WNPC Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = world[ch->in_room].people; tch; tch = tch->next_in_room)
         {
         if(IS_PC(tch))
            continue;

         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(!str_cmp(arg1, "pczone"))
      {
      send_to_char("\n&+WPlayer Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = PC_list; tch; tch = tch->next)
         {
         if(IS_PC(tch) && tch->desc && tch->desc->connected)
            continue;  /* player not in game */

         if(world[tch->in_room].zone != world[ch->in_room].zone)
            continue;

         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(!str_cmp(arg1, "npczone"))
      {
      send_to_char("\n&+WNPC Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      for(tch = NPC_list; tch; tch = tch->next)
         {
         if(world[tch->in_room].zone != world[ch->in_room].zone)
            continue;

         if(!tch->followers)
            continue; // don't show tree if they have no followers, they're kinda lame

         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else if(*arg1)
      {
      send_to_char("\n&+WChar Follow Tree&n\n", ch);
      send_to_char("&+W==================&n\n", ch);
      tch = get_char_in_game_vis(ch, arg1, FALSE);
      if(!tch->followers)
         {
         sprintf(Gbuf1, "%s has no followers.\n", C_NAME(tch));
         send_to_char(Gbuf1, ch);
         }
      else
         {
         followtree(ch, tch, 0);
         send_to_char("\n", ch);
         }
      }
   else
      {
      sprintf(Gbuf1, "follower tree options\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "------------------------\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "pc      - all PCs that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "npc     - all NPCs that have 1 or more followers (you will lose conn.)\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "pcroom  - all PCs in your room that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "npcroom - all NPCs in your room that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "pczone  - all PCs in your zone that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "npczone - all NPCs in your zone that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "name    - a specific (N)PC in the world that have 1 or more followers\n");
      sprintf(Gbuf1 + strlen(Gbuf1), "\n");
      send_to_char(Gbuf1, ch);
      }
}

void followtree(P_char ch, P_char tch, int leadin)
{
   struct follow_type *j = NULL, *k = NULL;
   char  Gbuf1[MAX_STRING_LENGTH] = "";
   static char *spaces = "                                                                                ";

   if(leadin > 80)
      leadin = 80;

   sprintf(Gbuf1, "%*.*s%s - ", leadin, leadin, spaces, FirstWord(GET_NAME(tch)));
   for(k = tch->followers; k; k = j)
      {
      j = k->next;

      sprintf(Gbuf1 + strlen(Gbuf1), "%s - ", FirstWord(GET_NAME(k->follower)));
      }

   sprintf(Gbuf1 + strlen(Gbuf1), "\n");
   send_to_char(Gbuf1, ch);

   for(k = tch->followers; k; k = j)
      {
      j = k->next;
      followtree(ch, k->follower, leadin + strlen(FirstWord(GET_NAME(tch))) + 3);
      }
}

void bitbucket(P_char ch, char *arg)
{
   P_obj obj = NULL;
   int   vnum;
   char  Gbuf1[MAX_STRING_LENGTH] = "";
   char  *wh = NULL;

   for(obj = object_list; obj; obj = obj->next)
      {
      vnum = (obj->R_num >= 0) ? obj_index[obj->R_num].virtual : 0;
      wh = where_obj(obj, FALSE);

      if(!strcmp(wh, "&+RLost in the bit bucket!&N"))
         {
         sprintf(Gbuf1, "&+RLost item:&n obj vnum %d\n", vnum);
         send_to_char(Gbuf1, ch);
         }
      }
}

/* Simple object rating function based on affects */
static int rate_object(P_obj obj)
{
   int rating = 0;
   int i;
   
   /* Base rating from affects */
   for(i = 0; i < MAX_OBJ_AFFECT; i++) {
      if(obj->affected[i].location) {
         switch(obj->affected[i].location) {
            case APPLY_STR:
            case APPLY_DEX:
            case APPLY_INT:
            case APPLY_WIS:
            case APPLY_CON:
            case APPLY_AGI:
            case APPLY_POW:
            case APPLY_CHA:
               rating += obj->affected[i].modifier * 10;
               break;
            case APPLY_HIT:
               rating += obj->affected[i].modifier / 5;
               break;
            case APPLY_MANA:
               rating += obj->affected[i].modifier / 10;
               break;
            case APPLY_AC:
               rating -= obj->affected[i].modifier * 5; /* Lower AC is better */
               break;
            case APPLY_HITROLL:
               rating += obj->affected[i].modifier * 15;
               break;
            case APPLY_DAMROLL:
               rating += obj->affected[i].modifier * 20;
               break;
            case APPLY_SAVING_PARA:
            case APPLY_SAVING_ROD:
            case APPLY_SAVING_PETRI:
            case APPLY_SAVING_BREATH:
            case APPLY_SAVING_SPELL:
               rating -= obj->affected[i].modifier * 8; /* Lower saves are better */
               break;
         }
      }
   }
   
   /* Bonus for magic items */
   if(IS_SET(obj->extra_flags, ITEM_MAGIC))
      rating += 10;
      
   /* Bonus for blessed items */
   if(IS_SET(obj->extra_flags, ITEM_BLESS))
      rating += 5;
      
   return rating;
}

/* Command to list equipment by slot and rating */
void do_eqrate(P_char ch, char *argument, int cmd)
{
   struct obj_rating {
      int vnum;
      int rating;
      char name[MAX_STRING_LENGTH];
   } ratings[1000];
   
   int num_found = 0;
   int i, j, obj_num;
   uint wear_bit = 0;
   char arg[MAX_INPUT_LENGTH];
   char buf[MAX_STRING_LENGTH];
   struct obj_rating temp;
   
   if(!IS_TRUSTED(ch)) {
      send_to_char("This command is for staff only.\n", ch);
      return;
   }
   
   one_argument(argument, arg);
   
   if(!*arg) {
      send_to_char("Usage: eqrate <wear position>\n", ch);
      send_to_char("Valid positions: finger, neck, body, head, legs, feet, hands, arms,\n", ch);
      send_to_char("                shield, about, waist, wrist, wield, hold, eyes, face, earring\n", ch);
      return;
   }
   
   /* Find the wear position */
   if(!str_cmp(arg, "finger")) wear_bit = ITEM_WEAR_FINGER;
   else if(!str_cmp(arg, "neck")) wear_bit = ITEM_WEAR_NECK;
   else if(!str_cmp(arg, "body")) wear_bit = ITEM_WEAR_BODY;
   else if(!str_cmp(arg, "head")) wear_bit = ITEM_WEAR_HEAD;
   else if(!str_cmp(arg, "legs")) wear_bit = ITEM_WEAR_LEGS;
   else if(!str_cmp(arg, "feet")) wear_bit = ITEM_WEAR_FEET;
   else if(!str_cmp(arg, "hands")) wear_bit = ITEM_WEAR_HANDS;
   else if(!str_cmp(arg, "arms")) wear_bit = ITEM_WEAR_ARMS;
   else if(!str_cmp(arg, "shield")) wear_bit = ITEM_WEAR_SHIELD;
   else if(!str_cmp(arg, "about")) wear_bit = ITEM_WEAR_ABOUT;
   else if(!str_cmp(arg, "waist")) wear_bit = ITEM_WEAR_WAIST;
   else if(!str_cmp(arg, "wrist")) wear_bit = ITEM_WEAR_WRIST;
   else if(!str_cmp(arg, "wield")) wear_bit = ITEM_WIELD;
   else if(!str_cmp(arg, "hold")) wear_bit = ITEM_HOLD;
   else if(!str_cmp(arg, "eyes")) wear_bit = ITEM_WEAR_EYES;
   else if(!str_cmp(arg, "face")) wear_bit = ITEM_WEAR_FACE;
   else if(!str_cmp(arg, "earring")) wear_bit = ITEM_WEAR_EARRING;
   else {
      send_to_char("Invalid wear position.\n", ch);
      return;
   }
   
   /* Scan all object prototypes */
   for(obj_num = 0; obj_num <= top_of_objt && num_found < 1000; obj_num++) {
      /* Create a temporary object to check and rate */
      P_obj temp_obj = read_object(obj_num, REAL);
      if(temp_obj) {
         /* Check if object can be worn in specified position */
         if(IS_SET(temp_obj->wear_flags, wear_bit)) {
            int rating = rate_object(temp_obj);
            
            /* Store the rating */
            ratings[num_found].vnum = obj_index[obj_num].virtual;
            ratings[num_found].rating = rating;
            strncpy(ratings[num_found].name, temp_obj->short_description ? 
                    temp_obj->short_description : "undefined", MAX_STRING_LENGTH - 1);
            ratings[num_found].name[MAX_STRING_LENGTH - 1] = '\0';
            num_found++;
         }
         extract_obj(temp_obj);
      }
   }
   
   /* Sort by rating (bubble sort for simplicity) */
   for(i = 0; i < num_found - 1; i++) {
      for(j = 0; j < num_found - i - 1; j++) {
         if(ratings[j].rating < ratings[j + 1].rating) {
            temp = ratings[j];
            ratings[j] = ratings[j + 1];
            ratings[j + 1] = temp;
         }
      }
   }
   
   /* Display results */
   sprintf(buf, "Top equipment for %s slot (showing max 100):\n", arg);
   send_to_char(buf, ch);
   send_to_char("Vnum   Rating  Name\n", ch);
   send_to_char("-----  ------  ----------------------------------------------------\n", ch);
   
   for(i = 0; i < num_found && i < 100; i++) {
      sprintf(buf, "%5d  %6d  %s\n", ratings[i].vnum, ratings[i].rating, ratings[i].name);
      send_to_char(buf, ch);
   }
   
   sprintf(buf, "\nTotal items found: %d\n", num_found);
   send_to_char(buf, ch);
}

