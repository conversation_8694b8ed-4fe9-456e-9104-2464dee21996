# Makefile for Outcast's Diku

SHELL = /bin/sh

CC	= gcc 
# added -rdynamic for runtime stack trace ability
DEBUG	= -ggdb3 -rdynamic
PROFILE =
SRC_FLAGS = -D_LINUX_SOURCE 
#EXTRA_WARNING_FLAGS = -Wmissing-prototypes -Wmissing-declarations -Winline -Wnested-externs -Wmissing-braces  
#WARNING_FLAGS = -Wall $(EXTRA_WARNING_FLAGS)

# Linux
#LDFLAGS	  = $(DEBUG) $(PROFILE) -L../lib -L./lib -L/lib 
#LIBS      = -lmysqlclient

LDFLAGS	  = $(DEBUG) $(PROFILE) -L../lib -L./lib -L/lib
LIBS      = -lz -lpthread


OPTS    = -O2 $(DEBUG) $(PROFILE) 

CFLAGS = $(SRC_FLAGS) $(DEBUG) $(PROFILE) -Wall \
         -Wno-format-overflow \
         -Wno-pointer-to-int-cast \
         -Wno-unused-but-set-variable \
         -Wno-int-conversion \
         -Wno-format-security \
         -Wno-pointer-sign \
         -Wno-maybe-uninitialized \
         -Wno-address \
         -Wno-logical-not-parentheses \
         -Wno-bool-compare \
         -Wno-restrict \
         -Wno-int-in-bool-context \
         -Wno-deprecated-declarations \
         -Wno-unused-variable \
         -Wno-switch-bool \
         -Wno-format \
         -Wno-builtin-declaration-mismatch \
         -Wno-implicit-function-declaration \
         -Wno-parentheses \
         -Wno-incompatible-pointer-types \
         -Wno-int-to-pointer-cast \
         -Wno-sequence-point \
         -Wno-discarded-qualifiers \
         -Wno-misleading-indentation \
         -Wno-uninitialized \
         -Wno-use-after-free
#CFLAGS = $(SRC_FLAGS) $(DEBUG) $(PROFILE) $(WARNING_FLAGS)

# -Wid-clash-32 -Wpointer-arith -Winline \
#-Wbad-function-cast -Wcast-qual -Wcast-align -Wwrite-strings -Wconversion -Waggregate-return -Wshadow \
#-Wstrict-prototypes -Wmissing-prototypes -Wmissing-declarations -Wredundant-decls -Wnested-externs

OPT_OBJS	= graph.o mm.o

OBJS	= \
        actcomm.o \
        actgroup.o \
        actinf.o \
        actmove.o \
        actnoff.o \
        actobj.o \
        actoff.o \
        actset.o \
        actunused.o \
        actwiz.o \
        assocs.o \
        bard.o \
        bandwidth.o\
        board.o \
        build_areas.o \
        combat.o \
        control.o \
        comm.o \
        constant.o \
        contracts.o \
        db.o \
        debug.o \
        disease.handler.o \
        editor.o \
        elementalist.o \
        events.o \
        events.files.o \
        email_reg.o \
        files.o \
        follow.o \
        globals.o \
        group.o \
        guild.o \
        handler.o \
        hsearch_r.o \
        illusionist.o \
        interp.o \
        justice.o \
        kingdom.o \
        language.o \
        limits.o \
        lookup.o \
        magic.o \
        mccp.o \
        memorize.o \
        missile.o \
        mobact.o \
        mobgen.o \
        mobmagic.o \
        modify.o \
        mmail.o \
        mmail.files.o \
        morph.o \
        mount.o \
        namegen.o \
        nanny.o \
        necro.o \
	newauction.o \
        newbard.o \
        newjustice.o \
        newmagic.o \
        objtrap.o \
        olc.o \
        olc_room.o \
        olcdb.o \
        path.o \
        pkill.o \
        psionic.o \
        quest.o \
        race_class.o \
        random.o \
        ship.o \
        shop.o \
        shop_parallel.o \
        signals.o \
        socials.o \
        sparser.o \
        specials.o \
        specs.PC.o \
        specs.acheron.o \
        specs.artifacts.o \
        specs.assign.o \
        specs.associations.o \
        specs.astral.o \
        specs.avernus.o \
        specs.baldurs.o \
        specs.bloodstone.o \
        specs.bhaal.o \
        specs.caercorwell.o \
        specs.calimshan.o \
        specs.cavecity.o \
        specs.cemetary.o \
        specs.clouds.o \
	specs.cpalace.o \
        specs.darkhold.o \
        specs.diseases.o \
        specs.distro.o \
        specs.dobluth.o \
        specs.duskroad.o \
        specs.elemental.o \
        specs.erlan.o \
        specs.ethereal.o \
        specs.faang.o \
        specs.files.o \
        specs.fogwoods.o \
        specs.fun.o \
        specs.generic.o \
        specs.ghore.o \
        specs.gloomhaven.o \
        specs.greycloak.o \
        specs.griffons.o \
        specs.halruaa.o \
        specs.hive.o \
        specs.hulburg.o \
        specs.hyssk.o \
        specs.icecrag.o \
        specs.instruments.o \
        specs.jotunheim.o \
        specs.kobold.o \
        specs.lavatubes.o \
        specs.menden.o \
        specs.mobile.o \
        specs.monastery.o \
        specs.moonshae.o \
        specs.muspelheim.o \
        specs.mythdrannor.o \
        specs.neverwinter.o \
        specs.newhaven.o \
        specs.newzone.o \
        specs.nizari.o \
        specs.object.o \
	specs.ohp.o \
        specs.planar.o \
        specs.procregistration.o \
        specs.realm.o \
        specs.retired.o \
        specs.riverruins.o \
        specs.ruins_yath_oloth.o \
        specs.room.o \
        specs.seeliecourt.o\
        specs.scornubel.o \
        specs.spiderhaunt.o \
        specs.splitshield.o \
        specs.tarrasque.o \
        specs.tarsellian.o \
        specs.thunderheadpeak.o \
	specs.tiamat.o \
        specs.towerofsorc.o \
        specs.toys.o \
        specs.trahern.o \
        specs.undermountain.o \
        specs.underworld.o \
        specs.waterdeep.o \
        specs.weapons.o \
        specs.westernrealms.o \
        specs.wildlandtrails.o \
        specs.zhentilkeep.o \
        spells.o \
        stats.o \
        storage.o \
        system.o \
        traps.o \
        trade.o \
        utility.o \
        utilset.o \
        weather.o \
        whod.o
        
LOOKUPD_OBJS  =  lookupd.o


#******************************************************************************
#******************************************************************************
#
# Don't mess around below this point.  Everything past here are makefile rules,
# and NOT dependency lists that need to be changed

PROGS 	= ocm_new

DMS_OBJS = $(OBJS)

SRCS	= $(OBJS:.o=.c) $(OPT_OBJS:.o=.c)

all: $(PROGS)

ocm_new : $(DMS_OBJS) $(OPT_OBJS)
	$(CC) $(LDFLAGS) -o ocm_new $(DMS_OBJS) $(OPT_OBJS) $(LIBS)

lookupd : $(LOOKUPD_OBJS)
	$(CC) $(LDFLAGS) -o lookupd $(LOOKUPD_OBJS) $(LIBS)

purify : $(DMS_OBJS) $(OPT_OBJS)
	purify $(CC) $(LDFLAGS) -o ocm_new $(DMS_OBJS) $(OPT_OBJS) $(LIBS)


$(DMS_OBJS): %.o: %.c
	$(CC) $(CFLAGS) $(DEBUG) -c $< -o $@

$(OPT_OBJS): %.o: %.c
	$(CC) $(CFLAGS) $(OPTS) -c $< -o $@

depend .depend:
	$(CC) $(CFLAGS) -MM $(SRCS) > .depend

clean:
	rm -f *.o ocm_new .depend lookupd fscan ../ocm

install:
	make all
	mv ocm_new ../ocm

tgz:
	tar czhf ../src_`date +%m%d`_`date +%H%M`.tgz *.[ch] Makefile ChangeLog

include .depend

# DO NOT DELETE THIS LINE -- make depend depends on it.

