#ifndef _SOJ_SPECS_PROTOTYPES_H_
#define _SOJ_SPECS_PROTOTYPES_H_


/* newauction.c */
int auctioneer(P_char mob, P_char ch, int cmd, char *arg);

/* missile.c */

int ammo_repair_shopkeeper(P_char, P_char, int, char *);

/* shop.c */

int is_ok(P_char, P_char, int);
int shop_keeper(P_char, P_char, int, char *);
int shop_producing(P_obj, int);
int shop_reaction(P_char, P_char, int);
int shop_will_buy(int, P_obj);
void assign_the_shopkeepers(void);
/* Replaced with optimized version for better performance */
/* void boot_the_shops(void); */
void boot_the_shops_optimized(void);
void boot_the_shops_parallel(void);
void shopping_buy(char *, P_char, P_char, int, int qty, P_obj where_to);
void shopping_kill(char *, P_char, P_char, int);
void shopping_list(char *, P_char, P_char, int);
void shopping_sell(char *, P_char, P_char, int);
void shopping_value(char *, P_char, P_char, int);

/* specs.artifact.c */

int Doombringer(P_obj obj, P_char, int, char *);
int Kelrom(P_obj obj, P_char, int, char *);
int Kelrarin(P_obj obj, P_char, int, char *);
int Gesen(P_obj obj, P_char, int, char *);
int Fade2(P_obj obj, P_char, int, char *);
int Amaukekel(P_obj obj, P_char, int, char *);
int OakenDefender(P_obj obj, P_char, int, char *);
int NeverLooseItem(P_obj obj, P_char, int, char *);
int MinorHealback(P_obj obj, P_char, int, char *);
int HornOfHenekar(P_obj obj, P_char, int, char *);
int SwordOfKumaar(P_obj obj, P_char, int, char *);
int EyeOfZschisha(P_obj obj, P_char, int, char *);
int KomBloodAxe(P_obj obj, P_char, int, char *);

/* spec.assign.c */

void AddProcPC(P_char, int (*) (P_char, P_char, int, char *), const char *);
void RemoveProcPC(P_char, int (*) (P_char, P_char, int, char *), const char *);
void AddProcMob(int, int (*) (P_char, P_char, int, char *), const char *);
void AddProcObj(int, int (*) (P_obj, P_char, int, char *), const char *);
void AddProcRoom(int, int (*) (int, P_char, int, char *), const char *);
void assign_mobiles(void);
void assign_objects(void);
void assign_rooms(void);
void item_procs(void);
void room_procs(void);

/* specs.avernus.c */
void assignAvernusObjects(void);
void assignAvernusMobiles(void);
void assignAvernusRooms(void);
int avernus_man(P_char, P_char, int, char *);
int avernus_Rod(P_obj, P_char, int, char *);
int dragon_shout(P_char, P_char, int, char *);
int barbazu_glaive(P_obj, P_char, int, char *);
int dancing_dagger_obj(P_obj, P_char, int, char *);
int dancing_dagger_mob(P_char, P_char, int, char *);
int dancing_dagger_return_to_owner(P_char);
int barbazu_berserk(P_char, P_char, int, char *);
int erinyes_death(P_char, P_char, int, char *);
int avernus_black_altar(P_char, P_char, int, char *);
int gelugon_tail_freeze(P_char, P_char, int, char *);
int avernus_deva_echos(P_char, P_char, int, char *);
int avernus_prisoner_return(P_char, P_char, int, char *);
int bel(P_char, P_char, int, char *);
int avernus_gelugon_meritos(P_char, P_char, int, char *);
int avernus_gelugon_hanariel(P_char, P_char, int, char *);
int avernus_seal_unload(P_obj, P_char, int, char *);
int bel_flaming_sword(P_obj, P_char, int, char *);
int gelugon_freeze_spear(P_obj, P_char, int, char *);
int garden_room(int, P_char, int, char *);
int ring_patrol(P_char);
int ring_patrol_reverse(P_char);
int prison_patrol(P_char);
int citadel_patrol(P_char);
int citadel_patrol_reverse(P_char);

/* specials.c */

void underwaterSector(P_char);
void firesector(P_char);
void smokesector(P_char);
void magmasector(P_char);
P_obj find_key(P_char, int);
bool check_get_disarmed_obj(P_char, P_char, P_obj);
bool transact(P_char, P_obj, P_char, int);
int OutlawAggro(P_char, const char *);
//int pow10(int);
void CheckForTentacles(P_char);
void npc_steal(P_char, P_char);
int Dream_Scapes(P_char, char *);

/* specs.associations.c */

int keyless_unlock(int, P_char, int, char *);
int sigil_glyph(int, P_char, int, char *);
int sisterhood_glyph(int, P_char, int, char *);

/* specs.bloodstone.c */

int bs_acolyte(P_char, P_char, int, char *);
int bs_altar(int room, P_char ch, int cmd, char *arg);
int bs_antibrothel(P_char, P_char, int, char *);
int bs_antimaster(P_char, P_char, int, char *);
int bs_antishop(P_char, P_char, int, char *);
int bs_armor(P_char, P_char, int, char *);
int bs_assmaster(P_char, P_char, int, char *);
int bs_assshop(P_char, P_char, int, char *);
int bs_banker(P_char, P_char, int, char *);
int bs_baron(P_char, P_char, int, char *);
int bs_bear(P_char, P_char, int, char *);
int bs_boar(P_char, P_char, int, char *);
int bs_boss(P_char, P_char, int, char *);
int bs_bouncer(P_char, P_char, int, char *);
int bs_brat(P_char, P_char, int, char *);
int bs_cerrio(P_char, P_char, int, char *);
int bs_child_sacrifice(P_obj, P_char, int, char *);
int bs_citizen(P_char, P_char, int, char *);
int bs_clericpriest(P_char, P_char, int, char *);
int bs_comwoman(P_char, P_char, int, char *);
int bs_critter(P_char, P_char, int, char *);
int bs_crow(P_char, P_char, int, char *);
int bs_cub(P_char, P_char, int, char *);
int bs_darkpriest(P_char, P_char, int, char *);
int bs_dispersement_room(int, P_char, int, char *);
int bs_eatery(P_char, P_char, int, char *);
int bs_enochel(P_char, P_char, int, char *);
int bs_ettin(P_char, P_char, int, char *);
int bs_executioner(P_char, P_char, int, char *);
int bs_fierce(P_char, P_char, int, char *);
int bs_flayer(P_char, P_char, int, char *);
int bs_fslave(P_char, P_char, int, char *);
int bs_ghelian(P_char, P_char, int, char *);
int bs_gnoll(P_char, P_char, int, char *);
int bs_griffon(P_char, P_char, int, char *);
int bs_guard(P_char, P_char, int, char *);
int bs_guildguard_antiwar(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_assassin(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_clersham(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_monk(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_necro(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_sorcconj(P_char ch, P_char pl, int cmd, char *arg);
int bs_guildguard_thief(P_char ch, P_char pl, int cmd, char *arg);
int bs_holyman(P_char, P_char, int, char *);
int bs_homeless(P_char, P_char, int, char *);
int bs_inn(P_char, P_char, int, char *);
int bs_insane(P_char, P_char, int, char *);
int orcus_master(P_char, P_char, int, char *);
int orcus_toss(P_char, P_char);
int orcus_impale(P_char, P_char);
int orcus_tail_sting(P_char);
int orcus_summon_undead(P_char, P_char);
int orcus_berserk(P_char, P_char, int, char *);
int orcus_hitall(P_char);
int orcus_mace(P_obj, P_char, int, char *);
int orcus_pwk_wand(P_obj, P_char, int, char *);
int shadow_stun(P_char, P_char, int, char *);
int bs_magemaster(P_char, P_char, int, char *);
int bs_mageshop(P_char, P_char, int, char *);
int bs_magic(P_char, P_char, int, char *);
int bs_menacing(P_char, P_char, int, char *);
int bs_merchant(P_char, P_char, int, char *);
int bs_mountainman(P_char, P_char, int, char *);
int bs_mslave(P_char, P_char, int, char *);
int bs_nakedman(P_char, P_char, int, char *);
int bs_nakedwoman(P_char, P_char, int, char *);
int bs_necromaster(P_char, P_char, int, char *);
int bs_necroshop(P_char, P_char, int, char *);
int bs_nomad(P_char, P_char, int, char *);
int bs_payton(P_char, P_char, int, char *);
int bs_peddler(P_char, P_char, int, char *);
int bs_pet(P_char, P_char, int, char *);
int bs_portal(P_obj, P_char, int, char *);
int bs_prostitute(P_char, P_char, int, char *);
int bs_robber(P_char, P_char, int, char *);
int bs_salesman(P_char, P_char, int, char *);
int bs_servant(P_char, P_char, int, char *);
int bs_shady(P_char, P_char, int, char *);
int bs_sickguard(P_char, P_char, int, char *);
int bs_sinister(P_char, P_char, int, char *);
int bs_soultaker(P_char, P_char, int, char *);
int bs_sparrow(P_char, P_char, int, char *);
int bs_squire(P_char, P_char, int, char *);
int bs_squirrel(P_char, P_char, int, char *);
int bs_stable(P_char, P_char, int, char *);
int bs_stirge(P_char, P_char, int, char *);
int bs_tax(P_char, P_char, int, char *);
int bs_thiefmaster(P_char, P_char, int, char *);
int bs_thiefshop(P_char, P_char, int, char *);
int bs_timid(P_char, P_char, int, char *);
int bs_trenton(P_char, P_char, int, char *);
int bs_undead_die(P_char, P_char, int, char *);
int bs_undead_with_die(P_char, P_char, int, char *);
int bs_undead_without_die(P_char, P_char, int, char *);
int bs_veriallo(P_char, P_char, int, char *);
int bs_watcher(P_char, P_char, int, char *);
int bs_weapon(P_char, P_char, int, char *);
int bs_whirlpool(int room, P_char ch, int cmd, char *arg);
int bs_wino(P_char, P_char, int, char *);
int bs_wolf(P_char, P_char, int, char *);
int bs_youngm(P_char, P_char, int, char *);
int bs_youngw(P_char, P_char, int, char *);
int bs_zzzcitizen(P_char, P_char, int, char *);
int bs_zzzguard(P_char, P_char, int, char *);
void bs_gate(void);

/* specs.calimshan.c */

int calimshan_pasha_teleport(P_char, P_char, int, char *);
int calimshan_pashasGuard(P_char, P_char, int, char *);
int calimshan_hydra(int, P_char, int, char *);
void assignCalimshanMobiles(void);
void assignCalimshanObjects(void);
void assignCalimshanRooms(void);

/* specs.cemetary.c */

int cemetary_disruption(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_skeletal_hand(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_flute_mandolin(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_black_blade(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_gleaming_blade(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_instrument_rub(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.balders.c */

void assignBaldersGateObjects(void);
void assignBaldersGateMobiles(void);
void assignBaldersGateRooms(void);

/* specs.dobluth.c */
void assignDobluthKyorObjects(void);
void assignDobluthKyorMobiles(void);
void assignDobluthKyorRooms(void);
int dk_lich_energy_drain(P_char, P_char, int, char *);
int dk_aleanrahel(P_char, P_char, int, char *);
int dk_bansheeWail(P_char, P_char, int, char *);
int dk_bladestorm(P_char, P_char, int, char *);
int broadsword_dancing_shadows(P_obj obj, P_char, int, char *);

/* specs.diseases.c */

void blackPlaguePathogen(P_char, char *, int);
void blackPlagueEffect(P_char, char *, int);
void blackPlagueReSchedulePathogen(P_char, char *, int);
void blackPlagueReScheduleEffect(P_char, char *, int);
void blackPlagueSchedulePathogen(P_char);
void blackPlagueScheduleEffect(P_char);
int blackPlagueIsInfected(P_char);
int blackPlagueInfect(P_char);
int blackPlagueOnInfect(P_char ch, P_char vict, int cmd, char *arg);
int blackPlagueReservoir(P_obj, P_char, int, char *);
int blackPlagueStat(P_char, void *);
int blackPlagueCure(P_obj, P_char, int, char *);

void tiberianFluPathogen(P_char, char *, int);
void tiberianFluEffect(P_char, char *, int);
void tiberianFluReSchedulePathogen(P_char, char *, int);
void tiberianFluReScheduleEffect(P_char, char *, int);
void tiberianFluSchedulePathogen(P_char);
void tiberianFluScheduleEffect(P_char);
int tiberianFluIsInfected(P_char);
int tiberianFluInfect(P_char);
int tiberianFluOnInfect(P_char ch, P_char vict, int cmd, char *arg);
int tiberianFluReservoir(P_obj, P_char, int, char *);
int tiberianFluStat(P_char, void *);
int tiberianFluCure(P_obj, P_char, int, char *);


/* specs.duskroad.c */

void assignDuskRoadMobiles(void);
int dr_minorParalyze(P_char, P_char, int, char*);
int dr_majorParalyze(P_char, P_char, int, char*);

/* specs.generic.c */

P_char getGroupLeader(P_char);
P_char getObjectOwner(P_obj);
int advancedProcTest(P_obj, P_char, int, char *);
int castOnFriend(P_char, P_char, int);
int checkAlignmentRestriction(P_obj obj, P_char ch, uint);
int checkClassRestriction(P_obj, P_char, uint);
int checkNPCRaceRestriction(P_obj, P_char, uint);
int checkObjectRestrictions(int, P_obj, P_char, int, uint);
int checkObjectValueBit(int, P_obj, int);
int checkPCRaceRestriction(P_obj, P_char, uint);
int checkSexRestriction(P_obj, P_char, uint);
int clericGroupMember(P_char, P_char, int, char *);
int genericDrowEq(P_obj, P_char, int, char *);
int getObjectRoom(P_obj);
int getObjectValue(int, P_obj);
int godMaintenanceCommands(P_obj, P_char, int, char *);
int groupMemberSanityCheck(P_char, int);
int magicUserGroupMember(P_char, P_char, int, char *);
int mobileSpecialDisplay(P_char, P_char, int, char *);
int objectUptime(P_obj);
int performCastOnFriend(P_char, P_char, int);
int performRestrictionPenalty(int, P_obj, P_char, int);
int rechargeObjectFeature(int, P_obj, P_char, int, char *, int, char *);
int reduceObjectValue(P_obj, int);
int removeObjectValueBit(int, P_obj, int);
int setObjectValue(int, P_obj, int);
int setObjectValueBit(int, P_obj, int);
int shout_and_hunt(P_char, int, const char *, int (*)(P_char, P_char, int, char *), int *, uint, uint);
int specialProcedureIdentifyObject (P_obj, P_char, int, char *, int, int, int, char *);
int verifyObjEventExists(int, P_obj, char *);
int warriorGroupMember(P_char, P_char, int, char *);
void call_solve_sanctuary(P_char, P_char);
void do_mobdisarm(P_char, char *, int);
void initializeObjectFeature(int, P_obj, int, int, char *);
void makeObjectWorthless(P_obj);
void usedFeatureMessage(P_obj, P_char, int, const char *);
int home_reset(int, P_char, int, char *);
int simpleProcedureIdentifyObject (P_obj, P_char, int, char *, char *);


/* specs.griffons.c */

void assignGriffonsNestMobiles(void);
void assignGriffonsNestRooms(void);
int aggroNonBarbarian(P_char, P_char, int, char*);
int pahlurukroot(P_obj obj, P_char ch, int cmd, char *arg);
int gn_dragoncultrobes (P_obj obj, P_char ch, int cmd, char *arg);
void pahlurukroot_event(P_char ch);

/* specs.hive.c */
void assignHiveMobiles(void);
int manscorpion_venom_light(P_char, P_char, int, char *);
int manscorpion_venom_medium(P_char, P_char, int, char *);
int manscorpion_venom_heavy(P_char, P_char, int, char *);
int manscorpion_king(P_char, P_char, int, char *);
int ms_sandstorm_beast(P_char, P_char, int, char *);
int hive_gythka(P_obj obj, P_char ch, int cmd, char *arg);
void perform_skriaxit_sandstorm(P_char, int, int);
int skriaxit_sandstorm(P_char, P_char, int, char *);

/* specs.hyssk.c */

void assignHysskMobiles(void);
void assignHysskObjects(void);
void assignHysskRooms(void);
int hyssk_slave_taker(P_char ch, P_char pl, int cmd, char *arg);
int hyssk_skeleton_teleport(P_obj, P_char, int, char *);

/* specs.icecrag.c */

int ic2_foreteller (P_obj, P_char, int, char *);
int ic2_icePriest (P_char, P_char, int, char *);
int ic2_icetooth (P_obj, P_char, int, char *);
int ic2_silverCirclet (P_obj, P_char, int, char *);
int ic2_strife (P_char, P_char, int, char *);
int ice_artist(P_char, P_char, int, char *);
int ice_bodyguards(P_char, P_char, int, char *);
int ice_cleaning_crew(P_char, P_char, int, char *);
int ice_commander(P_char, P_char, int, char *);
int ice_garden_attendant(P_char, P_char, int, char *);
int ice_impatient_guest(P_char, P_char, int, char *);
int ice_malice(P_char, P_char, int, char *);
int ice_masha(P_char, P_char, int, char *);
int ice_masonary_crew(P_char, P_char, int, char *);
int ice_priest(P_char, P_char, int, char *);
int ice_privates(P_char, P_char, int, char *);
int ice_raucous_guest(P_char, P_char, int, char *);
int ice_snooty_wife(P_char, P_char, int, char *);
int ice_tar(P_char, P_char, int, char *);
int ice_tubby_merchant(P_char, P_char, int, char *);
int ice_viscount(P_char, P_char, int, char *);
int ice_wolf(P_char, P_char, int, char *);
void assignIcecrag2Mobiles (void);
void assignIcecrag2Objects (void);

/* specs.jotunheim.c */

int jotun_adamantiumMaceMistweave(P_obj, P_char, int, char *);
int jotun_balor(P_char, P_char, int, char *);
int jotun_clawsDestroyer(P_obj, P_char, int, char *);
int jotun_cloakIcicles(P_obj, P_char, int, char *);
int jotun_doubleAxeGiantbane(P_obj, P_char, int, char *);
int jotun_flamingSwordValkyrie(P_obj, P_char, int, char *);
int jotun_greatAxeFrostbite(P_obj, P_char, int, char *);
int jotun_twilight(P_obj, P_char, int, char *);
int jotun_skullSmasher(P_obj, P_char, int, char *);
int jotun_holySwordFaith(P_obj, P_char, int, char *);
int jotun_mimer(P_char, P_char, int, char *);
int jotun_thrym(P_char, P_char, int, char *);
int jotun_utgard_loki(P_char, P_char, int, char *);
void assignJotunheimObjects(void);

/* specs.greycloak.c */

void assignGreycloakMobs(void);
void assignGreycloakObjects(void);
int gc_swiftWind(P_obj, P_char, int, char *);
int gc_chaliceOfElvenGods(P_obj, P_char, int, char *);
int gc_pestilenceSword(P_obj, P_char, int, char *);
int gc_necromancerStone(P_obj, P_char, int, char *);
int gc_demonHideSpellbook(P_obj, P_char, int, char *);
int gc_deathKnell(P_obj, P_char, int, char *);

int gc_urguthaForka(P_char, P_char, int, char *);
int gc_araleshTandar(P_char, P_char, int, char *);
int gc_bansheWail(P_char, P_char, int, char *);



/* specs.mobile.c */

#ifdef CONFIG_JAIL
int apprehender(P_char, P_char, int, char *);
int jailkeeper(P_char, P_char, int, char *);
int judge(P_char, P_char, int, char *);
int witness(P_char, P_char, int, char *);
#endif
int spider_venom_medium(P_char, P_char, int, char *);
int werewolf_lycan(P_char, P_char, int, char *);
int lycan_wolf_infect(P_char, P_char, int, char *);
int Invader_Repulser(P_char, P_char, int, char *);
int Evil_Invader(P_char, P_char, int, char *);
int agthrodos(P_char, P_char, int, char *);
int air_mental_die(P_char, P_char, int, char *);
int albert(P_char, P_char, int, char *);
int automaton_unblock(P_char, P_char, int, char *);
int barbarian_spiritist(P_char, P_char, int, char *);
int barmaid(P_char, P_char, int, char *);
int blob(P_char, P_char, int, char *);
int boulder_pusher(P_char, P_char, int, char *);
int brass_dragon(P_char, P_char, int, char *);
int breath_attack_acid(P_char, P_char, int, char *);
int breath_attack_cold(P_char, P_char, int, char *);
int breath_attack_fire(P_char, P_char, int, char *);
int breath_attack_gas(P_char, P_char, int, char *);
int breath_attack_lightning(P_char, P_char, int, char *);
int breath_weapon_acid(P_char, P_char, int, char *);
int breath_weapon_cold(P_char, P_char, int, char *);
int breath_weapon_fire(P_char, P_char, int, char *);
int breath_weapon_gas(P_char, P_char, int, char *);
int breath_weapon_lightning(P_char, P_char, int, char *);
int breath_weapon_darkness(P_char, P_char, int, char *);
int breath_attack_faerie_reduce(P_char ch, P_char pl, int cmd, char *arg);
int bridge_troll(P_char, P_char, int, char *);
int cc_female_ffolk(P_char, P_char, int, char *);
int cc_fisherffolk(P_char, P_char, int, char *);
int cc_warehouse_foreman(P_char, P_char, int, char *);
int cc_warehouse_man(P_char, P_char, int, char *);
int chicken(P_char, P_char, int, char *);
int christine(P_char, P_char, int, char *);
int citizenship(P_char, P_char, int, char *);
int cityguard(P_char, P_char, int, char *);
int clyde(P_char, P_char, int, char *);
int confess_figure(P_char, P_char, int, char *);
int cookie(P_char, P_char, int, char *);
int crystal_golem_die(P_char, P_char, int, char *);
int da_shipguard(P_char, P_char, int, char *);
int demogorgon_shout(P_char, P_char, int, char *);
int devour(P_char, P_char, int, char *);
int dryad(P_char, P_char, int, char *);
int earth_mental_die(P_char, P_char, int, char *);
int elemental_tower_shout(P_char, P_char, int, char *);
int farmer(P_char, P_char, int, char *);
int fire_mental_die(P_char, P_char, int, char *);
int follow_that_mob(P_char, P_char, int, char *);
int gate_guard(P_char, P_char, int, char *);
int ghore_paradise(P_char, P_char, int, char *);
int guardian(P_char, P_char, int, char *);
int guardian_golem_one(P_char, P_char, int, char *);
int guild_guard(P_char, P_char, int, char *);
int guru_anapest(P_char, P_char, int, char *);
int hippogriff_die(P_char, P_char, int, char *);
int hunt_cat(P_char, P_char, int, char *);
int imix_shout(P_char, P_char, int, char *);
int imix_pet_demon_shout(P_char ch, P_char tch, int cmd, char *arg);
int av_drisinil_shout(P_char ch, P_char tch, int cmd, char *arg);
int av_tukra_shout(P_char ch, P_char tch, int cmd, char *arg);
int av_vanish(P_char ch, P_char tch, int cmd, char *arg);
void av_vanish_event(P_char);
int yancbin_shout(P_char, P_char, int, char *);
int janitor(P_char, P_char, int, char *);
int jester(P_char, P_char, int, char *);
int kobold_priest(P_char, P_char, int, char *);
int lich_energy_drain(P_char, P_char, int, char *);
int mage_anapest(P_char, P_char, int, char *);
int menden_figurine_die(P_char, P_char, int, char *);
int menden_fisherman(P_char, P_char, int, char *);
int menden_inv_serv_die(P_char, P_char, int, char *);
int menden_magus(P_char, P_char, int, char *);
int money_changer(P_char, P_char, int, char *);
int tithe_collector(P_char, P_char, int, char *);
int major_beholder(P_char, P_char, int, char *);
int mystra_dragon(P_char, P_char, int, char *);
int navagator(P_char, P_char, int, char *);
int neophyte(P_char, P_char, int, char *);
int nw_agatha(P_char, P_char, int, char *);
int nw_ammaster(P_char, P_char, int, char *);
int nw_ansal(P_char, P_char, int, char *);
int nw_brock(P_char, P_char, int, char *);
int nw_builder(P_char, P_char, int, char *);
int nw_carpen(P_char, P_char, int, char *);
int nw_chicken(P_char, P_char, int, char *);
int nw_chief(P_char, P_char, int, char *);
int nw_cow(P_char, P_char, int, char *);
int nw_cutter(P_char, P_char, int, char *);
int nw_diamaster(P_char, P_char, int, char *);
int nw_elfhealer(P_char, P_char, int, char *);
int nw_emmaster(P_char, P_char, int, char *);
int nw_farmer(P_char, P_char, int, char *);
int nw_foreman(P_char, P_char, int, char *);
int nw_golem(P_char, P_char, int, char *);
int nw_hafbreed(P_char, P_char, int, char *);
int nw_human(P_char, P_char, int, char *);
int nw_logger(P_char, P_char, int, char *);
int nw_malchor(P_char, P_char, int, char *);
int nw_merthol(P_char, P_char, int, char *);
int nw_mirroid(P_char, P_char, int, char *);
int nw_owl(P_char, P_char, int, char *);
int nw_pig(P_char, P_char, int, char *);
int nw_rubmaster(P_char, P_char, int, char *);
int nw_sapmaster(P_char, P_char, int, char *);
int nw_vitnor(P_char, P_char, int, char *);
int nw_woodelf(P_char, P_char, int, char *);
int phalanx(P_char, P_char, int, char *);
int plant_attacks_blindness(P_char, P_char, int, char *);
int plant_attacks_paralysis(P_char, P_char, int, char *);
int plant_attacks_poison(P_char, P_char, int, char *);
int poison(P_char, P_char, int, char *);
int puff(P_char, P_char, int, char *);
int raoul(P_char, P_char, int, char *);
int realms_master_shout(P_char, P_char, int, char *);
int receptionist(P_char, P_char, int, char *);
int rehide(P_char, P_char, int, char *);
int sales_spec(P_char, P_char, int, char *);
int seas_coral_golem(P_char, P_char, int, char *);
int shadow_demon(P_char, P_char, int, char *);
int shadow_demon_of_torm(P_char, P_char, int, char *);
int shady_man(P_char, P_char, int, char *);
int silver_lady_shout(P_char, P_char, int, char*);
int skeleton(P_char, P_char, int, char *);
int snowbeast(P_char, P_char, int, char *);
int snowvulture(P_char, P_char, int, char *);
int spiny(P_char, P_char, int, char *);
int spirit_wolf_die(P_char, P_char, int, char *);
int spirit_bear_die(P_char, P_char, int, char *);
int spirit_boar_die(P_char, P_char, int, char *);
int spirit_elk_die(P_char, P_char, int, char *);
int spirit_eagle_die(P_char, P_char, int, char *);
int spirit_crow_die(P_char, P_char, int, char *);
int spirit_lion_die(P_char, P_char, int, char *);
int spirit_tiger_die(P_char, P_char, int, char *);
int spirit_stallion_die(P_char, P_char, int, char *);
int spirit_snake_die(P_char, P_char, int, char *);
int spirit_worg_die(P_char, P_char, int, char *);
int spirit_vulture_die(P_char, P_char, int, char *);
int spirit_crocodile_die(P_char, P_char, int, char *);
int spirit_serpent_die(P_char, P_char, int, char *);
int spirit_scorpion_die(P_char, P_char, int, char *);
int spirit_hyena_die(P_char, P_char, int, char *);
int spirit_jackal_die(P_char, P_char, int, char *);
int spirit_spider_die(P_char, P_char, int, char *);
int spirit_viper_die(P_char, P_char, int, char *);
int spirit_bat_die(P_char, P_char, int, char *);
int spirit_raven_die(P_char, P_char, int, char *);
int spore_ball(P_char, P_char, int, char *);
int stone_crumble(P_char, P_char, int, char *);
int stone_golem(P_char, P_char, int, char *);
int succubus(P_char, P_char, int, char *);
int swallow_whole(P_char, P_char, int, char *);
int swallow_whole_spit(P_char, P_char, int, char *);
int tako_demon(P_char, P_char, int, char *);
int taxman(P_char, P_char, int, char *);
int tentacle(P_char, P_char, int, char *);
int thief(P_char, P_char, int, char *);
int tiaka_ghoul(P_char, P_char, int, char *);
int ticket_taker(P_char, P_char, int, char *);
int unseen_servant_die(P_char, P_char, int, char *);
int tentacle_die(P_char, P_char, int, char *);
int waiter(P_char, P_char, int, char *);
int warhorse(P_char ch, P_char pl, int cmd, char *arg);
int water_elemental(P_char ch, P_char pl, int cmd, char *arg);
int water_mental_die(P_char, P_char, int, char *);
int xexos(P_char, P_char, int, char *);
int yochlol(P_char, P_char, int, char *);
void InstantSpellup(P_char);
void MinorInstantSpellup(P_char);
void nw_block_exit(int, int, int);
void nw_reset_maze(int);
int bulette(P_char, P_char, int, char *);
int dranum_jurtrem(P_char, P_char, int, char *);
int dranum_lifesuck(P_char ch, P_char pl, int cmd, char *arg);
int piercer(P_char, P_char, int, char *);
int purple_worm(P_char, P_char, int, char *);
int Tiamat(P_char, P_char, int, char *);
int Tiamat_Crimson_Fury(P_char, P_char, int, char *);
int ancient_man(P_char, P_char, int, char *);
int petRent(P_char, P_char, int, char *);
#ifdef NEWJUSTICE
int justice_clerk(P_char, P_char, int, char *);
#endif
int conj_lycan_fox(P_char, P_char, int, char *);
int conj_lycan_wolf(P_char, P_char, int, char *);
int conj_lycan_tiger(P_char, P_char, int, char *);
int conj_lycan_bear(P_char, P_char, int, char *);
int conj_familiar_die(P_char, P_char, int, char *);
int conj_mount_die(P_char, P_char, int, char *);
int conj_monster_die(P_char, P_char, int, char *);
int goortok(P_char, P_char, int, char *);
int havenport_lorde_blindproc (P_char, P_char, int, char*);
int mob_patrol(P_char, P_char, int, char *);
int undead_ghoul(P_char ch, P_char pl, int cmd, char *arg);
int undead_shadow(P_char ch, P_char pl, int cmd, char *arg);
int undead_wight(P_char ch, P_char pl, int cmd, char *arg);
int undead_ghast(P_char ch, P_char pl, int cmd, char *arg);
int undead_wraith(P_char ch, P_char pl, int cmd, char *arg);
int undead_spectre(P_char ch, P_char pl, int cmd, char *arg);
int undead_ghost(P_char ch, P_char pl, int cmd, char *arg);
int bastion_ticket_taker(P_char ch, P_char pl, int cmd, char *arg);
int dark_shade_die(P_char ch, P_char pl, int cmd, char *arg);
int standardUmberhulk(P_char ch, P_char pl, int cmd, char *arg);
int doppelganger_switch(P_char ch, P_char pl, int cmd, char *arg);
int yggdrasil_branch(P_char ch, P_char pl, int cmd, char *arg);
void yggdrasilbranch_event(P_char ch);
int treant_die(P_char, P_char, int, char *);
int phantom_steed_die(P_char, P_char, int, char *);
int earth_mephit_die(P_char, P_char, int, char *);
int air_mephit_die(P_char, P_char, int, char *);
int water_mephit_die(P_char, P_char, int, char *);
int fire_mephit_die(P_char, P_char, int, char *);

/* specs.object.c */

#ifdef CONFIG_JAIL
int jailtally(P_obj obj, P_char, int, char *);
#endif
int lathander_disc(P_obj, P_char, int, char *);
int automaton_lever(P_obj, P_char, int, char *);
int blip_portal(P_obj, P_char, int, char *);
int chess_board(P_obj, P_char, int, char *);
int creeping_doom(P_obj, P_char, int, char *);
int crystal_spike(P_obj, P_char, int, char *);
int cursed_mirror(P_obj, P_char, int, char *);
int deathtrap_detect(P_obj, P_char, int, char *);
int elfgate(P_obj, P_char, int, char *);
int ethereal_magic_pool(P_obj, P_char, int, char *);
int floating_pool(P_obj, P_char, int, char *);
int fw_ruby_monocle(P_obj, P_char, int, char *);
int holy_weapon(P_obj, P_char, int, char *);
int holy_avenger(P_obj obj, P_char ch, int cmd, char *arg);
int item_summon(P_obj, P_char, int, char *);
int item_switch(P_obj, P_char, int, char *);
int item_teleport(P_obj, P_char, int, char *);
int item_loot_block(P_obj, P_char, int, char *);
int item_block(P_obj, P_char, int, char *);
int llyms_altar(P_obj, P_char, int, char *);
int magic_pool(P_obj, P_char, int, char *);
int menden_figurine(P_obj, P_char, int, char *);
int portal_door(P_obj, P_char, int, char *);
int dim_fold(P_obj, P_char, int, char *);
int ring_elemental_control(P_obj, P_char, int, char *);
int shaman_quest_teleport(P_obj, P_char, int, char *);
int shaman_totem(P_obj, P_char, int, char *);
int skeleton_key(P_obj, P_char, int, char *);
int slot_machine(P_obj, P_char, int, char *);
int unholy_weapon(P_obj, P_char, int, char *);
int house_door(P_obj, P_char, int, char *);
int wolfsbane_potion(P_obj, P_char, int, char *);
int ItemCache(P_obj, P_char, int, char *);
int goodberry_cure(P_obj, P_char, int, char *);
void do_goodberry_cure(P_char);
int smoke_stun_shield(P_obj, P_char, int, char *);
int obj_drain(P_obj, P_char, int, char *);
int lostTotemRestorer(P_char, P_char, int, char *);
void reimbTotem(P_char);
int tiamat_crescent_moon (P_obj obj, P_char ch, int cmd, char *arg);
int proximity_explosion(P_obj, P_char, int, char *);
int sneak_boots(P_obj, P_char, int, char *);
int slot_machine_new(P_obj obj, P_char ch, int cmd, char *arg);
void write_jackpot(void); // here so that events.c sees it
void read_jackpot(int type); // here so db.c can see it

/* specs.planar.c */

int abyssForgedWeapons (P_char, P_char, int, char *);
int demon_aluFiendRegen (P_char, P_char, int, char *);
int demon_babauCorrosion (P_char, P_char, int, char *);
int demon_babauGaze (P_char, P_char, int, char *);
int demon_balorDeath (P_char, P_char, int, char *);
int demon_balorLightningSword (P_obj, P_char, int, char *);
int demon_balorWhip (P_obj, P_char, int, char *);
int demon_bar_lgura (P_char, P_char, int, char *);
int demon_cambion (P_char, P_char, int, char *);
int demon_chasmeBuzz (P_char, P_char, int, char *);
int demon_dretch (P_char, P_char, int, char *);
int demon_glabrezuGrab (P_char, P_char, int, char *);
int demon_hezrouStench (P_char, P_char, int, char *);
int demon_manesDeath (P_char, P_char, int, char *);
int demon_marilithAttack (P_char, P_char, int, char *);
int demon_marilithTail (P_char, P_char, int, char *);
int demon_molydeusHeads (P_char, P_char, int, char *);
int demon_nabassuParalysis (P_char, P_char, int, char *);
int demon_nalfeshneeRainbow (P_char, P_char, int, char *);
int demon_rutterkin (P_char, P_char, int, char *);
int demon_succubusCharm (P_char, P_char, int, char *);
int demon_succubusCharmed (P_char, P_char, int, char *);
int demon_vrockDanceOfRuin (P_char, P_char, int, char *);
int demon_vrockScreech (P_char, P_char, int, char *);
int demon_vrockSpores (P_char, P_char, int, char *);
int devil_pitFiendTail (P_char, P_char, int, char *);
int devil_pitFiendBite (P_char, P_char, int, char *);
int devilLemure (P_char, P_char, int, char *);
int devil_spinagonFlameSpike(P_char, P_char, int, char *);
int standardDemon (P_char, P_char, int, char *);
int standardDevil (P_char, P_char, int, char *);
void addGatedGroupMember (P_char, P_char, int);
void assignExtraPlanarMobiles(void);
int movanic_deva(P_char ch, P_char pl, int cmd, char *arg);

/* specs.procregistration.c */

struct func_registration_data* findProcFunction(char *);
struct func_registration_data* findProcFunctionByPtr(void *);
void listProcFunctions(char *);
void ReleaseProc(struct func_attachment *);
void ListProcs(P_char, P_char);
struct func_attachment * findAttachedChProc(P_char, char *);


/* specs.realm.c */

int cricket(P_char, P_char, int, char *);
int faerie(P_char, P_char, int, char *);
int finn(P_char, P_char, int, char *);
int tree_spirit(P_char, P_char, int, char *);

/* specs.room.c */

int GlyphOfWarding(int, P_char, int, char *);
int akh_elamshin(int, P_char, int, char *);
int automaton_trapdoor(int, P_char, int, char *);
int cage_command_block(int, P_char, int, char *);
int dump(int, P_char, int, char *);
int feed_lock(int room, P_char, int, char *);
int fw_warning_room(int, P_char, int, char *);
int kings_hall(int room, P_char, int, char *);
int pet_shops(int, P_char, int, char *);
int pray_for_items(int, P_char, int, char *);
int cart_shops (int, P_char, int, char *);
bool zoneEarthquake(P_char, int);
void zoneEarthquakeEvent(void *);
void zoneEarthquakeDamage(P_char, int);
void startEarthquakeWrapper(P_char, char *);
int newbieLoadRoom(int, P_char, int, char *);
int autoDistributor(int, P_char, int, char *);
int weight_trigger(int room, P_char ch, int cmd, char *arg);
uint total_ch_weight(P_char ch);

/*specs.ruins_yath_oloth */

int ryo_bansheeWail(P_char, P_char, int, char *);

/* specs.scornubel.c */

int sc_parchimil(P_char, P_char, int, char *);
int sc_ladyRhessajan(P_char, P_char, int, char *);
int sc_chansrin(P_char, P_char, int, char *);
int sc_angryMan(P_char, P_char, int, char *);
int sc_butler(P_char, P_char, int, char *);
int sc_maid(P_char, P_char, int, char *);
int sc_guardsman(P_char, P_char, int, char *);
int sc_mercenary(P_char, P_char, int, char *);
int sc_merchant(P_char, P_char, int, char *);
int sc_commoner(P_char, P_char, int, char *);
int sc_clerk(P_char, P_char, int, char *);
int sc_loudPeddler(P_char, P_char, int, char *);
int sc_karlyn(P_char, P_char, int, char *);
int sc_fieryMace(P_obj, P_char, int, char *);
void assignScornubelMobs(void);
void assignScornubelObjs(void);
void assignScornubelRooms(void);

/* specs.tarsellian.c */
void assignTarsellianMobiles(void);
void assignTarsellianObjects(void);
int tf_jabberwock(P_char, P_char, int, char *);
int tf_render(P_char, P_char, int, char *);
int tf_dagger_oblivion(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.trahern.c */
int weevelDeath(P_char, P_char, int, char *);
int kazgorothToss(P_char, P_char, int, char *);
int slothenEngorge(P_char, P_char, int, char *);
int gakarakQuake(P_char, P_char, int, char *);
int erinyesCharm(P_char, P_char, int, char *);
int erinyesCharmed(P_char, P_char, int, char *);
int crystalSword(P_obj, P_char, int, char *);
int obsidianSword(P_obj, P_char, int, char *);
void assignTrahernObjects(void);
void assignTrahernMobiles(void);

/* specs.thunderheadpeak.c */

int thp_necroChild(P_obj, P_char, int, char *);
int necro_passing_glyph(int, P_char, int, char *);

/* specs.distro.c */

int test_mob_distro(int, P_char, int, char *);
int mob_distro_system(P_obj obj, P_char ch, int cmd, char *arg);
int scardale_distro(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.instruments.c */

int dragonhide_drum(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.toys.c */

int banana(P_obj, P_char, int, char *);
int kor_avatar(P_obj, P_char, int, char *);
int labelas(P_obj, P_char, int, char *);
int lathander(P_obj, P_char, int, char *);
int lloth(P_obj, P_char, int, char *);
int lloth_avatar(P_obj, P_char, int, char *);
int loviatar(P_obj, P_char, int, char *);
int mask(P_obj obj, P_char, int, char *);
int mystra(P_obj, P_char, int, char *);
int nuclear_bomb(P_obj, P_char, int, char *);
int orcus_wand(P_obj obj, P_char ch, int cmd, char *arg);
int rambo(P_obj obj, P_char, int, char *);
int staff_of_oghma(P_obj, P_char, int, char *);
int tyr_sword(P_obj, P_char, int, char *);
int vaprak_claw(P_obj, P_char, int, char *);
int varon(P_obj obj, P_char, int, char *);
int altherogs_blackSunSword(P_obj obj, P_char ch, int cmd, char *args);
void altherog_cd_event(void);
int velshorn(P_obj obj, P_char, int, char *);
int cinandriel(P_obj obj, P_char, int, char *);
int caytra(P_obj obj, P_char, int, char *);
int kelly_mirror(P_obj, P_char, int, char *);
int burunga(P_obj obj, P_char ch, int cmd, char *arg);
int diinkarazan(P_obj obj, P_char ch, int cmd, char *arg);
int magius_staff(P_obj, P_char, int, char *);
int erevan(P_obj, P_char, int, char *);
int kor_only_sword(P_obj obj, P_char ch, int cmd, char *arg);
void make_head(P_char ch, P_char victim);
int shar(P_obj, P_char, int, char *);
int miaxthing(P_obj, P_char, int, char *);
int azuth(P_obj obj, P_char, int, char *);

/* specs.undermountain.c */

int callObjectSpecials(P_obj, P_char, int, char *);
int um2_qogekStaff(P_obj, P_char, int, char *);
int um2_astralForged (P_obj, P_char, int, char *);
int um2_battleSelf (int, P_char, int, char *);
int um2_beholder (P_char, P_char, int, char *);
int um2_beholderThrall (P_char, P_char, int, char *);
int um2_blackPuddingSplit (P_char, P_char, int, char *);
int um2_bookworm (P_char, P_char, int, char *);
int um2_butcherKnife (P_char, P_char, int, char *);
int um2_crystalGolemDie (P_char, P_char, int, char *);
int um2_deathsHead(P_char, P_char, int, char *);
int um2_deathsHeadSeed(P_obj, P_char, int, char *);
int um2_deathsHeadTree(P_char, P_char, int, char *);
int um2_deriahSocials (P_char, P_char, int, char *);
int um2_devilSocials (P_char, P_char, int, char *);
int um2_drowConclaveGuard (P_char, P_char, int, char *);
int um2_drowSnakeWhip (P_obj, P_char, int, char *);
int um2_explodingRoom (int, P_char, int, char *);
int um2_gargoyleDie (P_char, P_char, int, char *);
int um2_gateGuardian (P_char, P_char, int, char *);
int um2_gheriasTukCombat (P_char, P_char, int, char *);
int um2_goldenKnight (P_char, P_char, int, char *);
int um2_impSocials (P_char, P_char, int, char *);
int um2_jurisSocials (P_char, P_char, int, char *);
int um2_madMageSocials (P_char, P_char, int, char *);
int um2_malana (P_char, P_char, int, char *);
int um2_manscorpionTail (P_char, P_char, int, char *);
int um2_medusaGaze (P_char, P_char, int, char *);
int um2_murialProne (P_char, P_char, int, char *);
int um2_murialSacrifice (int, P_char, int, char *);
int um2_murialSetFree (P_char, P_char, int, char *);
int um2_oculusRoom (int, P_char, int, char *);
int um2_orchidDecay (P_obj, P_char, int, char *);
int um2_platinumKnight (P_char, P_char, int, char *);
int um2_rustMonster (P_char, P_char, int, char *);
int um2_searingRod (P_obj, P_char, int, char *);
int um2_selfShadow (P_char, P_char, int, char *);
int um2_shaTarSocials (P_char, P_char, int, char *);
int um2_shrieker (P_char, P_char, int, char *);
int um2_silverKnight (P_char, P_char, int, char *);
int um2_succubusSocials (P_char, P_char, int, char *);
int um2_talugenSocials (P_char, P_char, int, char *);
int um2_tatteredCloak (P_obj, P_char, int, char *);
int um2_torinChainLightning (P_obj, P_char, int, char *);
int um2_torinEarthquake (P_obj, P_char, int, char *);
int um2_torinGeneral (P_obj, P_char, int, char *);
int um2_troglodyteStench (P_char, P_char, int, char *);
int um2_vortexGuardian (P_char, P_char, int, char *);
int um2_whitePuddingSplit (P_char, P_char, int, char *);
int um2_wyvernTail (P_char, P_char, int, char *);
int um3_blackPudding(P_char, P_char, int, char *);
int um_animatedSword(P_char, P_char, int, char *);
int um_bladeOfPaladins(P_obj, P_char, int, char *);
int um_cambionDemon(P_char, P_char, int, char *);
int um_durnan(P_char, P_char, int, char *);
int um_elfdawnSword(P_obj, P_char, int, char *);
int um_entranceFee(P_char, P_char, int, char *);
int um_essra(P_char, P_char, int, char *);
int um_fadeDrusus(P_obj, P_char, int, char *);
int um_flameOfNorthSword(P_obj, P_char, int, char *);
int um_flyingDagger(P_char, P_char, int, char *);
int um_gambler(P_char, P_char, int, char *);
int um_goblinLeader(P_char, P_char, int, char *);
int um_hamanathuStaff(P_obj, P_char, int, char *);
int um_helmedHorror(P_char, P_char, int, char *);
int um_highDukeSword(P_obj, P_char, int, char *);
int um_ironFlindbar(P_obj, P_char, int, char *);
int um_kevlar(P_char, P_char, int, char *);
int um_korelar(P_char, P_char, int, char *);
int um_lightningSword(P_obj, P_char, int, char *);
int um_magebaneFalchion(P_obj, P_char, int, char *);
int um_malodinOne(P_char, P_char, int, char *);
int um_malodinThree(P_char, P_char, int, char *);
int um_malodinTwo(P_char, P_char, int, char *);
int um_marteloMstar(P_obj, P_char, int, char *);
int um_mezzoloth(P_char, P_char, int, char *);
int um_mhaere(P_char, P_char, int, char *);
int um_ochreJelly(P_char, P_char, int, char *);
int um_regular(P_char, P_char, int, char *);
int um_solar (P_char, P_char, int, char *);
int um_tamsil(P_char, P_char, int, char *);
int um_thorn(P_char, P_char, int, char *);
int um_undeadTrident(P_obj, P_char, int, char *);
int um_wandOfWonder(P_obj, P_char, int, char *);
int um_woundhealerScimitar(P_obj, P_char, int, char *);
int um_zombieLord(P_char, P_char, int, char *);
void assignUndermountainMobiles (void);
void assignUndermountainObjects (void);
void assignUndermountainRooms (void);
void determineMalanaString (P_char);
void undermountainDungeonNoises(void);

/* specs.weapons.c */

int kytonchain(P_obj obj, P_char, int, char *);
int kytonoffhand(P_obj obj, P_char, int, char *);
int windsong(P_obj obj, P_char, int, char *);
int longsword_rippling_flames(P_obj obj, P_char, int, char *);
int longsword_acid(P_obj obj, P_char, int, char *);
int jeweled_fang(P_obj obj, P_char, int, char *);
int longsword_black_flames(P_obj obj, P_char, int, char *);
int mielikki_scimitar(P_obj, P_char, int, char *);
int avernus(P_obj, P_char, int, char *);
int valhalla_scepter(P_obj obj, P_char ch, int cmd, char *arg);
int New_Avernus(P_obj, P_char, int, char *);
int craine_serpent(P_obj, P_char, int, char *);
int doombringer(P_obj, P_char, int, char *);
int flamberge(P_obj, P_char, int, char *);
int githyanki(P_obj, P_char, int, char *);
int githyanki2(P_obj obj, P_char ch, int cmd, char *arg);
int hammer(P_obj, P_char, int, char *);
int lightning(P_obj, P_char, int, char *);
int nightbringer(P_obj, P_char, int, char *);
int orb(P_obj, P_char, int, char *);
int rockcrusher(P_obj, P_char, int, char *);
int tahlshara(P_obj, P_char, int, char *);
int torment(P_obj, P_char, int, char *);
int tiamat_stinger(P_obj, P_char, int, char *);
int swordOfFireGiants(P_obj, P_char, int, char *);
int shadow_dagger(P_obj, P_char, int, char *);
int kirinHorn(P_obj, P_char, int, char *);
int sword_wickedly_barbed(P_obj obj, P_char ch, int cmd, char *arg);
int longsword_tanthorian(P_obj obj, P_char ch, int cmd, char *arg);
int flaming_tanthorian(P_obj obj, P_char ch, int cmd, char *arg);
int basilisk_leggings(P_obj obj, P_char ch, int cmd, char *arg);
int basilisk_snakes(P_obj obj, P_char ch, int cmd, char *arg);
int basilisk_drop(P_obj obj, P_char ch, int cmd, char *arg);
int corpse_kisser(P_obj obj, P_char ch, int cmd, char *arg);
int proc_icydagger(P_obj obj, P_char ch, int cmd, char *arg);
int moonblade_starsong(P_obj obj, P_char ch, int cmd, char *arg);
int proc_dirk_reversehit(P_obj obj, P_char ch, int cmd, char *arg);
int proc_frostbite_cold(P_obj obj, P_char ch, int cmd, char *arg);
int everchanging_longsword(P_obj obj, P_char ch, int cmd, char *arg);
int glowing_crimson_dagger(P_obj obj, P_char ch, int cmd, char *arg);
int magetower_sunblade(P_obj obj, P_char ch, int cmd, char *arg);
int valhalla_scepter(P_obj obj, P_char ch, int cmd, char *arg);
int sf_glimmering_burst(P_obj obj, P_char ch, int cmd, char *arg);
int proc_darkhold_warhammer(P_obj obj, P_char ch, int cmd, char *arg);
int proc_darkhold_bastard(P_obj obj, P_char ch, int cmd, char *arg);
int longsword_slenderelven(P_obj obj, P_char, int, char *);
int garg_test(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.waterdeep.c */

#ifdef CONFIG_JAIL
int waterdeep_witness(P_char, P_char, int, char *);
#endif
int corpse_kisser(P_obj, P_char, int, char *);
int artillery_one(P_char, P_char, int, char *);
int assassin_one(P_char, P_char, int, char *);
int baker_one(P_char, P_char, int, char *);
int baker_two(P_char, P_char, int, char *);
int bouncer_four(P_char, P_char, int, char *);
int bouncer_one(P_char, P_char, int, char *);
int bouncer_three(P_char, P_char, int, char *);
int bouncer_two(P_char, P_char, int, char *);
int brigand_one(P_char, P_char, int, char *);
int casino_four(P_char, P_char, int, char *);
int casino_one(P_char, P_char, int, char *);
int casino_three(P_char, P_char, int, char *);
int casino_two(P_char, P_char, int, char *);
int cat_one(P_char, P_char, int, char *);
int cell_drunk(P_char, P_char, int, char *);
int cleric_one(P_char, P_char, int, char *);
int clock_tower(P_obj, P_char, int, char *);
int commoner_five(P_char, P_char, int, char *);
int commoner_four(P_char, P_char, int, char *);
int commoner_one(P_char, P_char, int, char *);
int commoner_six(P_char, P_char, int, char *);
int commoner_three(P_char, P_char, int, char *);
int commoner_two(P_char, P_char, int, char *);
int crier_one(P_char, P_char, int, char *);
int dog_one(P_char, P_char, int, char *);
int dog_two(P_char, P_char, int, char *);
int drunk_one(P_char, P_char, int, char *);
int drunk_three(P_char, P_char, int, char *);
int drunk_two(P_char, P_char, int, char *);
int farmer_one(P_char, P_char, int, char *);
int fisherman_one(P_char, P_char, int, char *);
int fisherman_two(P_char, P_char, int, char *);
int frostbite(P_obj obj, P_char, int, char *);
int frulghiem(P_obj obj, P_char, int, char *);
int guard_one(P_char, P_char, int, char *);
int guard_two(P_char, P_char, int, char *);
int guild_guard_eight(P_char, P_char, int, char *);
int guild_guard_eleven(P_char, P_char, int, char *);
int guild_guard_five(P_char, P_char, int, char *);
int guild_guard_four(P_char, P_char, int, char *);

int guild_guard_nine(P_char, P_char, int, char *);
int guild_guard_one(P_char, P_char, int, char *);
int guild_guard_seven(P_char, P_char, int, char *);
int guild_guard_six(P_char, P_char, int, char *);
int guild_guard_ten(P_char, P_char, int, char *);
int guild_guard_thirteen(P_char, P_char, int, char *);
int guild_guard_three(P_char, P_char, int, char *);
int guild_guard_twelve(P_char, P_char, int, char *);
int guild_guard_two(P_char, P_char, int, char *);
int guild_protection(P_char, P_char);
int guildmaster_eight(P_char, P_char, int, char *);
int guildmaster_eleven(P_char, P_char, int, char *);
int guildmaster_five(P_char, P_char, int, char *);
int guildmaster_four(P_char, P_char, int, char *);
int guildmaster_nine(P_char, P_char, int, char *);
int guildmaster_one(P_char, P_char, int, char *);
int guildmaster_seven(P_char, P_char, int, char *);
int guildmaster_six(P_char, P_char, int, char *);
int guildmaster_ten(P_char, P_char, int, char *);
int guildmaster_three(P_char, P_char, int, char *);
int guildmaster_twelve(P_char, P_char, int, char *);
int guildmaster_two(P_char, P_char, int, char *);
int homeless_one(P_char, P_char, int, char *);
int homeless_two(P_char, P_char, int, char *);
int lighthouse_one(P_char, P_char, int, char *);
int lighthouse_two(P_char, P_char, int, char *);
int mage_one(P_char, P_char, int, char *);
int mercenary_one(P_char, P_char, int, char *);
int mercenary_three(P_char, P_char, int, char *);
int mercenary_two(P_char, P_char, int, char *);
int merchant_one(P_char, P_char, int, char *);
int merchant_two(P_char, P_char, int, char *);
int naval_four(P_char, P_char, int, char *);
int naval_one(P_char, P_char, int, char *);
int naval_three(P_char, P_char, int, char *);
int naval_two(P_char, P_char, int, char *);
int park_five(P_char, P_char, int, char *);
int park_four(P_char, P_char, int, char *);
int park_one(P_char, P_char, int, char *);
int park_six(P_char, P_char, int, char *);
int park_three(P_char, P_char, int, char *);
int park_two(P_char, P_char, int, char *);
int piergeiron(P_char, P_char, int, char *);
int piergeiron_guard(P_char, P_char, int, char *);
int prostitute_one(P_char, P_char, int, char *);
int rogue_one(P_char, P_char, int, char *);
int sailor_one(P_char, P_char, int, char *);
int seabird_one(P_char, P_char, int, char *);
int seabird_two(P_char, P_char, int, char *);
int seaman_one(P_char, P_char, int, char *);
int secret_door(P_obj, P_char, int, char *);
int selune_five(P_char, P_char, int, char *);
int selune_four(P_char, P_char, int, char *);
int selune_one(P_char, P_char, int, char *);
int selune_six(P_char, P_char, int, char *);
int selune_three(P_char, P_char, int, char *);
int selune_two(P_char, P_char, int, char *);
int shopper_one(P_char, P_char, int, char *);
int shopper_two(P_char, P_char, int, char *);
int sphere_lightning_weapon(P_obj obj, P_char, int, char *);
int tailor_one(P_char, P_char, int, char *);
int wanderer(P_char, P_char, int, char *);
int warrior_one(P_char, P_char, int, char *);
int waterdeep_guard_one(P_char, P_char, int, char *);
int waterdeep_guard_three(P_char, P_char, int, char *);
int waterdeep_guard_two(P_char, P_char, int, char *);
int waterdeep_guild_eight(int, P_char, int, char *);
int waterdeep_guild_eleven(int, P_char, int, char *);
int waterdeep_guild_five(int, P_char, int, char *);
int waterdeep_guild_four(int, P_char, int, char *);
int waterdeep_guild_nine(int, P_char, int, char *);
int waterdeep_guild_one(int, P_char, int, char *);
int waterdeep_guild_seven(int, P_char, int, char *);
int waterdeep_guild_six(int, P_char, int, char *);
int waterdeep_guild_ten(int, P_char, int, char *);
int waterdeep_guild_three(int, P_char, int, char *);
int waterdeep_guild_twelve(int, P_char, int, char *);
int waterdeep_guild_two(int, P_char, int, char *);
int waterdeep_portal(P_obj, P_char, int, char *);
int wrestler_one(P_char, P_char, int, char *);
int young_druid_one(P_char, P_char, int, char *);
int young_mercenary_one(P_char, P_char, int, char *);
int young_monk_one(P_char, P_char, int, char *);
int young_necro_one(P_char, P_char, int, char *);
int young_paladin_one(P_char, P_char, int, char *);
int youth_one(P_char, P_char, int, char *);
int youth_two(P_char, P_char, int, char *);
void waterdeep_city_noises(void);
int waterdeep_fountain_teleport(P_obj, P_char, int, char *);

/* specs.gloomhaven.c */

int gloomhaven_gate_guard(P_char, P_char, int, char *);
void gloomhaven_city_noises(void);

/* specs.zhentilkeep.c */

int zk_gate_guard(P_char, P_char, int, char *);
int zk_guild_cler_sham(int, P_char, int, char *);
int zk_guild_merc_war(int, P_char, int, char *);
int zk_little_girl(P_char, P_char, int, char *);
int zk_minstrel(P_char, P_char, int, char *);
int zk_scornubian_trader(P_char, P_char, int, char *);
int zk_terrified_merchant(P_char, P_char, int, char *);
int zk_ugly_prostitute(P_char, P_char, int, char *);
int zk_visiting_dignitary(P_char, P_char, int, char *);
void AssignZKMobiles();
void AssignZKObjects();
void AssignZKRooms();

/* specs.PC.c */

int alth_proc(P_char, P_char, int, char *);
int velshornsSocks(P_char, P_char, int, char *);
int lycanthropy_wolf(P_char, P_char, int, char *);
int lycanthropy_incubation(P_char, P_char, int, char *);
int bloodloss(P_char, P_char, int, char *);
int cold_actual(P_char ch, P_char pl, int cmd, char *args);
int cold_incubation(P_char ch, P_char pl, int cmd, char *args);
int cormyrian_incubation(P_char ch, P_char pl, int cmd, char *args);
int cormyrian1_actual(P_char ch, P_char pl, int cmd, char *args);
int cormyrian2_actual(P_char ch, P_char pl, int cmd, char *args);
int cormyrian3_actual(P_char ch, P_char pl, int cmd, char *args);
int fever_actual(P_char ch, P_char pl, int cmd, char *args);
int fever_incubation(P_char ch, P_char pl, int cmd, char *args);
int bane_actual(P_char ch, P_char pl, int cmd, char *args);
int darkdeath_actual(P_char ch, P_char pl, int cmd, char *args);
int reddeath_incubation(P_char ch, P_char pl, int cmd, char *args);
int reddeath_actual(P_char ch, P_char pl, int cmd, char *args);
int afkmonitor(P_char, P_char, int, char *);
void RemovePCDisease(P_char);
void AddPCDisease(P_char ch, int cmd);

/* specs.spiderhaunt.c */

int shw_hugeWhiteSpider(P_char, P_char, int, char *);
int shw_frailDruid(P_char, P_char, int, char *);
int shw_maggots(P_obj, P_char, int, char *);
int shw_maggotsSquirm(P_char);
int shw_cyricsAltar(P_obj, P_char, int, char *);
int shw_spiderVenomPouch(P_obj, P_char, int, char *);
void assignSpiderhauntObjects(void);
void assignSpiderhauntMobiles(void);

/* quest sphere written specs  */
int qs_contingency(P_char, P_char, int, char *);
int qs_blackmantle(P_char, P_char, int, char *);
int qs_lightninggrip(P_char, P_char, int, char *);
int qs_sleepcheck(P_char);
int qs_Monolith1(P_char, P_char, int, char *);
int qs_Monolith2(P_char, P_char, int, char *);
int qs_Monolith3(P_char, P_char, int, char *);
int qs_Monolith4(P_char, P_char, int, char *);
int qs_Monolith5(P_char, P_char, int, char *);
int qs_Monolith6(P_char, P_char, int, char *);
int qs_Monolith7(P_char, P_char, int, char *);
void assignQuestProcs (void);

/* specs.darkhold.c */

void assignDarkholdObjects (void);
void assignDarkholdMobiles (void);
int darkhold_bastardswordDetermination(P_obj, P_char, int, char *);
int darkhold_warhammerstonecleaver(P_obj, P_char, int, char *);
int musical_skull_1(P_obj, P_char, int, char *);
int musical_skull_2(P_obj, P_char, int, char *);
int fire_die(P_char, P_char, int, char *);
int air_die(P_char, P_char, int, char *);
int water_die(P_char, P_char, int , char *);
int earth_die(P_char, P_char, int, char *);
int shadow_dragon_die(P_char, P_char, int, char *);
int gold_diamond(P_obj, P_char, int, char *);
int ruby_aquamarine(P_obj, P_char, int, char *);
int shadow_fiendDarkness(P_char, P_char, int, char *);
int shadow_fiendSteal(P_char, P_char, int, char *);

/* specs.westernrealms.c */

int wr_ancientBrownie(P_char ch, P_char pl, int cmd, char *arg);


/* specs.fun.c */
int beavis(P_char ch, P_char pl, int cmd, char *arg);
int butthead(P_char ch, P_char pl, int cmd, char *arg);
int billthecat(P_char ch, P_char pl, int cmd, char *arg);

/* specs.monastery.c */
int pure_blood_90812(P_char ch, P_char pl, int cmd, char *arg);
int pure_blood_90819(P_char ch, P_char pl, int cmd, char *arg);
int pure_blood_90837(P_char ch, P_char pl, int cmd, char *arg);
int pure_blood_90866(P_char ch, P_char pl, int cmd, char *arg);
/* int monastery_give(P_char ch, P_char pl, int cmd, char*arg); */
int shadow_giant(P_char ch, P_char pl, int cmd, char *arg);

/* specs.mythdrannor.c */
int md_darken_aura(P_obj obj, P_char ch, int cmd, char *arg);
int md_gleaming_burst(P_obj obj, P_char ch, int cmd, char *arg);
int md_enchanted_khanjari(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.clouds.c */
int haste_sleeves(P_obj obj, P_char ch, int cmd, char *arg);
void haste_sleeves_event(P_char ch);

/* specs.newhaven.c */
int winged_deva(P_char ch, P_char pl, int cmd, char *arg);
int ashentoris(P_char ch, P_char pl, int cmd, char *arg);
int nh_blueplume (P_obj obj, P_char ch, int cmd, char *arg);
int nh_writhingash (P_obj obj, P_char ch, int cmd, char *arg);
int ilshazone_kamerynn(P_char ch, P_char pl, int cmd, char *arg);
int ilshazone_canthus(P_char ch, P_char pl, int cmd, char *arg);

/* specs.moonshae.c */
int cymric_hugh(P_obj, P_char, int, char *);
int ilshazone_roll_with_it(P_char ch, P_char pl, int cmd, char *arg);
int sister_knight(P_char, P_char, int, char *);
int jessica_summon_wisp(P_char ch, P_char pl, int cmd, char *arg);
int robyn_summon_wisp(P_char ch, P_char pl, int cmd, char *arg);
int robyn_summon_servant(P_char ch, P_char pl, int cmd, char *arg);
int moonshae_earthmother_staff (P_obj obj, P_char ch, int cmd, char *arg);


/* specs.muspelheim.c */
int muspel_chimney_pour(P_obj obj, P_char ch, int cmd, char *arg);
void muspel_smoke_dissipate_event(void);
int muspel_chieftain_open(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_giant_shout_m58833(P_char ch, P_char tch, int cmd, char *arg);
int muspel_giant_shout_m58708_m58709(P_char ch, P_char tch, int cmd, char *arg);
int muspel_giant_shout_m58806(P_char ch, P_char tch, int cmd, char *arg);
int muspel_bec_de_corbin(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_dragon_lance(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_spider_dagger(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_crystal_scimitar(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_recurve_bow(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_duergar_battlehammer(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_dagger_whispers(P_obj obj, P_char ch, int cmd, char *arg);
int muspel_spider_venom(P_char ch, P_char pl, int cmd, char *arg);
int muspel_lookout_shout_m58806(P_char ch, P_char tch, int cmd, char *arg);
int muspel_lookout_shout_m58708_m58709(P_char ch, P_char tch, int cmd, char *arg);
int muspel_lookout_shout_m58833(P_char ch, P_char tch, int cmd, char *arg);
int muspel_ice_river(int room, P_char ch, int cmd, char *arg);
void muspel_river_thaw(void);
void muspel_river_freeze(void);
int thorn_shield(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.hulburg.c */
int hulburg_beholder_minor (P_char ch, P_char pl, int cmd, char *arg);
int hulburg_beholder_major (P_char ch, P_char pl, int cmd, char *arg);

/* specs.halruaa.c */
int halruaa_necrostaff(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_illusionstaff(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_enchanterstaff(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_invokerstaff(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_elemstaff(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_transmuter_itemdrop(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_transmuter2(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_transmuter1(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_fleshdoll(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_small_prismatic_elem(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_crit_prismatic_elem(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_uber_prismatic_elem(P_char ch, P_char pl, int cmd, char *arg);
int halruaa_magebane(P_obj obj, P_char ch, int cmd, char *arg);
int halruaa_dwarven_hammer(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.tarrasque.c */
void assignTarrasqueMobiles(void);
void assignTarrasqueObjects(void);
int tarrasque_stomache(P_obj obj, P_char ch, int cmd, char *arg);
int tarrasque_swallow_smack(P_char ch, P_char pl, int cmd, char *arg);
int tarrasque_die(P_char ch, P_char pl, int cmd, char *arg);
int tarrasque_corpse_enter(P_obj obj, P_char ch, int cmd, char *arg);
int tarrasque_corpse_exit(P_obj obj, P_char ch, int cmd, char *arg);

// specs.newzone.c

int ttf_rot_bringer(P_char, P_char, int, char *);
int ttf_tentacles(P_char, P_char, int, char *);
int ttf_fourarms(P_char, P_char, int, char *);

int et_earthBoss(P_char, P_char, int, char *);
int et_airBoss(P_char, P_char, int, char *);
int et_waterBoss(P_char, P_char, int, char *);
int et_fireBoss(P_char, P_char, int, char *);

int murlynds_spoon(P_obj, P_char, int, char *);

int cemetary_cloakMeteors(P_obj obj, P_char ch, int cmd, char *arg);
int cemetary_lightsaber(P_obj obj, P_char ch, int cmd, char *arg);

int magma_dragon_shout(P_char ch, P_char tch, int cmd, char *arg);
int azuth_test(P_obj obj, P_char ch, int cmd, char *arg);

/* missile.c */
int hellish_fury_bow(P_obj obj, P_char ch, int cmd, char *arg);

/* seeliecourt.c */
void assignSeelieCourtMobiles(void);
void assignSeelieCourtObjects(void);
int standard_faerie_ff(P_char, P_char, int, char *);
int standard_faerie_prism(P_char, P_char, int, char *);
int faerie_search(P_char, P_char, int, char *);
int faerie_grab(P_char, P_char);
int seelie_staff(P_obj obj, P_char ch, int cmd, char *arg);
int bards_glaive(P_obj obj, P_char ch, int cmd, char *arg);
int master_bards_glaive(P_obj obj, P_char ch, int cmd, char *arg);

/* specs.bhaal.c */
void assignBhaalMobiles(void);
void assignBhaalObjects(void);
int bhaal_res(P_obj, P_char, int, char *);
int bhaal_enter(P_obj, P_char, int, char *);
void scribeAllSpells(P_char);
void loadGear(P_char, int *);
int bhaal_mage_weapon(P_obj, P_char, int, char *);
int bhaal_priest_weapon(P_obj, P_char, int, char *);
int bhaal_rogue_weapon(P_obj, P_char, int, char *);
int bhaal_warrior_weapon(P_obj, P_char, int, char *);

/* specs.erlan.c */
int erlan_spike_shield(P_obj obj, P_char ch, int cmd, char *arg);
int erlan_trackless_hunting(P_obj obj, P_char ch, int cmd, char *arg);
int erlan_winged_boots(P_obj obj, P_char ch, int cmd, char *arg);
int erlan_food_bag(P_obj obj, P_char ch, int cmd, char *arg);


/* specs.tiamat.c */
void assignTiamatMobiles(void);
void assignTiamatObjs(void);
int hand_entrapment(P_char ch, P_char pl, int cmd, char *arg);
void hand_entrapment_event(P_char ch);

/* Orcish Hall of Plunder (specs.ohp.c) */
void assignOhpMobiles(void);
void assignOhpObjects(void);
int ohp_greed_dagger(P_obj obj, P_char ch, int cmd, char *arg);
int ohp_serrated_axe(P_obj obj, P_char ch, int cmd, char *arg);

#endif /* _SOJ_SPECS_PROTOTYPES_H_ */
